{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\Sidebar.js\";\nimport React from 'react';\nimport './Sidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  setCurrentPage,\n  currentPage\n}) => {\n  const menuItems = [{\n    id: 'dashboard',\n    label: '📊 Dashboard'\n  }, {\n    id: 'connections',\n    label: '🔗 Connections'\n  }, {\n    id: 'import-series',\n    label: '📺 Import Series M3U'\n  }, {\n    id: 'import-vod',\n    label: '🎬 Import VOD M3U'\n  }, {\n    id: 'history',\n    label: '📜 History'\n  }, {\n    id: 'profile',\n    label: '👤 Profile'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar d-flex flex-column p-3 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      className: \"text-center mb-4\",\n      children: \"\\uD83C\\uDFAF RGS XUI\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"list-unstyled\",\n      children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `btn w-100 text-start ${currentPage === item.id ? 'btn-warning' : 'btn-outline-light'}`,\n          onClick: () => setCurrentPage(item.id),\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 13\n        }, this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Sidebar", "setCurrentPage", "currentPage", "menuItems", "id", "label", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "onClick", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/Sidebar.js"], "sourcesContent": ["import React from 'react';\r\nimport './Sidebar.css';\r\n\r\nconst Sidebar = ({ setCurrentPage, currentPage }) => {\r\n  const menuItems = [\r\n    { id: 'dashboard', label: '📊 Dashboard' },\r\n    { id: 'connections', label: '🔗 Connections' },\r\n    { id: 'import-series', label: '📺 Import Series M3U' },\r\n    { id: 'import-vod', label: '🎬 Import VOD M3U' },\r\n    { id: 'history', label: '📜 History' },\r\n    { id: 'profile', label: '👤 Profile' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"sidebar d-flex flex-column p-3 text-white\">\r\n      <h4 className=\"text-center mb-4\">🎯 RGS XUI</h4>\r\n      <ul className=\"list-unstyled\">\r\n        {menuItems.map(item => (\r\n          <li key={item.id} className=\"mb-2\">\r\n            <button \r\n              className={`btn w-100 text-start ${currentPage === item.id ? 'btn-warning' : 'btn-outline-light'}`}\r\n              onClick={() => setCurrentPage(item.id)}\r\n            >\r\n              {item.label}\r\n            </button>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,cAAc;EAAEC;AAAY,CAAC,KAAK;EACnD,MAAMC,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC1C;IAAED,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC9C;IAAED,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAuB,CAAC,EACtD;IAAED,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAChD;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAa,CAAC,EACtC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAa,CAAC,CACvC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAC,2CAA2C;IAAAC,QAAA,gBACxDR,OAAA;MAAIO,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChDZ,OAAA;MAAIO,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC1BJ,SAAS,CAACS,GAAG,CAACC,IAAI,iBACjBd,OAAA;QAAkBO,SAAS,EAAC,MAAM;QAAAC,QAAA,eAChCR,OAAA;UACEO,SAAS,EAAE,wBAAwBJ,WAAW,KAAKW,IAAI,CAACT,EAAE,GAAG,aAAa,GAAG,mBAAmB,EAAG;UACnGU,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAACY,IAAI,CAACT,EAAE,CAAE;UAAAG,QAAA,EAEtCM,IAAI,CAACR;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC,GANFE,IAAI,CAACT,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOZ,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACI,EAAA,GA3BIf,OAAO;AA6Bb,eAAeA,OAAO;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}