{"ast": null, "code": "import React from'react';import'./Sidebar.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sidebar=_ref=>{let{setCurrentPage,currentPage}=_ref;const menuItems=[{id:'dashboard',label:'📊 Dashboard'},{id:'connections',label:'🔗 Connections'},{id:'import-series',label:'📺 Import Series M3U'},{id:'import-vod',label:'🎬 Import VOD M3U'},{id:'history',label:'📜 History'},{id:'profile',label:'👤 Profile'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar d-flex flex-column p-3 text-white\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-center mb-4\",children:\"\\uD83C\\uDFAF RGS XUI\"}),/*#__PURE__*/_jsx(\"ul\",{className:\"list-unstyled\",children:menuItems.map(item=>/*#__PURE__*/_jsx(\"li\",{className:\"mb-2\",children:/*#__PURE__*/_jsx(\"button\",{className:`btn w-100 text-start ${currentPage===item.id?'btn-warning':'btn-outline-light'}`,onClick:()=>setCurrentPage(item.id),children:item.label})},item.id))})]});};export default Sidebar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}