import React from 'react';
import './Sidebar.css';

const Sidebar = ({ setCurrentPage, currentPage }) => {
  const menuItems = [
    { id: 'dashboard', label: '📊 Dashboard' },
    { id: 'connections', label: '🔗 Connections' },
    { id: 'import-series', label: '📺 Import Series M3U' },
    { id: 'import-vod', label: '🎬 Import VOD M3U' },
    { id: 'history', label: '📜 History' },
    { id: 'profile', label: '👤 Profile' }
  ];

  return (
    <div className="sidebar d-flex flex-column p-3 text-white">
      <h4 className="text-center mb-4">🎯 RGS XUI</h4>
      <ul className="list-unstyled">
        {menuItems.map(item => (
          <li key={item.id} className="mb-2">
            <button 
              className={`btn w-100 text-start ${currentPage === item.id ? 'btn-warning' : 'btn-outline-light'}`}
              onClick={() => setCurrentPage(item.id)}
            >
              {item.label}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Sidebar;
