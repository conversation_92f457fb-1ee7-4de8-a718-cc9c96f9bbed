import './App.css';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import ImportM3U from './components/ImportM3U';
import ImportVODM3U from './components/ImportVODM3U';
import Connections from './components/Connections';
import History from './components/History';
import Profile from './components/Profile';
import DebugPanel from './components/DebugPanel';
import { AppProvider } from './context/AppContext';
import { clearOldConfigurations } from './config/apiConfig';
import { useState, useEffect } from 'react';

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');

  // Limpiar configuraciones antiguas al cargar
  useEffect(() => {
    clearOldConfigurations();
  }, []);

  // Log de cambios de página
  useEffect(() => {
    if (window.debugLog) {
      window.debugLog(`📄 Navigation: Switched to ${currentPage}`, 'info');
    }
  }, [currentPage]);

  const renderPage = () => {
    switch(currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'connections':
        return <Connections />;
      case 'import-series':
        return <ImportM3U />;
      case 'import-vod':
        return <ImportVODM3U />;
      case 'history':
        return <History />;
      case 'profile':
        return <Profile />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <AppProvider>
      <div className="d-flex">
        <Sidebar setCurrentPage={setCurrentPage} currentPage={currentPage} />
        <div className="main-content">
          <div className="content-wrapper">
            {renderPage()}
          </div>
        </div>
        <DebugPanel />
      </div>
    </AppProvider>
  );
}

export default App;
