{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Row,Col,Card,Button,Form,Alert,ProgressBar,Table,Badge}from'react-bootstrap';import BackendStatus from'./BackendStatus';import{checkSystemHealth}from'../utils/seriesLogic';import{api,databaseAPI}from'../services/apiService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ImportVODM3U=()=>{var _fileAnalysis$basic_a,_fileAnalysis$basic_a2,_fileAnalysis$basic_a3,_fileAnalysis$basic_a4,_fileAnalysis$basic_a5,_fileAnalysis$file_in,_fileAnalysis$parse_r,_fileAnalysis$parse_r2,_fileAnalysis$parse_r3;const[selectedFile,setSelectedFile]=useState(null);const[isImporting,setIsImporting]=useState(false);const[importProgress,setImportProgress]=useState(0);const[showAlert,setShowAlert]=useState(false);const[alertMessage,setAlertMessage]=useState('');const[alertType,setAlertType]=useState('info');// Estados para backend\nconst[backendStatus,setBackendStatus]=useState('checking');const[fileAnalysis,setFileAnalysis]=useState(null);const[isProcessingFile,setIsProcessingFile]=useState(false);// Configuración fija para VOD/Movies\nconst contentType='movie';const[streamsServer,setStreamsServer]=useState('');const[sourceConfig,setSourceConfig]=useState({directSource:true,directProxy:false,loadBalancing:false});const[selectedCategories,setSelectedCategories]=useState([]);// Estados para datos dinámicos del backend\nconst[availableServers,setAvailableServers]=useState([]);const[existingCategories,setExistingCategories]=useState([]);const checkBackendStatus=async()=>{try{const health=await checkSystemHealth();setBackendStatus(health.success?'connected':'error');if(!health.success){displayAlert('warning','Backend no disponible. Funcionando en modo offline.');}}catch(error){setBackendStatus('error');displayAlert('danger','No se puede conectar al backend');}};const loadInitialData=async()=>{try{console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);// Cargar servidores y categorías desde backend si está disponible\nif(backendStatus==='connected'){console.log('✅ Backend conectado, cargando datos reales...');await loadRealServers();await loadRealCategories();}else{console.log('⚠️ Backend no conectado, usando datos mock...');// Fallback a mock data si no hay conexión\nloadMockData();}}catch(error){console.error('Error cargando datos iniciales:',error);if(window.debugLog){window.debugLog('error',`Error cargando datos iniciales: ${error.message}`);}// Fallback a mock data en caso de error\nloadMockData();}};// Cargar servidores reales desde la base de datos\nconst loadRealServers=async()=>{try{console.log('🔄 Iniciando carga de servidores reales...');const response=await fetch('http://localhost:5001/api/database/streaming-servers');console.log('📡 Respuesta del servidor:',response.status,response.statusText);const result=await response.json();console.log('📊 Datos recibidos:',result);if(result.success&&result.data){const servers=result.data.map(server=>({id:server.server_id,name:server.server_name||`Server ${server.server_id}`,ip:server.server_ip||'Unknown IP',load:`${server.total_streams||0} streams`,// Mostrar cantidad de streams como \"carga\"\ntotal_streams:server.total_streams||0,status:server.server_status===1?'Active':'Inactive'}));console.log('🖥️ Servidores mapeados:',servers);setAvailableServers(servers);console.log('✅ Estado actualizado con',servers.length,'servidores');if(window.debugLog){window.debugLog('success',`✅ Cargados ${servers.length} servidores reales desde BD`);}}else{console.error('❌ Respuesta no exitosa:',result);throw new Error(result.error||'No se pudieron cargar servidores');}}catch(error){console.error('❌ Error cargando servidores:',error);if(window.debugLog){window.debugLog('error',`❌ Error cargando servidores: ${error.message}`);}throw error;}};// Cargar categorías reales desde la base de datos\nconst loadRealCategories=async()=>{try{const result=await databaseAPI.getCategories();if(result.success&&result.data){const categories=result.data.map(cat=>({id:cat.category_id,name:cat.category_name,type:detectCategoryType(cat.category_name),// Detectar tipo basado en nombre\nparent_id:cat.parent_id}));setExistingCategories(categories);if(window.debugLog){window.debugLog('success',`✅ Cargadas ${categories.length} categorías reales desde BD`);}}else{throw new Error('No se pudieron cargar categorías');}}catch(error){console.error('Error cargando categorías:',error);if(window.debugLog){window.debugLog('error',`❌ Error cargando categorías: ${error.message}`);}throw error;}};// Detectar tipo de categoría basado en el nombre\nconst detectCategoryType=categoryName=>{const name=categoryName.toLowerCase();if(name.includes('movie')||name.includes('film')||name.includes('cinema')){return'movie';}else if(name.includes('series')||name.includes('show')||name.includes('drama')){return'series';}else if(name.includes('live')||name.includes('tv')||name.includes('channel')||name.includes('news')||name.includes('sport')){return'live';}else if(name.includes('radio')||name.includes('music')||name.includes('fm')){return'radio';}return'movie';// Default a movie para VOD si no se puede detectar\n};// Datos mock como fallback\nconst loadMockData=()=>{setAvailableServers([{id:1,name:'Main Server US',ip:'*************',load:'45%'},{id:2,name:'EU Server',ip:'*************',load:'32%'},{id:3,name:'Asia Server',ip:'*************',load:'67%'},{id:4,name:'Backup Server',ip:'*************',load:'12%'}]);setExistingCategories([{id:1,name:'Action Movies',type:'movie'},{id:2,name:'Comedy Movies',type:'movie'},{id:3,name:'Drama Movies',type:'movie'},{id:4,name:'Horror Movies',type:'movie'},{id:5,name:'Sci-Fi Movies',type:'movie'},{id:6,name:'Documentary',type:'movie'},{id:7,name:'Animation',type:'movie'},{id:8,name:'Thriller',type:'movie'},{id:9,name:'Romance',type:'movie'}]);if(window.debugLog){window.debugLog('warning','⚠️ Usando datos mock - backend no disponible');}};const showAlertMessage=function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'info';setAlertMessage(message);setAlertType(type);setShowAlert(true);setTimeout(()=>setShowAlert(false),5000);};// Helper function to show alert with better formatting\nconst displayAlert=(type,message)=>{showAlertMessage(message,type);};// File handling functions\nconst handleBrowseFiles=()=>{const fileInput=document.createElement('input');fileInput.type='file';fileInput.accept='.m3u,.m3u8';fileInput.onchange=e=>{const file=e.target.files[0];if(file){handleFileSelect({target:{files:[file]}});}};fileInput.click();};const handleAnalyzeFile=async()=>{if(!selectedFile)return;setIsProcessingFile(true);setFileAnalysis(null);try{displayAlert('info','Analizando archivo M3U...');const response=await api.m3uAPI.analyzeFile(selectedFile);if(response.success){var _response$data$basic_;setFileAnalysis(response.data);// Análisis completado - configurado para VOD/Movies\ndisplayAlert('success',`✅ Archivo analizado correctamente. Se detectaron ${((_response$data$basic_=response.data.basic_analysis)===null||_response$data$basic_===void 0?void 0:_response$data$basic_.estimated_entries)||0} entradas. Configurado para importar como películas/VOD.`);}else{throw new Error(response.error||'Error analizando archivo');}}catch(error){console.error('Error analyzing file:',error);displayAlert('danger',`❌ Error analizando archivo: ${error.message}`);}finally{setIsProcessingFile(false);}};const handleClearAnalysis=()=>{setFileAnalysis(null);setSelectedFile(null);// Content type fijo para VOD\ndisplayAlert('info','Análisis limpiado. Selecciona un nuevo archivo.');};// Verificar estado del backend al cargar\nuseEffect(()=>{checkBackendStatus();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[]);// Cargar datos cuando el backend status cambie\nuseEffect(()=>{if(backendStatus!=='checking'){loadInitialData();}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[backendStatus]);const handleFileSelect=async event=>{const file=event.target.files[0];setSelectedFile(file);setFileAnalysis(null);if(file){displayAlert('info',`Archivo seleccionado: ${file.name} (${(file.size/1024/1024).toFixed(2)} MB)`);// Solo mostrar información básica, el análisis se hace manualmente\nif(window.debugLog){window.debugLog(`📁 File selected: ${file.name}`,'info');window.debugLog(`📊 File size: ${(file.size/1024/1024).toFixed(2)} MB`,'info');}}};const handleImport=async()=>{if(!selectedFile||!streamsServer){displayAlert('warning','⚠️ Por favor completa todos los campos requeridos (archivo y servidor).');return;}if(!fileAnalysis){displayAlert('warning','⚠️ Por favor analiza el archivo antes de importar.');return;}if(window.debugLog){window.debugLog(`📥 Starting VOD import of ${selectedFile.name}`,'info');window.debugLog(`📊 File size: ${(selectedFile.size/1024/1024).toFixed(2)} MB`,'info');window.debugLog(`🎯 Content type: ${contentType}`,'info');window.debugLog(`🖥️ Target server: ${streamsServer}`,'info');}setIsImporting(true);setImportProgress(0);try{// Preparar configuración de importación para VOD\nconst importConfig={contentType,streamsServer,sourceConfig,categories:selectedCategories,tmdbEnabled:true,autoAssignCategories:true};displayAlert('info','🔍 Iniciando proceso de importación VOD...');setImportProgress(10);// Paso 1: Subir archivo\nconst formData=new FormData();formData.append('file',selectedFile);formData.append('config',JSON.stringify(importConfig));if(window.debugLog){window.debugLog('📤 Uploading VOD file to backend...','info');}// Paso 1: Analizar archivo M3U\nconst analyzeResponse=await api.m3uAPI.analyzeFile(selectedFile);setImportProgress(30);if(!analyzeResponse.success){throw new Error(analyzeResponse.error||'Error analyzing file');}displayAlert('info','🎯 Archivo subido, procesando contenido VOD...');// Paso 2: Leer contenido del archivo para parsear películas\nconst fileContent=await new Promise((resolve,reject)=>{const reader=new FileReader();reader.onload=e=>resolve(e.target.result);reader.onerror=()=>reject(new Error('Error reading file'));reader.readAsText(selectedFile);});setImportProgress(40);// Paso 3: Parsear contenido del M3U como películas/VOD\nconst parseResponse=await fetch('http://localhost:5001/api/import/parse-movies',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({m3uContent:fileContent})});const parseResult=await parseResponse.json();if(!parseResult.success){throw new Error(parseResult.error||'Error parsing VOD content');}setImportProgress(60);// Paso 4: Importar películas a la base de datos\nconst importPayload={movies:parseResult.data.movies,server_id:parseInt(streamsServer),category_id:selectedCategories.length>0?parseInt(selectedCategories[0]):null,tmdb_search:sourceConfig.tmdbEnrichment||true};const importResponse=await fetch('http://localhost:5001/api/import/movies',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(importPayload)});const importResult=await importResponse.json();// Debug: Log de la respuesta completa\nconsole.log('🔍 VOD Import Response:',importResult);if(!importResult.success){throw new Error(importResult.error||'Error importing VOD content');}// Paso 5: Finalizar importación\nsetImportProgress(100);// Mostrar estadísticas de películas (acceso seguro)\nconst stats=importResult.data||importResult;const successMessage=`✅ Importación VOD completada exitosamente!\\n📊 Estadísticas:\\n• ${stats.imported||0} elementos importados\\n• ${stats.errors||0} errores\\n• ${stats.movies_created||0} películas creadas\\n• ${stats.metadata_enriched||0} con metadata TMDB`;displayAlert('success',successMessage);if(window.debugLog){window.debugLog(`✅ VOD Import completed successfully: ${selectedFile.name}`,'success');window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`,'info');}// Limpiar estado después de importación exitosa\nsetTimeout(()=>{setSelectedFile(null);setFileAnalysis(null);// Content type fijo para VOD\nsetStreamsServer('');setSelectedCategories([]);},3000);}catch(error){console.error('VOD Import error:',error);displayAlert('danger',`❌ Error durante la importación VOD: ${error.message}`);if(window.debugLog){window.debugLog(`❌ VOD Import failed: ${error.message}`,'error');}}finally{setIsImporting(false);}};return/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%',maxWidth:'none'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-4\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-primary\",children:\"\\uD83C\\uDFAC Import VOD M3U Files\"}),/*#__PURE__*/_jsx(BackendStatus,{status:backendStatus,onRetry:checkBackendStatus})]}),showAlert&&/*#__PURE__*/_jsxs(Alert,{variant:alertType,dismissible:true,onClose:()=>setShowAlert(false),children:[/*#__PURE__*/_jsxs(Alert.Heading,{children:[alertType==='success'&&'✅ Success!',alertType==='danger'&&'❌ Error!',alertType==='warning'&&'⚠️ Warning!',alertType==='info'&&'ℹ️ Information']}),/*#__PURE__*/_jsx(\"p\",{children:alertMessage})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{lg:6,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm h-100\",children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"bg-primary text-white d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"\\uD83D\\uDCC2 File Upload\"}),backendStatus==='connected'&&/*#__PURE__*/_jsxs(Badge,{bg:\"success\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"bi bi-cloud-check\"}),\" Backend Ready\"]})]}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Select M3U File\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\".m3u,.m3u8\",onChange:handleFileSelect,disabled:isImporting}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Supported formats: .m3u, .m3u8 (Max size: 50MB)\"})]}),selectedFile&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Alert,{variant:\"info\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Selected File:\"}),\" \",selectedFile.name,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"Size:\"}),\" \",(selectedFile.size/1024/1024).toFixed(2),\" MB\"]}),isProcessingFile&&/*#__PURE__*/_jsx(Alert,{variant:\"secondary\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\"}),\"Analizando archivo...\"]})}),fileAnalysis&&!isProcessingFile&&/*#__PURE__*/_jsxs(Alert,{variant:\"success\",children:[/*#__PURE__*/_jsx(\"h6\",{children:\"\\uD83D\\uDCCA An\\xE1lisis del Archivo\"}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Lines:\"}),\" \",((_fileAnalysis$basic_a=fileAnalysis.basic_analysis)===null||_fileAnalysis$basic_a===void 0?void 0:_fileAnalysis$basic_a.total_lines)||0,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"EXTINF Entries:\"}),\" \",((_fileAnalysis$basic_a2=fileAnalysis.basic_analysis)===null||_fileAnalysis$basic_a2===void 0?void 0:_fileAnalysis$basic_a2.extinf_lines)||0,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"URL Entries:\"}),\" \",((_fileAnalysis$basic_a3=fileAnalysis.basic_analysis)===null||_fileAnalysis$basic_a3===void 0?void 0:_fileAnalysis$basic_a3.url_lines)||0]}),/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Estimated Entries:\"}),\" \",((_fileAnalysis$basic_a4=fileAnalysis.basic_analysis)===null||_fileAnalysis$basic_a4===void 0?void 0:_fileAnalysis$basic_a4.estimated_entries)||0,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"Valid M3U:\"}),\" \",(_fileAnalysis$basic_a5=fileAnalysis.basic_analysis)!==null&&_fileAnalysis$basic_a5!==void 0&&_fileAnalysis$basic_a5.has_valid_m3u_header?'✅ Yes':'❌ No',/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"File Size:\"}),\" \",((_fileAnalysis$file_in=fileAnalysis.file_info)===null||_fileAnalysis$file_in===void 0?void 0:_fileAnalysis$file_in.size_mb)||0,\" MB\"]})]}),((_fileAnalysis$parse_r=fileAnalysis.parse_results)===null||_fileAnalysis$parse_r===void 0?void 0:(_fileAnalysis$parse_r2=_fileAnalysis$parse_r.movies)===null||_fileAnalysis$parse_r2===void 0?void 0:_fileAnalysis$parse_r2.success)&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Movies Detected:\"}),\" \",((_fileAnalysis$parse_r3=fileAnalysis.parse_results.movies.data)===null||_fileAnalysis$parse_r3===void 0?void 0:_fileAnalysis$parse_r3.length)||0]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Button,{onClick:handleBrowseFiles,variant:\"primary\",disabled:isProcessingFile,children:\"Seleccionar Archivo M3U\"}),selectedFile&&!fileAnalysis&&/*#__PURE__*/_jsx(Button,{onClick:handleAnalyzeFile,variant:\"info\",className:\"ms-2\",disabled:isProcessingFile,children:isProcessingFile?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\"}),\"Analizando...\"]}):'Analizar Archivo'}),fileAnalysis&&/*#__PURE__*/_jsx(Button,{onClick:handleClearAnalysis,variant:\"outline-secondary\",className:\"ms-2\",disabled:isProcessingFile,children:\"Limpiar An\\xE1lisis\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-info\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83C\\uDFAF Tipo de Contenido:\"}),\" \\uD83C\\uDFAC Pel\\xEDculas/VOD (fijo)\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"small\",{children:\"Este importador est\\xE1 configurado espec\\xEDficamente para pel\\xEDculas y contenido VOD.\"})]})}),true&&/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-2\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"mb-0\",children:\"\\uD83D\\uDDA5\\uFE0F Target Streams Server\"}),/*#__PURE__*/_jsx(Button,{variant:\"outline-secondary\",size:\"sm\",onClick:loadRealServers,disabled:isImporting||backendStatus!=='connected',children:\"\\uD83D\\uDD04 Refresh\"})]}),/*#__PURE__*/_jsxs(Form.Select,{value:streamsServer,onChange:e=>setStreamsServer(e.target.value),disabled:isImporting,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select streams server...\"}),availableServers.map(server=>/*#__PURE__*/_jsxs(\"option\",{value:server.id,children:[server.name,\" (\",server.ip,\") - \",server.load]},server.id))]}),/*#__PURE__*/_jsxs(Form.Text,{className:\"text-muted\",children:[\"Server where VOD content will be hosted and served from.\",availableServers.length>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-success\",children:[\" \\u2705 \",availableServers.length,\" servers loaded\"]})]})]}),streamsServer&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"\\uD83D\\uDD17 Source Configuration\"}),/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\u2705 Direct Source (recommended for better performance)\",checked:sourceConfig.directSource,onChange:e=>setSourceConfig(prev=>({...prev,directSource:e.target.checked})),disabled:isImporting}),/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83D\\uDD04 Direct Proxy (for geo-restricted content)\",checked:sourceConfig.directProxy,onChange:e=>setSourceConfig(prev=>({...prev,directProxy:e.target.checked})),disabled:isImporting}),/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\u2696\\uFE0F Load Balancing (distribute across servers)\",checked:sourceConfig.loadBalancing,onChange:e=>setSourceConfig(prev=>({...prev,loadBalancing:e.target.checked})),disabled:isImporting})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"\\uD83C\\uDFF7\\uFE0F Categories Assignment\"}),/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'120px',overflowY:'auto',border:'1px solid #ddd',padding:'8px',borderRadius:'4px'},children:existingCategories.filter(cat=>cat.type==='movie'||cat.type==='live').map(category=>/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:`${category.name} (${category.type})`,checked:selectedCategories.includes(category.id),onChange:e=>{if(e.target.checked){setSelectedCategories(prev=>[...prev,category.id]);}else{setSelectedCategories(prev=>prev.filter(id=>id!==category.id));}},disabled:isImporting},category.id))}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Select existing movie categories or new ones will be created automatically\"})]})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"\\u2699\\uFE0F Import Settings\"}),/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83D\\uDD04 Auto-rename with TMDB data\",defaultChecked:true}),/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83D\\uDCC2 Auto-assign categories\",defaultChecked:true}),/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83C\\uDFAC Process movie metadata\",defaultChecked:true})]}),isImporting&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Import Progress\"}),/*#__PURE__*/_jsx(ProgressBar,{now:importProgress,label:`${importProgress}%`,variant:importProgress===100?'success':'primary',animated:importProgress<100})]}),/*#__PURE__*/_jsx(Button,{variant:\"success\",size:\"lg\",onClick:handleImport,disabled:!selectedFile||!fileAnalysis||!streamsServer||isImporting||isProcessingFile,className:\"w-100\",children:isImporting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\"}),\"Importando... \",importProgress,\"%\"]}):!selectedFile?'📁 Selecciona un archivo M3U':!fileAnalysis?'🔍 Analiza el archivo primero':!streamsServer?'⚙️ Selecciona un servidor':'🚀 Iniciar Importación VOD'})]})})]})}),/*#__PURE__*/_jsx(Col,{lg:6,children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm h-100\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-info text-white\",children:/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"\\u2139\\uFE0F VOD Import Guidelines\"})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h6\",{children:\"\\uD83D\\uDCCB Supported VOD Content:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83C\\uDFAC Movies:\"}),\" Feature films and documentaries\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83C\\uDFAD Short Films:\"}),\" Independent and festival content\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83D\\uDCFA Specials:\"}),\" TV movies and special events\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83C\\uDFAA Stand-up:\"}),\" Comedy specials and performances\"]})]}),/*#__PURE__*/_jsx(\"h6\",{children:\"\\u2699\\uFE0F Processing Features:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83C\\uDFAF TMDB Integration:\"}),\" Auto-fetch movie metadata\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83C\\uDFF7\\uFE0F Category Assignment:\"}),\" Smart movie categorization\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83D\\uDDBC\\uFE0F Poster Download:\"}),\" High-quality movie artwork\"]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\uD83D\\uDCDD Description Parsing:\"}),\" Extract movie info\"]})]}),/*#__PURE__*/_jsx(\"h6\",{children:\"\\u26A1 Performance Tips:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Files under 10MB import faster\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Use UTF-8 encoding for special characters\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Clean duplicate entries before import\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Ensure stable internet for TMDB metadata\"})]})]})]})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-secondary text-white\",children:/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0\",children:\"\\uD83D\\uDCCA Recent VOD Import Queue\"})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Table,{striped:true,hover:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"\\uD83D\\uDCC4 File\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\uD83D\\uDCC5 Queued\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\uD83D\\uDCCA Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\uD83C\\uDFAF Target\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u26A1 Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"movies_collection.m3u\"})}),/*#__PURE__*/_jsx(\"td\",{children:\"2025-07-15 16:30\"}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"badge bg-warning\",children:\"\\u23F3 Queued\"})}),/*#__PURE__*/_jsx(\"td\",{children:\"Main Server\"}),/*#__PURE__*/_jsxs(\"td\",{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-primary\",className:\"me-1\",children:\"\\u25B6\\uFE0F\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-danger\",children:\"\\u274C\"})]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"action_movies.m3u\"})}),/*#__PURE__*/_jsx(\"td\",{children:\"2025-07-15 16:25\"}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"badge bg-success\",children:\"\\u2705 Processing\"})}),/*#__PURE__*/_jsx(\"td\",{children:\"Cloud Server\"}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"outline-info\",children:\"\\uD83D\\uDCCA\"})})]})]})]})})]})})})]});};export default ImportVODM3U;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}