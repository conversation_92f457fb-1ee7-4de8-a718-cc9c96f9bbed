{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport './App.css';\nimport Sidebar from './components/Sidebar';\nimport Dashboard from './components/Dashboard';\nimport ImportM3U from './components/ImportM3U';\nimport ImportVODM3U from './components/ImportVODM3U';\nimport Connections from './components/Connections';\nimport History from './components/History';\nimport Profile from './components/Profile';\nimport DebugPanel from './components/DebugPanel';\nimport { AppProvider } from './context/AppContext';\nimport { clearOldConfigurations } from './config/apiConfig';\nimport { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('dashboard');\n\n  // Limpiar configuraciones antiguas al cargar\n  useEffect(() => {\n    clearOldConfigurations();\n  }, []);\n\n  // Log de cambios de página\n  useEffect(() => {\n    if (window.debugLog) {\n      window.debugLog(`📄 Navigation: Switched to ${currentPage}`, 'info');\n    }\n  }, [currentPage]);\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'dashboard':\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 16\n        }, this);\n      case 'connections':\n        return /*#__PURE__*/_jsxDEV(Connections, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 16\n        }, this);\n      case 'import-series':\n        return /*#__PURE__*/_jsxDEV(ImportM3U, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 16\n        }, this);\n      case 'import-vod':\n        return /*#__PURE__*/_jsxDEV(ImportVODM3U, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 16\n        }, this);\n      case 'history':\n        return /*#__PURE__*/_jsxDEV(History, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 16\n        }, this);\n      case 'profile':\n        return /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AppProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        setCurrentPage: setCurrentPage,\n        currentPage: currentPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-wrapper\",\n          children: renderPage()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DebugPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"YAvMukcl+FEVTYZefxEGrUkG+hc=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["Sidebar", "Dashboard", "ImportM3U", "ImportVODM3U", "Connections", "History", "Profile", "DebugPanel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearOldConfigurations", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "currentPage", "setCurrentPage", "window", "debugLog", "renderPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/App.js"], "sourcesContent": ["import './App.css';\nimport Sidebar from './components/Sidebar';\nimport Dashboard from './components/Dashboard';\nimport ImportM3U from './components/ImportM3U';\nimport ImportVODM3U from './components/ImportVODM3U';\nimport Connections from './components/Connections';\nimport History from './components/History';\nimport Profile from './components/Profile';\nimport DebugPanel from './components/DebugPanel';\nimport { AppProvider } from './context/AppContext';\nimport { clearOldConfigurations } from './config/apiConfig';\nimport { useState, useEffect } from 'react';\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('dashboard');\n\n  // Limpiar configuraciones antiguas al cargar\n  useEffect(() => {\n    clearOldConfigurations();\n  }, []);\n\n  // Log de cambios de página\n  useEffect(() => {\n    if (window.debugLog) {\n      window.debugLog(`📄 Navigation: Switched to ${currentPage}`, 'info');\n    }\n  }, [currentPage]);\n\n  const renderPage = () => {\n    switch(currentPage) {\n      case 'dashboard':\n        return <Dashboard />;\n      case 'connections':\n        return <Connections />;\n      case 'import-series':\n        return <ImportM3U />;\n      case 'import-vod':\n        return <ImportVODM3U />;\n      case 'history':\n        return <History />;\n      case 'profile':\n        return <Profile />;\n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <AppProvider>\n      <div className=\"d-flex\">\n        <Sidebar setCurrentPage={setCurrentPage} currentPage={currentPage} />\n        <div className=\"main-content\">\n          <div className=\"content-wrapper\">\n            {renderPage()}\n          </div>\n        </div>\n        <DebugPanel />\n      </div>\n    </AppProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAO,WAAW;AAClB,OAAOA,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,WAAW,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACdF,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAE,SAAS,CAAC,MAAM;IACd,IAAIO,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,8BAA8BH,WAAW,EAAE,EAAE,MAAM,CAAC;IACtE;EACF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAOJ,WAAW;MAChB,KAAK,WAAW;QACd,oBAAOH,OAAA,CAACZ,SAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,aAAa;QAChB,oBAAOX,OAAA,CAACT,WAAW;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxB,KAAK,eAAe;QAClB,oBAAOX,OAAA,CAACX,SAAS;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,YAAY;QACf,oBAAOX,OAAA,CAACV,YAAY;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,SAAS;QACZ,oBAAOX,OAAA,CAACR,OAAO;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpB,KAAK,SAAS;QACZ,oBAAOX,OAAA,CAACP,OAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpB;QACE,oBAAOX,OAAA,CAACZ,SAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACEX,OAAA,CAACL,WAAW;IAAAiB,QAAA,eACVZ,OAAA;MAAKa,SAAS,EAAC,QAAQ;MAAAD,QAAA,gBACrBZ,OAAA,CAACb,OAAO;QAACiB,cAAc,EAAEA,cAAe;QAACD,WAAW,EAAEA;MAAY;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrEX,OAAA;QAAKa,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3BZ,OAAA;UAAKa,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAC7BL,UAAU,CAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNX,OAAA,CAACN,UAAU;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB;AAACT,EAAA,CA/CQD,GAAG;AAAAa,EAAA,GAAHb,GAAG;AAiDZ,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}