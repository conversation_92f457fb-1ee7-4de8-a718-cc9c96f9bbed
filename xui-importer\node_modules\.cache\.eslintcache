[{"F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\index.js": "1", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\reportWebVitals.js": "2", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\App.js": "3", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\NavigationBar.js": "4", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Dashboard.js": "5", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportM3U.js": "6", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Sidebar.js": "7", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Profile.js": "8", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\History.js": "9", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Connections.js": "10", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DebugPanel.js": "11", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\SeriesImportHandler.js": "12", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\seriesLogic.js": "13", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\services\\apiService.js": "14", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\BackendStatus.js": "15", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\context\\AppContext.js": "16", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingProgress.js": "17", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingModal.js": "18", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DatabaseOptimization.js": "19", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\hooks\\useDatabaseWorker.js": "20", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\workers\\databaseWorker.js": "21", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\config\\apiConfig.js": "22", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ConnectivityDiagnostic.js": "23", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\connectivityTest.js": "24", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportVODM3U.js": "25"}, {"size": 582, "mtime": 1752616067662, "results": "26", "hashOfConfig": "27"}, {"size": 362, "mtime": 1752612862467, "results": "28", "hashOfConfig": "27"}, {"size": 1740, "mtime": 1752865637505, "results": "29", "hashOfConfig": "27"}, {"size": 878, "mtime": 1752613257084, "results": "30", "hashOfConfig": "27"}, {"size": 11095, "mtime": 1752729154482, "results": "31", "hashOfConfig": "27"}, {"size": 32128, "mtime": 1752865856438, "results": "32", "hashOfConfig": "27"}, {"size": 1065, "mtime": 1752865608272, "results": "33", "hashOfConfig": "27"}, {"size": 20767, "mtime": 1752620222163, "results": "34", "hashOfConfig": "27"}, {"size": 12297, "mtime": 1752703292672, "results": "35", "hashOfConfig": "27"}, {"size": 19905, "mtime": 1752708600731, "results": "36", "hashOfConfig": "27"}, {"size": 3561, "mtime": 1752620222163, "results": "37", "hashOfConfig": "27"}, {"size": 11480, "mtime": 1752854110462, "results": "38", "hashOfConfig": "27"}, {"size": 16014, "mtime": 1752721373152, "results": "39", "hashOfConfig": "27"}, {"size": 14296, "mtime": 1752789632883, "results": "40", "hashOfConfig": "27"}, {"size": 1617, "mtime": 1752627612546, "results": "41", "hashOfConfig": "27"}, {"size": 19465, "mtime": 1752684469144, "results": "42", "hashOfConfig": "27"}, {"size": 6900, "mtime": 1752685510779, "results": "43", "hashOfConfig": "27"}, {"size": 9257, "mtime": 1752685535428, "results": "44", "hashOfConfig": "27"}, {"size": 7557, "mtime": 1752703292686, "results": "45", "hashOfConfig": "27"}, {"size": 6043, "mtime": 1752701323984, "results": "46", "hashOfConfig": "27"}, {"size": 9869, "mtime": 1752703445206, "results": "47", "hashOfConfig": "27"}, {"size": 4534, "mtime": 1752765805111, "results": "48", "hashOfConfig": "27"}, {"size": 8830, "mtime": 1752703292740, "results": "49", "hashOfConfig": "27"}, {"size": 5494, "mtime": 1752686091588, "results": "50", "hashOfConfig": "27"}, {"size": 30710, "mtime": 1752871521185, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "z8i460", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\index.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\reportWebVitals.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\App.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\NavigationBar.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Dashboard.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportM3U.js", [], ["127", "128"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Sidebar.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Profile.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\History.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Connections.js", [], ["129"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DebugPanel.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\SeriesImportHandler.js", ["130", "131"], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\seriesLogic.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\services\\apiService.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\BackendStatus.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\context\\AppContext.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingProgress.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingModal.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DatabaseOptimization.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\hooks\\useDatabaseWorker.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\workers\\databaseWorker.js", [], ["132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\config\\apiConfig.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ConnectivityDiagnostic.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\connectivityTest.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportVODM3U.js", [], ["148", "149"], {"ruleId": "150", "severity": 1, "message": "151", "line": 261, "column": 6, "nodeType": "152", "endLine": 261, "endColumn": 8, "suggestions": "153", "suppressions": "154"}, {"ruleId": "150", "severity": 1, "message": "155", "line": 269, "column": 6, "nodeType": "152", "endLine": 269, "endColumn": 21, "suggestions": "156", "suppressions": "157"}, {"ruleId": "150", "severity": 1, "message": "158", "line": 41, "column": 6, "nodeType": "152", "endLine": 41, "endColumn": 32, "suggestions": "159", "suppressions": "160"}, {"ruleId": "161", "severity": 1, "message": "162", "line": 3, "column": 29, "nodeType": "163", "messageId": "164", "endLine": 3, "endColumn": 52}, {"ruleId": "150", "severity": 1, "message": "165", "line": 18, "column": 6, "nodeType": "152", "endLine": 18, "endColumn": 20, "suggestions": "166"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 108, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 108, "endColumn": 9, "suppressions": "170"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 114, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 114, "endColumn": 9, "suppressions": "171"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 124, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 124, "endColumn": 9, "suppressions": "172"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 135, "column": 7, "nodeType": "163", "messageId": "169", "endLine": 135, "endColumn": 11, "suppressions": "173"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 144, "column": 7, "nodeType": "163", "messageId": "169", "endLine": 144, "endColumn": 11, "suppressions": "174"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 155, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 155, "endColumn": 9, "suppressions": "175"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 177, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 177, "endColumn": 9, "suppressions": "176"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 188, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 188, "endColumn": 9, "suppressions": "177"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 213, "column": 9, "nodeType": "163", "messageId": "169", "endLine": 213, "endColumn": 13, "suppressions": "178"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 231, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 231, "endColumn": 9, "suppressions": "179"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 302, "column": 7, "nodeType": "163", "messageId": "169", "endLine": 302, "endColumn": 11, "suppressions": "180"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 333, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 333, "endColumn": 9, "suppressions": "181"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 346, "column": 5, "nodeType": "163", "messageId": "169", "endLine": 346, "endColumn": 9, "suppressions": "182"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 359, "column": 1, "nodeType": "163", "messageId": "169", "endLine": 359, "endColumn": 5, "suppressions": "183"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 367, "column": 9, "nodeType": "163", "messageId": "169", "endLine": 367, "endColumn": 13, "suppressions": "184"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 377, "column": 7, "nodeType": "163", "messageId": "169", "endLine": 377, "endColumn": 11, "suppressions": "185"}, {"ruleId": "150", "severity": 1, "message": "151", "line": 254, "column": 6, "nodeType": "152", "endLine": 254, "endColumn": 8, "suggestions": "186", "suppressions": "187"}, {"ruleId": "150", "severity": 1, "message": "155", "line": 262, "column": 6, "nodeType": "152", "endLine": 262, "endColumn": 21, "suggestions": "188", "suppressions": "189"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkBackendStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["190"], ["191"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["192"], ["193"], "React Hook useEffect has a missing dependency: 'updateConnectionStatus'. Either include it or remove the dependency array.", ["194"], ["195"], "no-unused-vars", "'validateSeriesStructure' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'analyzeM3UFile'. Either include it or remove the dependency array.", ["196"], "no-restricted-globals", "Unexpected use of 'self'.", "defaultMessage", ["197"], ["198"], ["199"], ["200"], ["201"], ["202"], ["203"], ["204"], ["205"], ["206"], ["207"], ["208"], ["209"], ["210"], ["211"], ["212"], ["213"], ["214"], ["215"], ["216"], {"desc": "217", "fix": "218"}, {"kind": "219", "justification": "220"}, {"desc": "221", "fix": "222"}, {"kind": "219", "justification": "220"}, {"desc": "223", "fix": "224"}, {"kind": "219", "justification": "220"}, {"desc": "225", "fix": "226"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"kind": "219", "justification": "220"}, {"desc": "217", "fix": "227"}, {"kind": "219", "justification": "220"}, {"desc": "221", "fix": "228"}, {"kind": "219", "justification": "220"}, "Update the dependencies array to be: [checkBackendStatus]", {"range": "229", "text": "230"}, "directive", "", "Update the dependencies array to be: [backendStatus, loadInitialData]", {"range": "231", "text": "232"}, "Update the dependencies array to be: [state.databaseConnection, updateConnectionStatus]", {"range": "233", "text": "234"}, "Update the dependencies array to be: [analyzeM3UFile, selectedFile]", {"range": "235", "text": "236"}, {"range": "237", "text": "230"}, {"range": "238", "text": "232"}, [9421, 9423], "[checkBackendStatus]", [9641, 9656], "[backendStatus, loadInitialData]", [1417, 1443], "[state.databaseConnection, updateConnectionStatus]", [864, 878], "[analyzeM3UFile, selectedFile]", [9036, 9038], [9248, 9263]]