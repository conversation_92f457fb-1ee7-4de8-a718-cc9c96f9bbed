/*! For license information please see main.612c1bb5.js.LICENSE.txt */
(()=>{var e={85:(e,n,t)=>{"use strict";var r=t(340),a=t(950),s=t(119);function l(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function o(e){var n=e,t=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do{0!==(4098&(n=e).flags)&&(t=n.return),e=n.return}while(e)}return 3===n.tag?t:null}function c(e){if(13===e.tag){var n=e.memoizedState;if(null===n&&(null!==(e=e.alternate)&&(n=e.memoizedState)),null!==n)return n.dehydrated}return null}function u(e){if(o(e)!==e)throw Error(l(188))}function d(e){var n=e.tag;if(5===n||26===n||27===n||6===n)return e;for(e=e.child;null!==e;){if(null!==(n=d(e)))return n;e=e.sibling}return null}var f=Object.assign,h=Symbol.for("react.element"),m=Symbol.for("react.transitional.element"),p=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),b=Symbol.for("react.profiler"),y=Symbol.for("react.provider"),x=Symbol.for("react.consumer"),j=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),k=Symbol.for("react.suspense_list"),E=Symbol.for("react.memo"),N=Symbol.for("react.lazy");Symbol.for("react.scope");var C=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var T=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var P=Symbol.iterator;function _(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=P&&e[P]||e["@@iterator"])?e:null}var L=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===L?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case b:return"Profiler";case v:return"StrictMode";case S:return"Suspense";case k:return"SuspenseList";case C:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case p:return"Portal";case j:return(e.displayName||"Context")+".Provider";case x:return(e._context.displayName||"Context")+".Consumer";case w:var n=e.render;return(e=e.displayName)||(e=""!==(e=n.displayName||n.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case E:return null!==(n=e.displayName||null)?n:R(e.type)||"Memo";case N:n=e._payload,e=e._init;try{return R(e(n))}catch(t){}}return null}var O=Array.isArray,z=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,A=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},I=[],M=-1;function $(e){return{current:e}}function F(e){0>M||(e.current=I[M],I[M]=null,M--)}function U(e,n){M++,I[M]=e.current,e.current=n}var B=$(null),H=$(null),V=$(null),W=$(null);function q(e,n){switch(U(V,n),U(H,e),U(B,null),n.nodeType){case 9:case 11:e=(e=n.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=n.tagName,n=n.namespaceURI)e=sd(n=ad(n),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(B),U(B,e)}function K(){F(B),F(H),F(V)}function Q(e){null!==e.memoizedState&&U(W,e);var n=B.current,t=sd(n,e.type);n!==t&&(U(H,e),U(B,t))}function G(e){H.current===e&&(F(B),F(H)),W.current===e&&(F(W),Qd._currentValue=D)}var X=Object.prototype.hasOwnProperty,Y=r.unstable_scheduleCallback,J=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,ne=r.unstable_now,te=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,se=r.unstable_NormalPriority,le=r.unstable_LowPriority,ie=r.unstable_IdlePriority,oe=r.log,ce=r.unstable_setDisableYieldValue,ue=null,de=null;function fe(e){if("function"===typeof oe&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(n){}}var he=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(me(e)/pe|0)|0},me=Math.log,pe=Math.LN2;var ge=256,ve=4194304;function be(e){var n=42&e;if(0!==n)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ye(e,n,t){var r=e.pendingLanes;if(0===r)return 0;var a=0,s=e.suspendedLanes,l=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~s)?a=be(r):0!==(l&=i)?a=be(l):t||0!==(t=i&~e)&&(a=be(t)):0!==(i=r&~s)?a=be(i):0!==l?a=be(l):t||0!==(t=r&~e)&&(a=be(t)),0===a?0:0!==n&&n!==a&&0===(n&s)&&((s=a&-a)>=(t=n&-n)||32===s&&0!==(4194048&t))?n:a}function xe(e,n){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)}function je(e,n){switch(e){case 1:case 2:case 4:case 8:case 64:return n+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;default:return-1}}function we(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function ke(e){for(var n=[],t=0;31>t;t++)n.push(e);return n}function Ee(e,n){e.pendingLanes|=n,268435456!==n&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ne(e,n,t){e.pendingLanes|=n,e.suspendedLanes&=~n;var r=31-he(n);e.entangledLanes|=n,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&t}function Ce(e,n){var t=e.entangledLanes|=n;for(e=e.entanglements;t;){var r=31-he(t),a=1<<r;a&n|e[r]&n&&(e[r]|=n),t&=~a}}function Te(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pe(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function _e(){var e=A.p;return 0!==e?e:void 0===(e=window.event)?32:uf(e.type)}var Le=Math.random().toString(36).slice(2),Re="__reactFiber$"+Le,Oe="__reactProps$"+Le,ze="__reactContainer$"+Le,Ae="__reactEvents$"+Le,De="__reactListeners$"+Le,Ie="__reactHandles$"+Le,Me="__reactResources$"+Le,$e="__reactMarker$"+Le;function Fe(e){delete e[Re],delete e[Oe],delete e[Ae],delete e[De],delete e[Ie]}function Ue(e){var n=e[Re];if(n)return n;for(var t=e.parentNode;t;){if(n=t[ze]||t[Re]){if(t=n.alternate,null!==n.child||null!==t&&null!==t.child)for(e=yd(e);null!==e;){if(t=e[Re])return t;e=yd(e)}return n}t=(e=t).parentNode}return null}function Be(e){if(e=e[Re]||e[ze]){var n=e.tag;if(5===n||6===n||13===n||26===n||27===n||3===n)return e}return null}function He(e){var n=e.tag;if(5===n||26===n||27===n||6===n)return e.stateNode;throw Error(l(33))}function Ve(e){var n=e[Me];return n||(n=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function We(e){e[$e]=!0}var qe=new Set,Ke={};function Qe(e,n){Ge(e,n),Ge(e+"Capture",n)}function Ge(e,n){for(Ke[e]=n,e=0;e<n.length;e++)qe.add(n[e])}var Xe,Ye,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},en={};function nn(e,n,t){if(a=n,X.call(en,a)||!X.call(Ze,a)&&(Je.test(a)?en[a]=!0:(Ze[a]=!0,0)))if(null===t)e.removeAttribute(n);else{switch(typeof t){case"undefined":case"function":case"symbol":return void e.removeAttribute(n);case"boolean":var r=n.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(n)}e.setAttribute(n,""+t)}var a}function tn(e,n,t){if(null===t)e.removeAttribute(n);else{switch(typeof t){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttribute(n,""+t)}}function rn(e,n,t,r){if(null===r)e.removeAttribute(t);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttributeNS(n,t,""+r)}}function an(e){if(void 0===Xe)try{throw Error()}catch(t){var n=t.stack.trim().match(/\n( *(at )?)/);Xe=n&&n[1]||"",Ye=-1<t.stack.indexOf("\n    at")?" (<anonymous>)":-1<t.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xe+e+Ye}var sn=!1;function ln(e,n){if(!e||sn)return"";sn=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(n){var t=function(){throw Error()};if(Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(a){var r=a}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){r=s}e.call(t.prototype)}}else{try{throw Error()}catch(l){r=l}(t=e())&&"function"===typeof t.catch&&t.catch(function(){})}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=r.DetermineComponentFrameRoot(),l=s[0],i=s[1];if(l&&i){var o=l.split("\n"),c=i.split("\n");for(a=r=0;r<o.length&&!o[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===o.length||a===c.length)for(r=o.length-1,a=c.length-1;1<=r&&0<=a&&o[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(o[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||o[r]!==c[a]){var u="\n"+o[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{sn=!1,Error.prepareStackTrace=t}return(t=e?e.displayName||e.name:"")?an(t):""}function on(e){switch(e.tag){case 26:case 27:case 5:return an(e.type);case 16:return an("Lazy");case 13:return an("Suspense");case 19:return an("SuspenseList");case 0:case 15:return ln(e.type,!1);case 11:return ln(e.type.render,!1);case 1:return ln(e.type,!0);case 31:return an("Activity");default:return""}}function cn(e){try{var n="";do{n+=on(e),e=e.return}while(e);return n}catch(t){return"\nError generating stack: "+t.message+"\n"+t.stack}}function un(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function dn(e){var n=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===n||"radio"===n)}function fn(e){e._valueTracker||(e._valueTracker=function(e){var n=dn(e)?"checked":"value",t=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),r=""+e[n];if(!e.hasOwnProperty(n)&&"undefined"!==typeof t&&"function"===typeof t.get&&"function"===typeof t.set){var a=t.get,s=t.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,s.call(this,e)}}),Object.defineProperty(e,n,{enumerable:t.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}(e))}function hn(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var t=n.getValue(),r="";return e&&(r=dn(e)?e.checked?"true":"false":e.value),(e=r)!==t&&(n.setValue(e),!0)}function mn(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(n){return e.body}}var pn=/[\n"\\]/g;function gn(e){return e.replace(pn,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function vn(e,n,t,r,a,s,l,i){e.name="",null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l?e.type=l:e.removeAttribute("type"),null!=n?"number"===l?(0===n&&""===e.value||e.value!=n)&&(e.value=""+un(n)):e.value!==""+un(n)&&(e.value=""+un(n)):"submit"!==l&&"reset"!==l||e.removeAttribute("value"),null!=n?yn(e,l,un(n)):null!=t?yn(e,l,un(t)):null!=r&&e.removeAttribute("value"),null==a&&null!=s&&(e.defaultChecked=!!s),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.name=""+un(i):e.removeAttribute("name")}function bn(e,n,t,r,a,s,l,i){if(null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s&&(e.type=s),null!=n||null!=t){if(!("submit"!==s&&"reset"!==s||void 0!==n&&null!==n))return;t=null!=t?""+un(t):"",n=null!=n?""+un(n):t,i||n===e.value||(e.value=n),e.defaultValue=n}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.name=l)}function yn(e,n,t){"number"===n&&mn(e.ownerDocument)===e||e.defaultValue===""+t||(e.defaultValue=""+t)}function xn(e,n,t,r){if(e=e.options,n){n={};for(var a=0;a<t.length;a++)n["$"+t[a]]=!0;for(t=0;t<e.length;t++)a=n.hasOwnProperty("$"+e[t].value),e[t].selected!==a&&(e[t].selected=a),a&&r&&(e[t].defaultSelected=!0)}else{for(t=""+un(t),n=null,a=0;a<e.length;a++){if(e[a].value===t)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==n||e[a].disabled||(n=e[a])}null!==n&&(n.selected=!0)}}function jn(e,n,t){null==n||((n=""+un(n))!==e.value&&(e.value=n),null!=t)?e.defaultValue=null!=t?""+un(t):"":e.defaultValue!==n&&(e.defaultValue=n)}function wn(e,n,t,r){if(null==n){if(null!=r){if(null!=t)throw Error(l(92));if(O(r)){if(1<r.length)throw Error(l(93));r=r[0]}t=r}null==t&&(t=""),n=t}t=un(n),e.defaultValue=t,(r=e.textContent)===t&&""!==r&&null!==r&&(e.value=r)}function Sn(e,n){if(n){var t=e.firstChild;if(t&&t===e.lastChild&&3===t.nodeType)return void(t.nodeValue=n)}e.textContent=n}var kn=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function En(e,n,t){var r=0===n.indexOf("--");null==t||"boolean"===typeof t||""===t?r?e.setProperty(n,""):"float"===n?e.cssFloat="":e[n]="":r?e.setProperty(n,t):"number"!==typeof t||0===t||kn.has(n)?"float"===n?e.cssFloat=t:e[n]=(""+t).trim():e[n]=t+"px"}function Nn(e,n,t){if(null!=n&&"object"!==typeof n)throw Error(l(62));if(e=e.style,null!=t){for(var r in t)!t.hasOwnProperty(r)||null!=n&&n.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in n)r=n[a],n.hasOwnProperty(a)&&t[a]!==r&&En(e,a,r)}else for(var s in n)n.hasOwnProperty(s)&&En(e,s,n[s])}function Cn(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tn=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pn=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _n(e){return Pn.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ln=null;function Rn(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var On=null,zn=null;function An(e){var n=Be(e);if(n&&(e=n.stateNode)){var t=e[Oe]||null;e:switch(e=n.stateNode,n.type){case"input":if(vn(e,t.value,t.defaultValue,t.defaultValue,t.checked,t.defaultChecked,t.type,t.name),n=t.name,"radio"===t.type&&null!=n){for(t=e;t.parentNode;)t=t.parentNode;for(t=t.querySelectorAll('input[name="'+gn(""+n)+'"][type="radio"]'),n=0;n<t.length;n++){var r=t[n];if(r!==e&&r.form===e.form){var a=r[Oe]||null;if(!a)throw Error(l(90));vn(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(n=0;n<t.length;n++)(r=t[n]).form===e.form&&hn(r)}break e;case"textarea":jn(e,t.value,t.defaultValue);break e;case"select":null!=(n=t.value)&&xn(e,!!t.multiple,n,!1)}}}var Dn=!1;function In(e,n,t){if(Dn)return e(n,t);Dn=!0;try{return e(n)}finally{if(Dn=!1,(null!==On||null!==zn)&&(Uc(),On&&(n=On,e=zn,zn=On=null,An(n),e)))for(n=0;n<e.length;n++)An(e[n])}}function Mn(e,n){var t=e.stateNode;if(null===t)return null;var r=t[Oe]||null;if(null===r)return null;t=r[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(t&&"function"!==typeof t)throw Error(l(231,n,typeof t));return t}var $n=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Fn=!1;if($n)try{var Un={};Object.defineProperty(Un,"passive",{get:function(){Fn=!0}}),window.addEventListener("test",Un,Un),window.removeEventListener("test",Un,Un)}catch(zf){Fn=!1}var Bn=null,Hn=null,Vn=null;function Wn(){if(Vn)return Vn;var e,n,t=Hn,r=t.length,a="value"in Bn?Bn.value:Bn.textContent,s=a.length;for(e=0;e<r&&t[e]===a[e];e++);var l=r-e;for(n=1;n<=l&&t[r-n]===a[s-n];n++);return Vn=a.slice(e,1<n?1-n:void 0)}function qn(e){var n=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===n&&(e=13):e=n,10===e&&(e=13),32<=e||13===e?e:0}function Kn(){return!0}function Qn(){return!1}function Gn(e){function n(n,t,r,a,s){for(var l in this._reactName=n,this._targetInst=r,this.type=t,this.nativeEvent=a,this.target=s,this.currentTarget=null,e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Kn:Qn,this.isPropagationStopped=Qn,this}return f(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Kn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Kn)},persist:function(){},isPersistent:Kn}),n}var Xn,Yn,Jn,Zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},et=Gn(Zn),nt=f({},Zn,{view:0,detail:0}),tt=Gn(nt),rt=f({},nt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mt,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jn&&(Jn&&"mousemove"===e.type?(Xn=e.screenX-Jn.screenX,Yn=e.screenY-Jn.screenY):Yn=Xn=0,Jn=e),Xn)},movementY:function(e){return"movementY"in e?e.movementY:Yn}}),at=Gn(rt),st=Gn(f({},rt,{dataTransfer:0})),lt=Gn(f({},nt,{relatedTarget:0})),it=Gn(f({},Zn,{animationName:0,elapsedTime:0,pseudoElement:0})),ot=Gn(f({},Zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),ct=Gn(f({},Zn,{data:0})),ut={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dt={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ft={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ht(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):!!(e=ft[e])&&!!n[e]}function mt(){return ht}var pt=Gn(f({},nt,{key:function(e){if(e.key){var n=ut[e.key]||e.key;if("Unidentified"!==n)return n}return"keypress"===e.type?13===(e=qn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dt[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mt,charCode:function(e){return"keypress"===e.type?qn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?qn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gt=Gn(f({},rt,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vt=Gn(f({},nt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mt})),bt=Gn(f({},Zn,{propertyName:0,elapsedTime:0,pseudoElement:0})),yt=Gn(f({},rt,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),xt=Gn(f({},Zn,{newState:0,oldState:0})),jt=[9,13,27,32],wt=$n&&"CompositionEvent"in window,St=null;$n&&"documentMode"in document&&(St=document.documentMode);var kt=$n&&"TextEvent"in window&&!St,Et=$n&&(!wt||St&&8<St&&11>=St),Nt=String.fromCharCode(32),Ct=!1;function Tt(e,n){switch(e){case"keyup":return-1!==jt.indexOf(n.keyCode);case"keydown":return 229!==n.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pt(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var _t=!1;var Lt={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rt(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===n?!!Lt[e.type]:"textarea"===n}function Ot(e,n,t,r){On?zn?zn.push(r):zn=[r]:On=r,0<(n=Vu(n,"onChange")).length&&(t=new et("onChange","change",null,t,r),e.push({event:t,listeners:n}))}var zt=null,At=null;function Dt(e){Du(e,0)}function It(e){if(hn(He(e)))return e}function Mt(e,n){if("change"===e)return n}var $t=!1;if($n){var Ft;if($n){var Ut="oninput"in document;if(!Ut){var Bt=document.createElement("div");Bt.setAttribute("oninput","return;"),Ut="function"===typeof Bt.oninput}Ft=Ut}else Ft=!1;$t=Ft&&(!document.documentMode||9<document.documentMode)}function Ht(){zt&&(zt.detachEvent("onpropertychange",Vt),At=zt=null)}function Vt(e){if("value"===e.propertyName&&It(At)){var n=[];Ot(n,At,e,Rn(e)),In(Dt,n)}}function Wt(e,n,t){"focusin"===e?(Ht(),At=t,(zt=n).attachEvent("onpropertychange",Vt)):"focusout"===e&&Ht()}function qt(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return It(At)}function Kt(e,n){if("click"===e)return It(n)}function Qt(e,n){if("input"===e||"change"===e)return It(n)}var Gt="function"===typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e===1/n)||e!==e&&n!==n};function Xt(e,n){if(Gt(e,n))return!0;if("object"!==typeof e||null===e||"object"!==typeof n||null===n)return!1;var t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(r=0;r<t.length;r++){var a=t[r];if(!X.call(n,a)||!Gt(e[a],n[a]))return!1}return!0}function Yt(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jt(e,n){var t,r=Yt(e);for(e=0;r;){if(3===r.nodeType){if(t=e+r.textContent.length,e<=n&&t>=n)return{node:r,offset:n-e};e=t}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Yt(r)}}function Zt(e,n){return!(!e||!n)&&(e===n||(!e||3!==e.nodeType)&&(n&&3===n.nodeType?Zt(e,n.parentNode):"contains"in e?e.contains(n):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(n))))}function er(e){for(var n=mn((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);n instanceof e.HTMLIFrameElement;){try{var t="string"===typeof n.contentWindow.location.href}catch(r){t=!1}if(!t)break;n=mn((e=n.contentWindow).document)}return n}function nr(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&("input"===n&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===n||"true"===e.contentEditable)}var tr=$n&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,sr=null,lr=!1;function ir(e,n,t){var r=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;lr||null==rr||rr!==mn(r)||("selectionStart"in(r=rr)&&nr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},sr&&Xt(sr,r)||(sr=r,0<(r=Vu(ar,"onSelect")).length&&(n=new et("onSelect","select",null,n,t),e.push({event:n,listeners:r}),n.target=rr)))}function or(e,n){var t={};return t[e.toLowerCase()]=n.toLowerCase(),t["Webkit"+e]="webkit"+n,t["Moz"+e]="moz"+n,t}var cr={animationend:or("Animation","AnimationEnd"),animationiteration:or("Animation","AnimationIteration"),animationstart:or("Animation","AnimationStart"),transitionrun:or("Transition","TransitionRun"),transitionstart:or("Transition","TransitionStart"),transitioncancel:or("Transition","TransitionCancel"),transitionend:or("Transition","TransitionEnd")},ur={},dr={};function fr(e){if(ur[e])return ur[e];if(!cr[e])return e;var n,t=cr[e];for(n in t)if(t.hasOwnProperty(n)&&n in dr)return ur[e]=t[n];return e}$n&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var hr=fr("animationend"),mr=fr("animationiteration"),pr=fr("animationstart"),gr=fr("transitionrun"),vr=fr("transitionstart"),br=fr("transitioncancel"),yr=fr("transitionend"),xr=new Map,jr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function wr(e,n){xr.set(e,n),Qe(n,[e])}jr.push("scrollEnd");var Sr=new WeakMap;function kr(e,n){if("object"===typeof e&&null!==e){var t=Sr.get(e);return void 0!==t?t:(n={value:e,source:n,stack:cn(n)},Sr.set(e,n),n)}return{value:e,source:n,stack:cn(n)}}var Er=[],Nr=0,Cr=0;function Tr(){for(var e=Nr,n=Cr=Nr=0;n<e;){var t=Er[n];Er[n++]=null;var r=Er[n];Er[n++]=null;var a=Er[n];Er[n++]=null;var s=Er[n];if(Er[n++]=null,null!==r&&null!==a){var l=r.pending;null===l?a.next=a:(a.next=l.next,l.next=a),r.pending=a}0!==s&&Rr(t,a,s)}}function Pr(e,n,t,r){Er[Nr++]=e,Er[Nr++]=n,Er[Nr++]=t,Er[Nr++]=r,Cr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function _r(e,n,t,r){return Pr(e,n,t,r),Or(e)}function Lr(e,n){return Pr(e,null,null,n),Or(e)}function Rr(e,n,t){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t);for(var a=!1,s=e.return;null!==s;)s.childLanes|=t,null!==(r=s.alternate)&&(r.childLanes|=t),22===s.tag&&(null===(e=s.stateNode)||1&e._visibility||(a=!0)),e=s,s=s.return;return 3===e.tag?(s=e.stateNode,a&&null!==n&&(a=31-he(t),null===(r=(e=s.hiddenUpdates)[a])?e[a]=[n]:r.push(n),n.lane=536870912|t),s):null}function Or(e){if(50<Rc)throw Rc=0,Oc=null,Error(l(185));for(var n=e.return;null!==n;)n=(e=n).return;return 3===e.tag?e.stateNode:null}var zr={};function Ar(e,n,t,r){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,n,t,r){return new Ar(e,n,t,r)}function Ir(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,n){var t=e.alternate;return null===t?((t=Dr(e.tag,n,e.key,e.mode)).elementType=e.elementType,t.type=e.type,t.stateNode=e.stateNode,t.alternate=e,e.alternate=t):(t.pendingProps=n,t.type=e.type,t.flags=0,t.subtreeFlags=0,t.deletions=null),t.flags=65011712&e.flags,t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,n=e.dependencies,t.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext},t.sibling=e.sibling,t.index=e.index,t.ref=e.ref,t.refCleanup=e.refCleanup,t}function $r(e,n){e.flags&=65011714;var t=e.alternate;return null===t?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,n=t.dependencies,e.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Fr(e,n,t,r,a,s){var i=0;if(r=e,"function"===typeof e)Ir(e)&&(i=1);else if("string"===typeof e)i=function(e,n,t){if(1===t||null!=n.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof n.precedence||"string"!==typeof n.href||""===n.href)break;return!0;case"link":if("string"!==typeof n.rel||"string"!==typeof n.href||""===n.href||n.onLoad||n.onError)break;return"stylesheet"!==n.rel||(e=n.disabled,"string"===typeof n.precedence&&null==e);case"script":if(n.async&&"function"!==typeof n.async&&"symbol"!==typeof n.async&&!n.onLoad&&!n.onError&&n.src&&"string"===typeof n.src)return!0}return!1}(e,t,B.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case C:return(e=Dr(31,t,n,a)).elementType=C,e.lanes=s,e;case g:return Ur(t.children,a,s,n);case v:i=8,a|=24;break;case b:return(e=Dr(12,t,n,2|a)).elementType=b,e.lanes=s,e;case S:return(e=Dr(13,t,n,a)).elementType=S,e.lanes=s,e;case k:return(e=Dr(19,t,n,a)).elementType=k,e.lanes=s,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case y:case j:i=10;break e;case x:i=9;break e;case w:i=11;break e;case E:i=14;break e;case N:i=16,r=null;break e}i=29,t=Error(l(130,null===e?"null":typeof e,"")),r=null}return(n=Dr(i,t,n,a)).elementType=e,n.type=r,n.lanes=s,n}function Ur(e,n,t,r){return(e=Dr(7,e,r,n)).lanes=t,e}function Br(e,n,t){return(e=Dr(6,e,null,n)).lanes=t,e}function Hr(e,n,t){return(n=Dr(4,null!==e.children?e.children:[],e.key,n)).lanes=t,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}var Vr=[],Wr=0,qr=null,Kr=0,Qr=[],Gr=0,Xr=null,Yr=1,Jr="";function Zr(e,n){Vr[Wr++]=Kr,Vr[Wr++]=qr,qr=e,Kr=n}function ea(e,n,t){Qr[Gr++]=Yr,Qr[Gr++]=Jr,Qr[Gr++]=Xr,Xr=e;var r=Yr;e=Jr;var a=32-he(r)-1;r&=~(1<<a),t+=1;var s=32-he(n)+a;if(30<s){var l=a-a%5;s=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Yr=1<<32-he(n)+a|t<<a|r,Jr=s+e}else Yr=1<<s|t<<a|r,Jr=e}function na(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function ta(e){for(;e===qr;)qr=Vr[--Wr],Vr[Wr]=null,Kr=Vr[--Wr],Vr[Wr]=null;for(;e===Xr;)Xr=Qr[--Gr],Qr[Gr]=null,Jr=Qr[--Gr],Qr[Gr]=null,Yr=Qr[--Gr],Qr[Gr]=null}var ra=null,aa=null,sa=!1,la=null,ia=!1,oa=Error(l(519));function ca(e){throw pa(kr(Error(l(418,"")),e)),oa}function ua(e){var n=e.stateNode,t=e.type,r=e.memoizedProps;switch(n[Re]=e,n[Oe]=r,t){case"dialog":Iu("cancel",n),Iu("close",n);break;case"iframe":case"object":case"embed":Iu("load",n);break;case"video":case"audio":for(t=0;t<zu.length;t++)Iu(zu[t],n);break;case"source":Iu("error",n);break;case"img":case"image":case"link":Iu("error",n),Iu("load",n);break;case"details":Iu("toggle",n);break;case"input":Iu("invalid",n),bn(n,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),fn(n);break;case"select":Iu("invalid",n);break;case"textarea":Iu("invalid",n),wn(n,r.value,r.defaultValue,r.children),fn(n)}"string"!==typeof(t=r.children)&&"number"!==typeof t&&"bigint"!==typeof t||n.textContent===""+t||!0===r.suppressHydrationWarning||Xu(n.textContent,t)?(null!=r.popover&&(Iu("beforetoggle",n),Iu("toggle",n)),null!=r.onScroll&&Iu("scroll",n),null!=r.onScrollEnd&&Iu("scrollend",n),null!=r.onClick&&(n.onclick=Yu),n=!0):n=!1,n||ca(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(ia=!1);case 27:case 3:return void(ia=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!sa)return da(e),sa=!0,!1;var n,t=e.tag;if((n=3!==t&&27!==t)&&((n=5===t)&&(n=!("form"!==(n=e.type)&&"button"!==n)||ld(e.type,e.memoizedProps)),n=!n),n&&aa&&ca(e),da(e),13===t){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType)if("/$"===(n=e.data)){if(0===t){aa=vd(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++;e=e.nextSibling}aa=null}}else 27===t?(t=aa,hd(e.type)?(e=bd,bd=null,aa=e):aa=t):aa=ra?vd(e.stateNode.nextSibling):null;return!0}function ha(){aa=ra=null,sa=!1}function ma(){var e=la;return null!==e&&(null===yc?yc=e:yc.push.apply(yc,e),la=null),e}function pa(e){null===la?la=[e]:la.push(e)}var ga=$(null),va=null,ba=null;function ya(e,n,t){U(ga,n._currentValue),n._currentValue=t}function xa(e){e._currentValue=ga.current,F(ga)}function ja(e,n,t){for(;null!==e;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,null!==r&&(r.childLanes|=n)):null!==r&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===t)break;e=e.return}}function wa(e,n,t,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var s=a.dependencies;if(null!==s){var i=a.child;s=s.firstContext;e:for(;null!==s;){var o=s;s=a;for(var c=0;c<n.length;c++)if(o.context===n[c]){s.lanes|=t,null!==(o=s.alternate)&&(o.lanes|=t),ja(s.return,t,e),r||(i=null);break e}s=o.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(l(341));i.lanes|=t,null!==(s=i.alternate)&&(s.lanes|=t),ja(i,t,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function Sa(e,n,t,r){e=null;for(var a=n,s=!1;null!==a;){if(!s)if(0!==(524288&a.flags))s=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(l(387));if(null!==(i=i.memoizedProps)){var o=a.type;Gt(a.pendingProps.value,i.value)||(null!==e?e.push(o):e=[o])}}else if(a===W.current){if(null===(i=a.alternate))throw Error(l(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Qd):e=[Qd])}a=a.return}null!==e&&wa(n,e,t,r),n.flags|=262144}function ka(e){for(e=e.firstContext;null!==e;){if(!Gt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ea(e){va=e,ba=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Na(e){return Ta(va,e)}function Ca(e,n){return null===va&&Ea(e),Ta(e,n)}function Ta(e,n){var t=n._currentValue;if(n={context:n,memoizedValue:t,next:null},null===ba){if(null===e)throw Error(l(308));ba=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else ba=ba.next=n;return t}var Pa="undefined"!==typeof AbortController?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(n,t){e.push(t)}};this.abort=function(){n.aborted=!0,e.forEach(function(e){return e()})}},_a=r.unstable_scheduleCallback,La=r.unstable_NormalPriority,Ra={$$typeof:j,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Oa(){return{controller:new Pa,data:new Map,refCount:0}}function za(e){e.refCount--,0===e.refCount&&_a(La,function(){e.controller.abort()})}var Aa=null,Da=0,Ia=0,Ma=null;function $a(){if(0===--Da&&null!==Aa){null!==Ma&&(Ma.status="fulfilled");var e=Aa;Aa=null,Ia=0,Ma=null;for(var n=0;n<e.length;n++)(0,e[n])()}}var Fa=z.S;z.S=function(e,n){"object"===typeof n&&null!==n&&"function"===typeof n.then&&function(e,n){if(null===Aa){var t=Aa=[];Da=0,Ia=Pu(),Ma={status:"pending",value:void 0,then:function(e){t.push(e)}}}Da++,n.then($a,$a)}(0,n),null!==Fa&&Fa(e,n)};var Ua=$(null);function Ba(){var e=Ua.current;return null!==e?e:rc.pooledCache}function Ha(e,n){U(Ua,null===n?Ua.current:n.pool)}function Va(){var e=Ba();return null===e?null:{parent:Ra._currentValue,pool:e}}var Wa=Error(l(460)),qa=Error(l(474)),Ka=Error(l(542)),Qa={then:function(){}};function Ga(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Xa(){}function Ya(e,n,t){switch(void 0===(t=e[t])?e.push(n):t!==n&&(n.then(Xa,Xa),n=t),n.status){case"fulfilled":return n.value;case"rejected":throw es(e=n.reason),e;default:if("string"===typeof n.status)n.then(Xa,Xa);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(l(482));(e=n).status="pending",e.then(function(e){if("pending"===n.status){var t=n;t.status="fulfilled",t.value=e}},function(e){if("pending"===n.status){var t=n;t.status="rejected",t.reason=e}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw es(e=n.reason),e}throw Ja=n,Wa}}var Ja=null;function Za(){if(null===Ja)throw Error(l(459));var e=Ja;return Ja=null,e}function es(e){if(e===Wa||e===Ka)throw Error(l(483))}var ns=!1;function ts(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rs(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function as(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ss(e,n,t){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&tc)){var a=r.pending;return null===a?n.next=n:(n.next=a.next,a.next=n),r.pending=n,n=Or(e),Rr(e,null,t),n}return Pr(e,r,n,t),Or(e)}function ls(e,n,t){if(null!==(n=n.updateQueue)&&(n=n.shared,0!==(4194048&t))){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,Ce(e,t)}}function is(e,n){var t=e.updateQueue,r=e.alternate;if(null!==r&&t===(r=r.updateQueue)){var a=null,s=null;if(null!==(t=t.firstBaseUpdate)){do{var l={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};null===s?a=s=l:s=s.next=l,t=t.next}while(null!==t);null===s?a=s=n:s=s.next=n}else a=s=n;return t={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:s,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=t)}null===(e=t.lastBaseUpdate)?t.firstBaseUpdate=n:e.next=n,t.lastBaseUpdate=n}var os=!1;function cs(){if(os){if(null!==Ma)throw Ma}}function us(e,n,t,r){os=!1;var a=e.updateQueue;ns=!1;var s=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var o=i,c=o.next;o.next=null,null===l?s=c:l.next=c,l=o;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==l&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=o))}if(null!==s){var d=a.baseState;for(l=0,u=c=o=null,i=s;;){var h=-536870913&i.lane,m=h!==i.lane;if(m?(sc&h)===h:(r&h)===h){0!==h&&h===Ia&&(os=!0),null!==u&&(u=u.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var p=e,g=i;h=n;var v=t;switch(g.tag){case 1:if("function"===typeof(p=g.payload)){d=p.call(v,d,h);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(h="function"===typeof(p=g.payload)?p.call(v,d,h):p)||void 0===h)break e;d=f({},d,h);break e;case 2:ns=!0}}null!==(h=i.callback)&&(e.flags|=64,m&&(e.flags|=8192),null===(m=a.callbacks)?a.callbacks=[h]:m.push(h))}else m={lane:h,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=m,o=d):u=u.next=m,l|=h;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(m=i).next,m.next=null,a.lastBaseUpdate=m,a.shared.pending=null}}null===u&&(o=d),a.baseState=o,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null===s&&(a.shared.lanes=0),hc|=l,e.lanes=l,e.memoizedState=d}}function ds(e,n){if("function"!==typeof e)throw Error(l(191,e));e.call(n)}function fs(e,n){var t=e.callbacks;if(null!==t)for(e.callbacks=null,e=0;e<t.length;e++)ds(t[e],n)}var hs=$(null),ms=$(0);function ps(e,n){U(ms,e=dc),U(hs,n),dc=e|n.baseLanes}function gs(){U(ms,dc),U(hs,hs.current)}function vs(){dc=ms.current,F(hs),F(ms)}var bs=0,ys=null,xs=null,js=null,ws=!1,Ss=!1,ks=!1,Es=0,Ns=0,Cs=null,Ts=0;function Ps(){throw Error(l(321))}function _s(e,n){if(null===n)return!1;for(var t=0;t<n.length&&t<e.length;t++)if(!Gt(e[t],n[t]))return!1;return!0}function Ls(e,n,t,r,a,s){return bs=s,ys=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,z.H=null===e||null===e.memoizedState?Wl:ql,ks=!1,s=t(r,a),ks=!1,Ss&&(s=Os(n,t,r,a)),Rs(e),s}function Rs(e){z.H=Vl;var n=null!==xs&&null!==xs.next;if(bs=0,js=xs=ys=null,ws=!1,Ns=0,Cs=null,n)throw Error(l(300));null===e||Ni||null!==(e=e.dependencies)&&ka(e)&&(Ni=!0)}function Os(e,n,t,r){ys=e;var a=0;do{if(Ss&&(Cs=null),Ns=0,Ss=!1,25<=a)throw Error(l(301));if(a+=1,js=xs=null,null!=e.updateQueue){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,null!=s.memoCache&&(s.memoCache.index=0)}z.H=Kl,s=n(t,r)}while(Ss);return s}function zs(){var e=z.H,n=e.useState()[0];return n="function"===typeof n.then?Fs(n):n,e=e.useState()[0],(null!==xs?xs.memoizedState:null)!==e&&(ys.flags|=1024),n}function As(){var e=0!==Es;return Es=0,e}function Ds(e,n,t){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~t}function Is(e){if(ws){for(e=e.memoizedState;null!==e;){var n=e.queue;null!==n&&(n.pending=null),e=e.next}ws=!1}bs=0,js=xs=ys=null,Ss=!1,Ns=Es=0,Cs=null}function Ms(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===js?ys.memoizedState=js=e:js=js.next=e,js}function $s(){if(null===xs){var e=ys.alternate;e=null!==e?e.memoizedState:null}else e=xs.next;var n=null===js?ys.memoizedState:js.next;if(null!==n)js=n,xs=e;else{if(null===e){if(null===ys.alternate)throw Error(l(467));throw Error(l(310))}e={memoizedState:(xs=e).memoizedState,baseState:xs.baseState,baseQueue:xs.baseQueue,queue:xs.queue,next:null},null===js?ys.memoizedState=js=e:js=js.next=e}return js}function Fs(e){var n=Ns;return Ns+=1,null===Cs&&(Cs=[]),e=Ya(Cs,e,n),n=ys,null===(null===js?n.memoizedState:js.next)&&(n=n.alternate,z.H=null===n||null===n.memoizedState?Wl:ql),e}function Us(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Fs(e);if(e.$$typeof===j)return Na(e)}throw Error(l(438,String(e)))}function Bs(e){var n=null,t=ys.updateQueue;if(null!==t&&(n=t.memoCache),null==n){var r=ys.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(n={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==n&&(n={data:[],index:0}),null===t&&(t={lastEffect:null,events:null,stores:null,memoCache:null},ys.updateQueue=t),t.memoCache=n,void 0===(t=n.data[n.index]))for(t=n.data[n.index]=Array(e),r=0;r<e;r++)t[r]=T;return n.index++,t}function Hs(e,n){return"function"===typeof n?n(e):n}function Vs(e){return Ws($s(),xs,e)}function Ws(e,n,t){var r=e.queue;if(null===r)throw Error(l(311));r.lastRenderedReducer=t;var a=e.baseQueue,s=r.pending;if(null!==s){if(null!==a){var i=a.next;a.next=s.next,s.next=i}n.baseQueue=a=s,r.pending=null}if(s=e.baseState,null===a)e.memoizedState=s;else{var o=i=null,c=null,u=n=a.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(sc&f)===f:(bs&f)===f){var h=u.revertLane;if(0===h)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===Ia&&(d=!0);else{if((bs&h)===h){u=u.next,h===Ia&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(o=c=f,i=s):c=c.next=f,ys.lanes|=h,hc|=h}f=u.action,ks&&t(s,f),s=u.hasEagerState?u.eagerState:t(s,f)}else h={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(o=c=h,i=s):c=c.next=h,ys.lanes|=f,hc|=f;u=u.next}while(null!==u&&u!==n);if(null===c?i=s:c.next=o,!Gt(s,e.memoizedState)&&(Ni=!0,d&&null!==(t=Ma)))throw t;e.memoizedState=s,e.baseState=i,e.baseQueue=c,r.lastRenderedState=s}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function qs(e){var n=$s(),t=n.queue;if(null===t)throw Error(l(311));t.lastRenderedReducer=e;var r=t.dispatch,a=t.pending,s=n.memoizedState;if(null!==a){t.pending=null;var i=a=a.next;do{s=e(s,i.action),i=i.next}while(i!==a);Gt(s,n.memoizedState)||(Ni=!0),n.memoizedState=s,null===n.baseQueue&&(n.baseState=s),t.lastRenderedState=s}return[s,r]}function Ks(e,n,t){var r=ys,a=$s(),s=sa;if(s){if(void 0===t)throw Error(l(407));t=t()}else t=n();var i=!Gt((xs||a).memoizedState,t);if(i&&(a.memoizedState=t,Ni=!0),a=a.queue,gl(2048,8,Xs.bind(null,r,a,e),[e]),a.getSnapshot!==n||i||null!==js&&1&js.memoizedState.tag){if(r.flags|=2048,hl(9,{destroy:void 0,resource:void 0},Gs.bind(null,r,a,t,n),null),null===rc)throw Error(l(349));s||0!==(124&bs)||Qs(r,n,t)}return t}function Qs(e,n,t){e.flags|=16384,e={getSnapshot:n,value:t},null===(n=ys.updateQueue)?(n={lastEffect:null,events:null,stores:null,memoCache:null},ys.updateQueue=n,n.stores=[e]):null===(t=n.stores)?n.stores=[e]:t.push(e)}function Gs(e,n,t,r){n.value=t,n.getSnapshot=r,Ys(n)&&Js(e)}function Xs(e,n,t){return t(function(){Ys(n)&&Js(e)})}function Ys(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!Gt(e,t)}catch(r){return!0}}function Js(e){var n=Lr(e,2);null!==n&&Dc(n,e,2)}function Zs(e){var n=Ms();if("function"===typeof e){var t=e;if(e=t(),ks){fe(!0);try{t()}finally{fe(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hs,lastRenderedState:e},n}function el(e,n,t,r){return e.baseState=t,Ws(e,xs,"function"===typeof r?r:Hs)}function nl(e,n,t,r,a){if(Ul(e))throw Error(l(485));if(null!==(e=n.action)){var s={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){s.listeners.push(e)}};null!==z.T?t(!0):s.isTransition=!1,r(s),null===(t=n.pending)?(s.next=n.pending=s,tl(n,s)):(s.next=t.next,n.pending=t.next=s)}}function tl(e,n){var t=n.action,r=n.payload,a=e.state;if(n.isTransition){var s=z.T,l={};z.T=l;try{var i=t(a,r),o=z.S;null!==o&&o(l,i),rl(e,n,i)}catch(c){sl(e,n,c)}finally{z.T=s}}else try{rl(e,n,s=t(a,r))}catch(u){sl(e,n,u)}}function rl(e,n,t){null!==t&&"object"===typeof t&&"function"===typeof t.then?t.then(function(t){al(e,n,t)},function(t){return sl(e,n,t)}):al(e,n,t)}function al(e,n,t){n.status="fulfilled",n.value=t,ll(n),e.state=t,null!==(n=e.pending)&&((t=n.next)===n?e.pending=null:(t=t.next,n.next=t,tl(e,t)))}function sl(e,n,t){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{n.status="rejected",n.reason=t,ll(n),n=n.next}while(n!==r)}e.action=null}function ll(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function il(e,n){return n}function ol(e,n){if(sa){var t=rc.formState;if(null!==t){e:{var r=ys;if(sa){if(aa){n:{for(var a=aa,s=ia;8!==a.nodeType;){if(!s){a=null;break n}if(null===(a=vd(a.nextSibling))){a=null;break n}}a="F!"===(s=a.data)||"F"===s?a:null}if(a){aa=vd(a.nextSibling),r="F!"===a.data;break e}}ca(r)}r=!1}r&&(n=t[0])}}return(t=Ms()).memoizedState=t.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:n},t.queue=r,t=Ml.bind(null,ys,r),r.dispatch=t,r=Zs(!1),s=Fl.bind(null,ys,!1,r.queue),a={state:n,dispatch:null,action:e,pending:null},(r=Ms()).queue=a,t=nl.bind(null,ys,a,s,t),a.dispatch=t,r.memoizedState=e,[n,t,!1]}function cl(e){return ul($s(),xs,e)}function ul(e,n,t){if(n=Ws(e,n,il)[0],e=Vs(Hs)[0],"object"===typeof n&&null!==n&&"function"===typeof n.then)try{var r=Fs(n)}catch(l){if(l===Wa)throw Ka;throw l}else r=n;var a=(n=$s()).queue,s=a.dispatch;return t!==n.memoizedState&&(ys.flags|=2048,hl(9,{destroy:void 0,resource:void 0},dl.bind(null,a,t),null)),[r,s,e]}function dl(e,n){e.action=n}function fl(e){var n=$s(),t=xs;if(null!==t)return ul(n,t,e);$s(),n=n.memoizedState;var r=(t=$s()).queue.dispatch;return t.memoizedState=e,[n,r,!1]}function hl(e,n,t,r){return e={tag:e,create:t,deps:r,inst:n,next:null},null===(n=ys.updateQueue)&&(n={lastEffect:null,events:null,stores:null,memoCache:null},ys.updateQueue=n),null===(t=n.lastEffect)?n.lastEffect=e.next=e:(r=t.next,t.next=e,e.next=r,n.lastEffect=e),e}function ml(){return $s().memoizedState}function pl(e,n,t,r){var a=Ms();r=void 0===r?null:r,ys.flags|=e,a.memoizedState=hl(1|n,{destroy:void 0,resource:void 0},t,r)}function gl(e,n,t,r){var a=$s();r=void 0===r?null:r;var s=a.memoizedState.inst;null!==xs&&null!==r&&_s(r,xs.memoizedState.deps)?a.memoizedState=hl(n,s,t,r):(ys.flags|=e,a.memoizedState=hl(1|n,s,t,r))}function vl(e,n){pl(8390656,8,e,n)}function bl(e,n){gl(2048,8,e,n)}function yl(e,n){return gl(4,2,e,n)}function xl(e,n){return gl(4,4,e,n)}function jl(e,n){if("function"===typeof n){e=e();var t=n(e);return function(){"function"===typeof t?t():n(null)}}if(null!==n&&void 0!==n)return e=e(),n.current=e,function(){n.current=null}}function wl(e,n,t){t=null!==t&&void 0!==t?t.concat([e]):null,gl(4,4,jl.bind(null,n,e),t)}function Sl(){}function kl(e,n){var t=$s();n=void 0===n?null:n;var r=t.memoizedState;return null!==n&&_s(n,r[1])?r[0]:(t.memoizedState=[e,n],e)}function El(e,n){var t=$s();n=void 0===n?null:n;var r=t.memoizedState;if(null!==n&&_s(n,r[1]))return r[0];if(r=e(),ks){fe(!0);try{e()}finally{fe(!1)}}return t.memoizedState=[r,n],r}function Nl(e,n,t){return void 0===t||0!==(1073741824&bs)?e.memoizedState=n:(e.memoizedState=t,e=Ac(),ys.lanes|=e,hc|=e,t)}function Cl(e,n,t,r){return Gt(t,n)?t:null!==hs.current?(e=Nl(e,t,r),Gt(e,n)||(Ni=!0),e):0===(42&bs)?(Ni=!0,e.memoizedState=t):(e=Ac(),ys.lanes|=e,hc|=e,n)}function Tl(e,n,t,r,a){var s=A.p;A.p=0!==s&&8>s?s:8;var l=z.T,i={};z.T=i,Fl(e,!1,n,t);try{var o=a(),c=z.S;if(null!==c&&c(i,o),null!==o&&"object"===typeof o&&"function"===typeof o.then)$l(e,n,function(e,n){var t=[],r={status:"pending",value:null,reason:null,then:function(e){t.push(e)}};return e.then(function(){r.status="fulfilled",r.value=n;for(var e=0;e<t.length;e++)(0,t[e])(n)},function(e){for(r.status="rejected",r.reason=e,e=0;e<t.length;e++)(0,t[e])(void 0)}),r}(o,r),zc());else $l(e,n,r,zc())}catch(u){$l(e,n,{then:function(){},status:"rejected",reason:u},zc())}finally{A.p=s,z.T=l}}function Pl(){}function _l(e,n,t,r){if(5!==e.tag)throw Error(l(476));var a=Ll(e).queue;Tl(e,a,n,D,null===t?Pl:function(){return Rl(e),t(r)})}function Ll(e){var n=e.memoizedState;if(null!==n)return n;var t={};return(n={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hs,lastRenderedState:D},next:null}).next={memoizedState:t,baseState:t,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hs,lastRenderedState:t},next:null},e.memoizedState=n,null!==(e=e.alternate)&&(e.memoizedState=n),n}function Rl(e){$l(e,Ll(e).next.queue,{},zc())}function Ol(){return Na(Qd)}function zl(){return $s().memoizedState}function Al(){return $s().memoizedState}function Dl(e){for(var n=e.return;null!==n;){switch(n.tag){case 24:case 3:var t=zc(),r=ss(n,e=as(t),t);return null!==r&&(Dc(r,n,t),ls(r,n,t)),n={cache:Oa()},void(e.payload=n)}n=n.return}}function Il(e,n,t){var r=zc();t={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},Ul(e)?Bl(n,t):null!==(t=_r(e,n,t,r))&&(Dc(t,e,r),Hl(t,n,r))}function Ml(e,n,t){$l(e,n,t,zc())}function $l(e,n,t,r){var a={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(Ul(e))Bl(n,a);else{var s=e.alternate;if(0===e.lanes&&(null===s||0===s.lanes)&&null!==(s=n.lastRenderedReducer))try{var l=n.lastRenderedState,i=s(l,t);if(a.hasEagerState=!0,a.eagerState=i,Gt(i,l))return Pr(e,n,a,0),null===rc&&Tr(),!1}catch(o){}if(null!==(t=_r(e,n,a,r)))return Dc(t,e,r),Hl(t,n,r),!0}return!1}function Fl(e,n,t,r){if(r={lane:2,revertLane:Pu(),action:r,hasEagerState:!1,eagerState:null,next:null},Ul(e)){if(n)throw Error(l(479))}else null!==(n=_r(e,t,r,2))&&Dc(n,e,2)}function Ul(e){var n=e.alternate;return e===ys||null!==n&&n===ys}function Bl(e,n){Ss=ws=!0;var t=e.pending;null===t?n.next=n:(n.next=t.next,t.next=n),e.pending=n}function Hl(e,n,t){if(0!==(4194048&t)){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,Ce(e,t)}}var Vl={readContext:Na,use:Us,useCallback:Ps,useContext:Ps,useEffect:Ps,useImperativeHandle:Ps,useLayoutEffect:Ps,useInsertionEffect:Ps,useMemo:Ps,useReducer:Ps,useRef:Ps,useState:Ps,useDebugValue:Ps,useDeferredValue:Ps,useTransition:Ps,useSyncExternalStore:Ps,useId:Ps,useHostTransitionStatus:Ps,useFormState:Ps,useActionState:Ps,useOptimistic:Ps,useMemoCache:Ps,useCacheRefresh:Ps},Wl={readContext:Na,use:Us,useCallback:function(e,n){return Ms().memoizedState=[e,void 0===n?null:n],e},useContext:Na,useEffect:vl,useImperativeHandle:function(e,n,t){t=null!==t&&void 0!==t?t.concat([e]):null,pl(4194308,4,jl.bind(null,n,e),t)},useLayoutEffect:function(e,n){return pl(4194308,4,e,n)},useInsertionEffect:function(e,n){pl(4,2,e,n)},useMemo:function(e,n){var t=Ms();n=void 0===n?null:n;var r=e();if(ks){fe(!0);try{e()}finally{fe(!1)}}return t.memoizedState=[r,n],r},useReducer:function(e,n,t){var r=Ms();if(void 0!==t){var a=t(n);if(ks){fe(!0);try{t(n)}finally{fe(!1)}}}else a=n;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Il.bind(null,ys,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ms().memoizedState=e},useState:function(e){var n=(e=Zs(e)).queue,t=Ml.bind(null,ys,n);return n.dispatch=t,[e.memoizedState,t]},useDebugValue:Sl,useDeferredValue:function(e,n){return Nl(Ms(),e,n)},useTransition:function(){var e=Zs(!1);return e=Tl.bind(null,ys,e.queue,!0,!1),Ms().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,t){var r=ys,a=Ms();if(sa){if(void 0===t)throw Error(l(407));t=t()}else{if(t=n(),null===rc)throw Error(l(349));0!==(124&sc)||Qs(r,n,t)}a.memoizedState=t;var s={value:t,getSnapshot:n};return a.queue=s,vl(Xs.bind(null,r,s,e),[e]),r.flags|=2048,hl(9,{destroy:void 0,resource:void 0},Gs.bind(null,r,s,t,n),null),t},useId:function(){var e=Ms(),n=rc.identifierPrefix;if(sa){var t=Jr;n="\xab"+n+"R"+(t=(Yr&~(1<<32-he(Yr)-1)).toString(32)+t),0<(t=Es++)&&(n+="H"+t.toString(32)),n+="\xbb"}else n="\xab"+n+"r"+(t=Ts++).toString(32)+"\xbb";return e.memoizedState=n},useHostTransitionStatus:Ol,useFormState:ol,useActionState:ol,useOptimistic:function(e){var n=Ms();n.memoizedState=n.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=t,n=Fl.bind(null,ys,!0,t),t.dispatch=n,[e,n]},useMemoCache:Bs,useCacheRefresh:function(){return Ms().memoizedState=Dl.bind(null,ys)}},ql={readContext:Na,use:Us,useCallback:kl,useContext:Na,useEffect:bl,useImperativeHandle:wl,useInsertionEffect:yl,useLayoutEffect:xl,useMemo:El,useReducer:Vs,useRef:ml,useState:function(){return Vs(Hs)},useDebugValue:Sl,useDeferredValue:function(e,n){return Cl($s(),xs.memoizedState,e,n)},useTransition:function(){var e=Vs(Hs)[0],n=$s().memoizedState;return["boolean"===typeof e?e:Fs(e),n]},useSyncExternalStore:Ks,useId:zl,useHostTransitionStatus:Ol,useFormState:cl,useActionState:cl,useOptimistic:function(e,n){return el($s(),0,e,n)},useMemoCache:Bs,useCacheRefresh:Al},Kl={readContext:Na,use:Us,useCallback:kl,useContext:Na,useEffect:bl,useImperativeHandle:wl,useInsertionEffect:yl,useLayoutEffect:xl,useMemo:El,useReducer:qs,useRef:ml,useState:function(){return qs(Hs)},useDebugValue:Sl,useDeferredValue:function(e,n){var t=$s();return null===xs?Nl(t,e,n):Cl(t,xs.memoizedState,e,n)},useTransition:function(){var e=qs(Hs)[0],n=$s().memoizedState;return["boolean"===typeof e?e:Fs(e),n]},useSyncExternalStore:Ks,useId:zl,useHostTransitionStatus:Ol,useFormState:fl,useActionState:fl,useOptimistic:function(e,n){var t=$s();return null!==xs?el(t,0,e,n):(t.baseState=e,[e,t.queue.dispatch])},useMemoCache:Bs,useCacheRefresh:Al},Ql=null,Gl=0;function Xl(e){var n=Gl;return Gl+=1,null===Ql&&(Ql=[]),Ya(Ql,e,n)}function Yl(e,n){n=n.props.ref,e.ref=void 0!==n?n:null}function Jl(e,n){if(n.$$typeof===h)throw Error(l(525));throw e=Object.prototype.toString.call(n),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function Zl(e){return(0,e._init)(e._payload)}function ei(e){function n(n,t){if(e){var r=n.deletions;null===r?(n.deletions=[t],n.flags|=16):r.push(t)}}function t(t,r){if(!e)return null;for(;null!==r;)n(t,r),r=r.sibling;return null}function r(e){for(var n=new Map;null!==e;)null!==e.key?n.set(e.key,e):n.set(e.index,e),e=e.sibling;return n}function a(e,n){return(e=Mr(e,n)).index=0,e.sibling=null,e}function s(n,t,r){return n.index=r,e?null!==(r=n.alternate)?(r=r.index)<t?(n.flags|=67108866,t):r:(n.flags|=67108866,t):(n.flags|=1048576,t)}function i(n){return e&&null===n.alternate&&(n.flags|=67108866),n}function o(e,n,t,r){return null===n||6!==n.tag?((n=Br(t,e.mode,r)).return=e,n):((n=a(n,t)).return=e,n)}function c(e,n,t,r){var s=t.type;return s===g?d(e,n,t.props.children,r,t.key):null!==n&&(n.elementType===s||"object"===typeof s&&null!==s&&s.$$typeof===N&&Zl(s)===n.type)?(Yl(n=a(n,t.props),t),n.return=e,n):(Yl(n=Fr(t.type,t.key,t.props,null,e.mode,r),t),n.return=e,n)}function u(e,n,t,r){return null===n||4!==n.tag||n.stateNode.containerInfo!==t.containerInfo||n.stateNode.implementation!==t.implementation?((n=Hr(t,e.mode,r)).return=e,n):((n=a(n,t.children||[])).return=e,n)}function d(e,n,t,r,s){return null===n||7!==n.tag?((n=Ur(t,e.mode,r,s)).return=e,n):((n=a(n,t)).return=e,n)}function f(e,n,t){if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return(n=Br(""+n,e.mode,t)).return=e,n;if("object"===typeof n&&null!==n){switch(n.$$typeof){case m:return Yl(t=Fr(n.type,n.key,n.props,null,e.mode,t),n),t.return=e,t;case p:return(n=Hr(n,e.mode,t)).return=e,n;case N:return f(e,n=(0,n._init)(n._payload),t)}if(O(n)||_(n))return(n=Ur(n,e.mode,t,null)).return=e,n;if("function"===typeof n.then)return f(e,Xl(n),t);if(n.$$typeof===j)return f(e,Ca(e,n),t);Jl(e,n)}return null}function h(e,n,t,r){var a=null!==n?n.key:null;if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return null!==a?null:o(e,n,""+t,r);if("object"===typeof t&&null!==t){switch(t.$$typeof){case m:return t.key===a?c(e,n,t,r):null;case p:return t.key===a?u(e,n,t,r):null;case N:return h(e,n,t=(a=t._init)(t._payload),r)}if(O(t)||_(t))return null!==a?null:d(e,n,t,r,null);if("function"===typeof t.then)return h(e,n,Xl(t),r);if(t.$$typeof===j)return h(e,n,Ca(e,t),r);Jl(e,t)}return null}function v(e,n,t,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return o(n,e=e.get(t)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case m:return c(n,e=e.get(null===r.key?t:r.key)||null,r,a);case p:return u(n,e=e.get(null===r.key?t:r.key)||null,r,a);case N:return v(e,n,t,r=(0,r._init)(r._payload),a)}if(O(r)||_(r))return d(n,e=e.get(t)||null,r,a,null);if("function"===typeof r.then)return v(e,n,t,Xl(r),a);if(r.$$typeof===j)return v(e,n,t,Ca(n,r),a);Jl(n,r)}return null}function b(o,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case m:e:{for(var y=u.key;null!==c;){if(c.key===y){if((y=u.type)===g){if(7===c.tag){t(o,c.sibling),(d=a(c,u.props.children)).return=o,o=d;break e}}else if(c.elementType===y||"object"===typeof y&&null!==y&&y.$$typeof===N&&Zl(y)===c.type){t(o,c.sibling),Yl(d=a(c,u.props),u),d.return=o,o=d;break e}t(o,c);break}n(o,c),c=c.sibling}u.type===g?((d=Ur(u.props.children,o.mode,d,u.key)).return=o,o=d):(Yl(d=Fr(u.type,u.key,u.props,null,o.mode,d),u),d.return=o,o=d)}return i(o);case p:e:{for(y=u.key;null!==c;){if(c.key===y){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){t(o,c.sibling),(d=a(c,u.children||[])).return=o,o=d;break e}t(o,c);break}n(o,c),c=c.sibling}(d=Hr(u,o.mode,d)).return=o,o=d}return i(o);case N:return b(o,c,u=(y=u._init)(u._payload),d)}if(O(u))return function(a,l,i,o){for(var c=null,u=null,d=l,m=l=0,p=null;null!==d&&m<i.length;m++){d.index>m?(p=d,d=null):p=d.sibling;var g=h(a,d,i[m],o);if(null===g){null===d&&(d=p);break}e&&d&&null===g.alternate&&n(a,d),l=s(g,l,m),null===u?c=g:u.sibling=g,u=g,d=p}if(m===i.length)return t(a,d),sa&&Zr(a,m),c;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],o))&&(l=s(d,l,m),null===u?c=d:u.sibling=d,u=d);return sa&&Zr(a,m),c}for(d=r(d);m<i.length;m++)null!==(p=v(d,a,m,i[m],o))&&(e&&null!==p.alternate&&d.delete(null===p.key?m:p.key),l=s(p,l,m),null===u?c=p:u.sibling=p,u=p);return e&&d.forEach(function(e){return n(a,e)}),sa&&Zr(a,m),c}(o,c,u,d);if(_(u)){if("function"!==typeof(y=_(u)))throw Error(l(150));return function(a,i,o,c){if(null==o)throw Error(l(151));for(var u=null,d=null,m=i,p=i=0,g=null,b=o.next();null!==m&&!b.done;p++,b=o.next()){m.index>p?(g=m,m=null):g=m.sibling;var y=h(a,m,b.value,c);if(null===y){null===m&&(m=g);break}e&&m&&null===y.alternate&&n(a,m),i=s(y,i,p),null===d?u=y:d.sibling=y,d=y,m=g}if(b.done)return t(a,m),sa&&Zr(a,p),u;if(null===m){for(;!b.done;p++,b=o.next())null!==(b=f(a,b.value,c))&&(i=s(b,i,p),null===d?u=b:d.sibling=b,d=b);return sa&&Zr(a,p),u}for(m=r(m);!b.done;p++,b=o.next())null!==(b=v(m,a,p,b.value,c))&&(e&&null!==b.alternate&&m.delete(null===b.key?p:b.key),i=s(b,i,p),null===d?u=b:d.sibling=b,d=b);return e&&m.forEach(function(e){return n(a,e)}),sa&&Zr(a,p),u}(o,c,u=y.call(u),d)}if("function"===typeof u.then)return b(o,c,Xl(u),d);if(u.$$typeof===j)return b(o,c,Ca(o,u),d);Jl(o,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(t(o,c.sibling),(d=a(c,u)).return=o,o=d):(t(o,c),(d=Br(u,o.mode,d)).return=o,o=d),i(o)):t(o,c)}return function(e,n,t,r){try{Gl=0;var a=b(e,n,t,r);return Ql=null,a}catch(l){if(l===Wa||l===Ka)throw l;var s=Dr(29,l,null,e.mode);return s.lanes=r,s.return=e,s}}}var ni=ei(!0),ti=ei(!1),ri=$(null),ai=null;function si(e){var n=e.alternate;U(ci,1&ci.current),U(ri,e),null===ai&&(null===n||null!==hs.current||null!==n.memoizedState)&&(ai=e)}function li(e){if(22===e.tag){if(U(ci,ci.current),U(ri,e),null===ai){var n=e.alternate;null!==n&&null!==n.memoizedState&&(ai=e)}}else ii()}function ii(){U(ci,ci.current),U(ri,ri.current)}function oi(e){F(ri),ai===e&&(ai=null),F(ci)}var ci=$(0);function ui(e){for(var n=e;null!==n;){if(13===n.tag){var t=n.memoizedState;if(null!==t&&(null===(t=t.dehydrated)||"$?"===t.data||gd(t)))return n}else if(19===n.tag&&void 0!==n.memoizedProps.revealOrder){if(0!==(128&n.flags))return n}else if(null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function di(e,n,t,r){t=null===(t=t(r,n=e.memoizedState))||void 0===t?n:f({},n,t),e.memoizedState=t,0===e.lanes&&(e.updateQueue.baseState=t)}var fi={enqueueSetState:function(e,n,t){e=e._reactInternals;var r=zc(),a=as(r);a.payload=n,void 0!==t&&null!==t&&(a.callback=t),null!==(n=ss(e,a,r))&&(Dc(n,e,r),ls(n,e,r))},enqueueReplaceState:function(e,n,t){e=e._reactInternals;var r=zc(),a=as(r);a.tag=1,a.payload=n,void 0!==t&&null!==t&&(a.callback=t),null!==(n=ss(e,a,r))&&(Dc(n,e,r),ls(n,e,r))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var t=zc(),r=as(t);r.tag=2,void 0!==n&&null!==n&&(r.callback=n),null!==(n=ss(e,r,t))&&(Dc(n,e,t),ls(n,e,t))}};function hi(e,n,t,r,a,s,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,s,l):!n.prototype||!n.prototype.isPureReactComponent||(!Xt(t,r)||!Xt(a,s))}function mi(e,n,t,r){e=n.state,"function"===typeof n.componentWillReceiveProps&&n.componentWillReceiveProps(t,r),"function"===typeof n.UNSAFE_componentWillReceiveProps&&n.UNSAFE_componentWillReceiveProps(t,r),n.state!==e&&fi.enqueueReplaceState(n,n.state,null)}function pi(e,n){var t=n;if("ref"in n)for(var r in t={},n)"ref"!==r&&(t[r]=n[r]);if(e=e.defaultProps)for(var a in t===n&&(t=f({},t)),e)void 0===t[a]&&(t[a]=e[a]);return t}var gi="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vi(e){gi(e)}function bi(e){console.error(e)}function yi(e){gi(e)}function xi(e,n){try{(0,e.onUncaughtError)(n.value,{componentStack:n.stack})}catch(t){setTimeout(function(){throw t})}}function ji(e,n,t){try{(0,e.onCaughtError)(t.value,{componentStack:t.stack,errorBoundary:1===n.tag?n.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function wi(e,n,t){return(t=as(t)).tag=3,t.payload={element:null},t.callback=function(){xi(e,n)},t}function Si(e){return(e=as(e)).tag=3,e}function ki(e,n,t,r){var a=t.type.getDerivedStateFromError;if("function"===typeof a){var s=r.value;e.payload=function(){return a(s)},e.callback=function(){ji(n,t,r)}}var l=t.stateNode;null!==l&&"function"===typeof l.componentDidCatch&&(e.callback=function(){ji(n,t,r),"function"!==typeof a&&(null===kc?kc=new Set([this]):kc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ei=Error(l(461)),Ni=!1;function Ci(e,n,t,r){n.child=null===e?ti(n,null,t,r):ni(n,e.child,t,r)}function Ti(e,n,t,r,a){t=t.render;var s=n.ref;if("ref"in r){var l={};for(var i in r)"ref"!==i&&(l[i]=r[i])}else l=r;return Ea(n),r=Ls(e,n,t,l,s,a),i=As(),null===e||Ni?(sa&&i&&na(n),n.flags|=1,Ci(e,n,r,a),n.child):(Ds(e,n,a),Qi(e,n,a))}function Pi(e,n,t,r,a){if(null===e){var s=t.type;return"function"!==typeof s||Ir(s)||void 0!==s.defaultProps||null!==t.compare?((e=Fr(t.type,null,r,n,n.mode,a)).ref=n.ref,e.return=n,n.child=e):(n.tag=15,n.type=s,_i(e,n,s,r,a))}if(s=e.child,!Gi(e,a)){var l=s.memoizedProps;if((t=null!==(t=t.compare)?t:Xt)(l,r)&&e.ref===n.ref)return Qi(e,n,a)}return n.flags|=1,(e=Mr(s,r)).ref=n.ref,e.return=n,n.child=e}function _i(e,n,t,r,a){if(null!==e){var s=e.memoizedProps;if(Xt(s,r)&&e.ref===n.ref){if(Ni=!1,n.pendingProps=r=s,!Gi(e,a))return n.lanes=e.lanes,Qi(e,n,a);0!==(131072&e.flags)&&(Ni=!0)}}return zi(e,n,t,r,a)}function Li(e,n,t){var r=n.pendingProps,a=r.children,s=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&n.flags)){if(r=null!==s?s.baseLanes|t:t,null!==e){for(a=n.child=e.child,s=0;null!==a;)s=s|a.lanes|a.childLanes,a=a.sibling;n.childLanes=s&~r}else n.childLanes=0,n.child=null;return Ri(e,n,r,t)}if(0===(536870912&t))return n.lanes=n.childLanes=536870912,Ri(e,n,null!==s?s.baseLanes|t:t,t);n.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ha(0,null!==s?s.cachePool:null),null!==s?ps(n,s):gs(),li(n)}else null!==s?(Ha(0,s.cachePool),ps(n,s),ii(),n.memoizedState=null):(null!==e&&Ha(0,null),gs(),ii());return Ci(e,n,a,t),n.child}function Ri(e,n,t,r){var a=Ba();return a=null===a?null:{parent:Ra._currentValue,pool:a},n.memoizedState={baseLanes:t,cachePool:a},null!==e&&Ha(0,null),gs(),li(n),null!==e&&Sa(e,n,r,!0),null}function Oi(e,n){var t=n.ref;if(null===t)null!==e&&null!==e.ref&&(n.flags|=4194816);else{if("function"!==typeof t&&"object"!==typeof t)throw Error(l(284));null!==e&&e.ref===t||(n.flags|=4194816)}}function zi(e,n,t,r,a){return Ea(n),t=Ls(e,n,t,r,void 0,a),r=As(),null===e||Ni?(sa&&r&&na(n),n.flags|=1,Ci(e,n,t,a),n.child):(Ds(e,n,a),Qi(e,n,a))}function Ai(e,n,t,r,a,s){return Ea(n),n.updateQueue=null,t=Os(n,r,t,a),Rs(e),r=As(),null===e||Ni?(sa&&r&&na(n),n.flags|=1,Ci(e,n,t,s),n.child):(Ds(e,n,s),Qi(e,n,s))}function Di(e,n,t,r,a){if(Ea(n),null===n.stateNode){var s=zr,l=t.contextType;"object"===typeof l&&null!==l&&(s=Na(l)),s=new t(r,s),n.memoizedState=null!==s.state&&void 0!==s.state?s.state:null,s.updater=fi,n.stateNode=s,s._reactInternals=n,(s=n.stateNode).props=r,s.state=n.memoizedState,s.refs={},ts(n),l=t.contextType,s.context="object"===typeof l&&null!==l?Na(l):zr,s.state=n.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(di(n,t,l,r),s.state=n.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof s.getSnapshotBeforeUpdate||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||(l=s.state,"function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount(),l!==s.state&&fi.enqueueReplaceState(s,s.state,null),us(n,r,s,a),cs(),s.state=n.memoizedState),"function"===typeof s.componentDidMount&&(n.flags|=4194308),r=!0}else if(null===e){s=n.stateNode;var i=n.memoizedProps,o=pi(t,i);s.props=o;var c=s.context,u=t.contextType;l=zr,"object"===typeof u&&null!==u&&(l=Na(u));var d=t.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof s.getSnapshotBeforeUpdate,i=n.pendingProps!==i,u||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(i||c!==l)&&mi(n,s,r,l),ns=!1;var f=n.memoizedState;s.state=f,us(n,r,s,a),cs(),c=n.memoizedState,i||f!==c||ns?("function"===typeof d&&(di(n,t,d,r),c=n.memoizedState),(o=ns||hi(n,t,o,r,f,c,l))?(u||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(n.flags|=4194308)):("function"===typeof s.componentDidMount&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=c),s.props=r,s.state=c,s.context=l,r=o):("function"===typeof s.componentDidMount&&(n.flags|=4194308),r=!1)}else{s=n.stateNode,rs(e,n),u=pi(t,l=n.memoizedProps),s.props=u,d=n.pendingProps,f=s.context,c=t.contextType,o=zr,"object"===typeof c&&null!==c&&(o=Na(c)),(c="function"===typeof(i=t.getDerivedStateFromProps)||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(l!==d||f!==o)&&mi(n,s,r,o),ns=!1,f=n.memoizedState,s.state=f,us(n,r,s,a),cs();var h=n.memoizedState;l!==d||f!==h||ns||null!==e&&null!==e.dependencies&&ka(e.dependencies)?("function"===typeof i&&(di(n,t,i,r),h=n.memoizedState),(u=ns||hi(n,t,u,r,f,h,o)||null!==e&&null!==e.dependencies&&ka(e.dependencies))?(c||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,h,o),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,h,o)),"function"===typeof s.componentDidUpdate&&(n.flags|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(n.flags|=1024)):("function"!==typeof s.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(n.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=h),s.props=r,s.state=h,s.context=o,r=u):("function"!==typeof s.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(n.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(n.flags|=1024),r=!1)}return s=r,Oi(e,n),r=0!==(128&n.flags),s||r?(s=n.stateNode,t=r&&"function"!==typeof t.getDerivedStateFromError?null:s.render(),n.flags|=1,null!==e&&r?(n.child=ni(n,e.child,null,a),n.child=ni(n,null,t,a)):Ci(e,n,t,a),n.memoizedState=s.state,e=n.child):e=Qi(e,n,a),e}function Ii(e,n,t,r){return ha(),n.flags|=256,Ci(e,n,t,r),n.child}var Mi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function $i(e){return{baseLanes:e,cachePool:Va()}}function Fi(e,n,t){return e=null!==e?e.childLanes&~t:0,n&&(e|=gc),e}function Ui(e,n,t){var r,a=n.pendingProps,s=!1,i=0!==(128&n.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&ci.current)),r&&(s=!0,n.flags&=-129),r=0!==(32&n.flags),n.flags&=-33,null===e){if(sa){if(s?si(n):ii(),sa){var o,c=aa;if(o=c){e:{for(o=c,c=ia;8!==o.nodeType;){if(!c){c=null;break e}if(null===(o=vd(o.nextSibling))){c=null;break e}}c=o}null!==c?(n.memoizedState={dehydrated:c,treeContext:null!==Xr?{id:Yr,overflow:Jr}:null,retryLane:536870912,hydrationErrors:null},(o=Dr(18,null,null,0)).stateNode=c,o.return=n,n.child=o,ra=n,aa=null,o=!0):o=!1}o||ca(n)}if(null!==(c=n.memoizedState)&&null!==(c=c.dehydrated))return gd(c)?n.lanes=32:n.lanes=536870912,null;oi(n)}return c=a.children,a=a.fallback,s?(ii(),c=Hi({mode:"hidden",children:c},s=n.mode),a=Ur(a,s,t,null),c.return=n,a.return=n,c.sibling=a,n.child=c,(s=n.child).memoizedState=$i(t),s.childLanes=Fi(e,r,t),n.memoizedState=Mi,a):(si(n),Bi(n,c))}if(null!==(o=e.memoizedState)&&null!==(c=o.dehydrated)){if(i)256&n.flags?(si(n),n.flags&=-257,n=Vi(e,n,t)):null!==n.memoizedState?(ii(),n.child=e.child,n.flags|=128,n=null):(ii(),s=a.fallback,c=n.mode,a=Hi({mode:"visible",children:a.children},c),(s=Ur(s,c,t,null)).flags|=2,a.return=n,s.return=n,a.sibling=s,n.child=a,ni(n,e.child,null,t),(a=n.child).memoizedState=$i(t),a.childLanes=Fi(e,r,t),n.memoizedState=Mi,n=s);else if(si(n),gd(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(a=Error(l(419))).stack="",a.digest=r,pa({value:a,source:null,stack:null}),n=Vi(e,n,t)}else if(Ni||Sa(e,n,t,!1),r=0!==(t&e.childLanes),Ni||r){if(null!==(r=rc)&&(0!==(a=0!==((a=0!==(42&(a=t&-t))?1:Te(a))&(r.suspendedLanes|t))?0:a)&&a!==o.retryLane))throw o.retryLane=a,Lr(e,a),Dc(r,e,a),Ei;"$?"===c.data||Kc(),n=Vi(e,n,t)}else"$?"===c.data?(n.flags|=192,n.child=e.child,n=null):(e=o.treeContext,aa=vd(c.nextSibling),ra=n,sa=!0,la=null,ia=!1,null!==e&&(Qr[Gr++]=Yr,Qr[Gr++]=Jr,Qr[Gr++]=Xr,Yr=e.id,Jr=e.overflow,Xr=n),(n=Bi(n,a.children)).flags|=4096);return n}return s?(ii(),s=a.fallback,c=n.mode,u=(o=e.child).sibling,(a=Mr(o,{mode:"hidden",children:a.children})).subtreeFlags=65011712&o.subtreeFlags,null!==u?s=Mr(u,s):(s=Ur(s,c,t,null)).flags|=2,s.return=n,a.return=n,a.sibling=s,n.child=a,a=s,s=n.child,null===(c=e.child.memoizedState)?c=$i(t):(null!==(o=c.cachePool)?(u=Ra._currentValue,o=o.parent!==u?{parent:u,pool:u}:o):o=Va(),c={baseLanes:c.baseLanes|t,cachePool:o}),s.memoizedState=c,s.childLanes=Fi(e,r,t),n.memoizedState=Mi,a):(si(n),e=(t=e.child).sibling,(t=Mr(t,{mode:"visible",children:a.children})).return=n,t.sibling=null,null!==e&&(null===(r=n.deletions)?(n.deletions=[e],n.flags|=16):r.push(e)),n.child=t,n.memoizedState=null,t)}function Bi(e,n){return(n=Hi({mode:"visible",children:n},e.mode)).return=e,e.child=n}function Hi(e,n){return(e=Dr(22,e,null,n)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vi(e,n,t){return ni(n,e.child,null,t),(e=Bi(n,n.pendingProps.children)).flags|=2,n.memoizedState=null,e}function Wi(e,n,t){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n),ja(e.return,n,t)}function qi(e,n,t,r,a){var s=e.memoizedState;null===s?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:t,tailMode:a}:(s.isBackwards=n,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=t,s.tailMode=a)}function Ki(e,n,t){var r=n.pendingProps,a=r.revealOrder,s=r.tail;if(Ci(e,n,r.children,t),0!==(2&(r=ci.current)))r=1&r|2,n.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=n.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wi(e,t,n);else if(19===e.tag)Wi(e,t,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(U(ci,r),a){case"forwards":for(t=n.child,a=null;null!==t;)null!==(e=t.alternate)&&null===ui(e)&&(a=t),t=t.sibling;null===(t=a)?(a=n.child,n.child=null):(a=t.sibling,t.sibling=null),qi(n,!1,a,t,s);break;case"backwards":for(t=null,a=n.child,n.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ui(e)){n.child=a;break}e=a.sibling,a.sibling=t,t=a,a=e}qi(n,!0,t,null,s);break;case"together":qi(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Qi(e,n,t){if(null!==e&&(n.dependencies=e.dependencies),hc|=n.lanes,0===(t&n.childLanes)){if(null===e)return null;if(Sa(e,n,t,!1),0===(t&n.childLanes))return null}if(null!==e&&n.child!==e.child)throw Error(l(153));if(null!==n.child){for(t=Mr(e=n.child,e.pendingProps),n.child=t,t.return=n;null!==e.sibling;)e=e.sibling,(t=t.sibling=Mr(e,e.pendingProps)).return=n;t.sibling=null}return n.child}function Gi(e,n){return 0!==(e.lanes&n)||!(null===(e=e.dependencies)||!ka(e))}function Xi(e,n,t){if(null!==e)if(e.memoizedProps!==n.pendingProps)Ni=!0;else{if(!Gi(e,t)&&0===(128&n.flags))return Ni=!1,function(e,n,t){switch(n.tag){case 3:q(n,n.stateNode.containerInfo),ya(0,Ra,e.memoizedState.cache),ha();break;case 27:case 5:Q(n);break;case 4:q(n,n.stateNode.containerInfo);break;case 10:ya(0,n.type,n.memoizedProps.value);break;case 13:var r=n.memoizedState;if(null!==r)return null!==r.dehydrated?(si(n),n.flags|=128,null):0!==(t&n.child.childLanes)?Ui(e,n,t):(si(n),null!==(e=Qi(e,n,t))?e.sibling:null);si(n);break;case 19:var a=0!==(128&e.flags);if((r=0!==(t&n.childLanes))||(Sa(e,n,t,!1),r=0!==(t&n.childLanes)),a){if(r)return Ki(e,n,t);n.flags|=128}if(null!==(a=n.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),U(ci,ci.current),r)break;return null;case 22:case 23:return n.lanes=0,Li(e,n,t);case 24:ya(0,Ra,e.memoizedState.cache)}return Qi(e,n,t)}(e,n,t);Ni=0!==(131072&e.flags)}else Ni=!1,sa&&0!==(1048576&n.flags)&&ea(n,Kr,n.index);switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var r=n.elementType,a=r._init;if(r=a(r._payload),n.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===w){n.tag=11,n=Ti(null,n,r,e,t);break e}if(a===E){n.tag=14,n=Pi(null,n,r,e,t);break e}}throw n=R(r)||r,Error(l(306,n,""))}Ir(r)?(e=pi(r,e),n.tag=1,n=Di(null,n,r,e,t)):(n.tag=0,n=zi(null,n,r,e,t))}return n;case 0:return zi(e,n,n.type,n.pendingProps,t);case 1:return Di(e,n,r=n.type,a=pi(r,n.pendingProps),t);case 3:e:{if(q(n,n.stateNode.containerInfo),null===e)throw Error(l(387));r=n.pendingProps;var s=n.memoizedState;a=s.element,rs(e,n),us(n,r,null,t);var i=n.memoizedState;if(r=i.cache,ya(0,Ra,r),r!==s.cache&&wa(n,[Ra],t,!0),cs(),r=i.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:i.cache},n.updateQueue.baseState=s,n.memoizedState=s,256&n.flags){n=Ii(e,n,r,t);break e}if(r!==a){pa(a=kr(Error(l(424)),n)),n=Ii(e,n,r,t);break e}if(9===(e=n.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=vd(e.firstChild),ra=n,sa=!0,la=null,ia=!0,t=ti(n,null,r,t),n.child=t;t;)t.flags=-3&t.flags|4096,t=t.sibling}else{if(ha(),r===a){n=Qi(e,n,t);break e}Ci(e,n,r,t)}n=n.child}return n;case 26:return Oi(e,n),null===e?(t=Td(n.type,null,n.pendingProps,null))?n.memoizedState=t:sa||(t=n.type,e=n.pendingProps,(r=rd(V.current).createElement(t))[Re]=n,r[Oe]=e,ed(r,t,e),We(r),n.stateNode=r):n.memoizedState=Td(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return Q(n),null===e&&sa&&(r=n.stateNode=xd(n.type,n.pendingProps,V.current),ra=n,ia=!0,a=aa,hd(n.type)?(bd=a,aa=vd(r.firstChild)):aa=a),Ci(e,n,n.pendingProps.children,t),Oi(e,n),null===e&&(n.flags|=4194304),n.child;case 5:return null===e&&sa&&((a=r=aa)&&(null!==(r=function(e,n,t,r){for(;1===e.nodeType;){var a=t;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[$e])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(s=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(s!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((s=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==n||"hidden"!==e.type)return e;var s=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===s)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,n.type,n.pendingProps,ia))?(n.stateNode=r,ra=n,aa=vd(r.firstChild),ia=!1,a=!0):a=!1),a||ca(n)),Q(n),a=n.type,s=n.pendingProps,i=null!==e?e.memoizedProps:null,r=s.children,ld(a,s)?r=null:null!==i&&ld(a,i)&&(n.flags|=32),null!==n.memoizedState&&(a=Ls(e,n,zs,null,null,t),Qd._currentValue=a),Oi(e,n),Ci(e,n,r,t),n.child;case 6:return null===e&&sa&&((e=t=aa)&&(null!==(t=function(e,n,t){if(""===n)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!t)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(t,n.pendingProps,ia))?(n.stateNode=t,ra=n,aa=null,e=!0):e=!1),e||ca(n)),null;case 13:return Ui(e,n,t);case 4:return q(n,n.stateNode.containerInfo),r=n.pendingProps,null===e?n.child=ni(n,null,r,t):Ci(e,n,r,t),n.child;case 11:return Ti(e,n,n.type,n.pendingProps,t);case 7:return Ci(e,n,n.pendingProps,t),n.child;case 8:case 12:return Ci(e,n,n.pendingProps.children,t),n.child;case 10:return r=n.pendingProps,ya(0,n.type,r.value),Ci(e,n,r.children,t),n.child;case 9:return a=n.type._context,r=n.pendingProps.children,Ea(n),r=r(a=Na(a)),n.flags|=1,Ci(e,n,r,t),n.child;case 14:return Pi(e,n,n.type,n.pendingProps,t);case 15:return _i(e,n,n.type,n.pendingProps,t);case 19:return Ki(e,n,t);case 31:return r=n.pendingProps,t=n.mode,r={mode:r.mode,children:r.children},null===e?((t=Hi(r,t)).ref=n.ref,n.child=t,t.return=n,n=t):((t=Mr(e.child,r)).ref=n.ref,n.child=t,t.return=n,n=t),n;case 22:return Li(e,n,t);case 24:return Ea(n),r=Na(Ra),null===e?(null===(a=Ba())&&(a=rc,s=Oa(),a.pooledCache=s,s.refCount++,null!==s&&(a.pooledCacheLanes|=t),a=s),n.memoizedState={parent:r,cache:a},ts(n),ya(0,Ra,a)):(0!==(e.lanes&t)&&(rs(e,n),us(n,null,null,t),cs()),a=e.memoizedState,s=n.memoizedState,a.parent!==r?(a={parent:r,cache:r},n.memoizedState=a,0===n.lanes&&(n.memoizedState=n.updateQueue.baseState=a),ya(0,Ra,r)):(r=s.cache,ya(0,Ra,r),r!==a.cache&&wa(n,[Ra],t,!0))),Ci(e,n,n.pendingProps.children,t),n.child;case 29:throw n.pendingProps}throw Error(l(156,n.tag))}function Yi(e){e.flags|=4}function Ji(e,n){if("stylesheet"!==n.type||0!==(4&n.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Ud(n)){if(null!==(n=ri.current)&&((4194048&sc)===sc?null!==ai:(62914560&sc)!==sc&&0===(536870912&sc)||n!==ai))throw Ja=Qa,qa;e.flags|=8192}}function Zi(e,n){null!==n&&(e.flags|=4),16384&e.flags&&(n=22!==e.tag?Se():536870912,e.lanes|=n,vc|=n)}function eo(e,n){if(!sa)switch(e.tailMode){case"hidden":n=e.tail;for(var t=null;null!==n;)null!==n.alternate&&(t=n),n=n.sibling;null===t?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?n||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function no(e){var n=null!==e.alternate&&e.alternate.child===e.child,t=0,r=0;if(n)for(var a=e.child;null!==a;)t|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)t|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=t,n}function to(e,n,t){var r=n.pendingProps;switch(ta(n),n.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return no(n),null;case 3:return t=n.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),n.memoizedState.cache!==r&&(n.flags|=2048),xa(Ra),K(),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),null!==e&&null!==e.child||(fa(n)?Yi(n):null===e||e.memoizedState.isDehydrated&&0===(256&n.flags)||(n.flags|=1024,ma())),no(n),null;case 26:return t=n.memoizedState,null===e?(Yi(n),null!==t?(no(n),Ji(n,t)):(no(n),n.flags&=-16777217)):t?t!==e.memoizedState?(Yi(n),no(n),Ji(n,t)):(no(n),n.flags&=-16777217):(e.memoizedProps!==r&&Yi(n),no(n),n.flags&=-16777217),null;case 27:G(n),t=V.current;var a=n.type;if(null!==e&&null!=n.stateNode)e.memoizedProps!==r&&Yi(n);else{if(!r){if(null===n.stateNode)throw Error(l(166));return no(n),null}e=B.current,fa(n)?ua(n):(e=xd(a,r,t),n.stateNode=e,Yi(n))}return no(n),null;case 5:if(G(n),t=n.type,null!==e&&null!=n.stateNode)e.memoizedProps!==r&&Yi(n);else{if(!r){if(null===n.stateNode)throw Error(l(166));return no(n),null}if(e=B.current,fa(n))ua(n);else{switch(a=rd(V.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",t);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;default:switch(t){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",t);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(t,{is:r.is}):a.createElement(t)}}e[Re]=n,e[Oe]=r;e:for(a=n.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===n)break e;for(;null===a.sibling;){if(null===a.return||a.return===n)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}n.stateNode=e;e:switch(ed(e,t,r),t){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Yi(n)}}return no(n),n.flags&=-16777217,null;case 6:if(e&&null!=n.stateNode)e.memoizedProps!==r&&Yi(n);else{if("string"!==typeof r&&null===n.stateNode)throw Error(l(166));if(e=V.current,fa(n)){if(e=n.stateNode,t=n.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Re]=n,(e=!!(e.nodeValue===t||null!==r&&!0===r.suppressHydrationWarning||Xu(e.nodeValue,t)))||ca(n)}else(e=rd(e).createTextNode(r))[Re]=n,n.stateNode=e}return no(n),null;case 13:if(r=n.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(n),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(l(318));if(!(a=null!==(a=n.memoizedState)?a.dehydrated:null))throw Error(l(317));a[Re]=n}else ha(),0===(128&n.flags)&&(n.memoizedState=null),n.flags|=4;no(n),a=!1}else a=ma(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&n.flags?(oi(n),n):(oi(n),null)}if(oi(n),0!==(128&n.flags))return n.lanes=t,n;if(t=null!==r,e=null!==e&&null!==e.memoizedState,t){a=null,null!==(r=n.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var s=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(s=r.memoizedState.cachePool.pool),s!==a&&(r.flags|=2048)}return t!==e&&t&&(n.child.flags|=8192),Zi(n,n.updateQueue),no(n),null;case 4:return K(),null===e&&Fu(n.stateNode.containerInfo),no(n),null;case 10:return xa(n.type),no(n),null;case 19:if(F(ci),null===(a=n.memoizedState))return no(n),null;if(r=0!==(128&n.flags),null===(s=a.rendering))if(r)eo(a,!1);else{if(0!==fc||null!==e&&0!==(128&e.flags))for(e=n.child;null!==e;){if(null!==(s=ui(e))){for(n.flags|=128,eo(a,!1),e=s.updateQueue,n.updateQueue=e,Zi(n,e),n.subtreeFlags=0,e=t,t=n.child;null!==t;)$r(t,e),t=t.sibling;return U(ci,1&ci.current|2),n.child}e=e.sibling}null!==a.tail&&ne()>wc&&(n.flags|=128,r=!0,eo(a,!1),n.lanes=4194304)}else{if(!r)if(null!==(e=ui(s))){if(n.flags|=128,r=!0,e=e.updateQueue,n.updateQueue=e,Zi(n,e),eo(a,!0),null===a.tail&&"hidden"===a.tailMode&&!s.alternate&&!sa)return no(n),null}else 2*ne()-a.renderingStartTime>wc&&536870912!==t&&(n.flags|=128,r=!0,eo(a,!1),n.lanes=4194304);a.isBackwards?(s.sibling=n.child,n.child=s):(null!==(e=a.last)?e.sibling=s:n.child=s,a.last=s)}return null!==a.tail?(n=a.tail,a.rendering=n,a.tail=n.sibling,a.renderingStartTime=ne(),n.sibling=null,e=ci.current,U(ci,r?1&e|2:1&e),n):(no(n),null);case 22:case 23:return oi(n),vs(),r=null!==n.memoizedState,null!==e?null!==e.memoizedState!==r&&(n.flags|=8192):r&&(n.flags|=8192),r?0!==(536870912&t)&&0===(128&n.flags)&&(no(n),6&n.subtreeFlags&&(n.flags|=8192)):no(n),null!==(t=n.updateQueue)&&Zi(n,t.retryQueue),t=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),r=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(r=n.memoizedState.cachePool.pool),r!==t&&(n.flags|=2048),null!==e&&F(Ua),null;case 24:return t=null,null!==e&&(t=e.memoizedState.cache),n.memoizedState.cache!==t&&(n.flags|=2048),xa(Ra),no(n),null;case 25:case 30:return null}throw Error(l(156,n.tag))}function ro(e,n){switch(ta(n),n.tag){case 1:return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 3:return xa(Ra),K(),0!==(65536&(e=n.flags))&&0===(128&e)?(n.flags=-65537&e|128,n):null;case 26:case 27:case 5:return G(n),null;case 13:if(oi(n),null!==(e=n.memoizedState)&&null!==e.dehydrated){if(null===n.alternate)throw Error(l(340));ha()}return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 19:return F(ci),null;case 4:return K(),null;case 10:return xa(n.type),null;case 22:case 23:return oi(n),vs(),null!==e&&F(Ua),65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 24:return xa(Ra),null;default:return null}}function ao(e,n){switch(ta(n),n.tag){case 3:xa(Ra),K();break;case 26:case 27:case 5:G(n);break;case 4:K();break;case 13:oi(n);break;case 19:F(ci);break;case 10:xa(n.type);break;case 22:case 23:oi(n),vs(),null!==e&&F(Ua);break;case 24:xa(Ra)}}function so(e,n){try{var t=n.updateQueue,r=null!==t?t.lastEffect:null;if(null!==r){var a=r.next;t=a;do{if((t.tag&e)===e){r=void 0;var s=t.create,l=t.inst;r=s(),l.destroy=r}t=t.next}while(t!==a)}}catch(i){uu(n,n.return,i)}}function lo(e,n,t){try{var r=n.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var s=a.next;r=s;do{if((r.tag&e)===e){var l=r.inst,i=l.destroy;if(void 0!==i){l.destroy=void 0,a=n;var o=t,c=i;try{c()}catch(u){uu(a,o,u)}}}r=r.next}while(r!==s)}}catch(u){uu(n,n.return,u)}}function io(e){var n=e.updateQueue;if(null!==n){var t=e.stateNode;try{fs(n,t)}catch(r){uu(e,e.return,r)}}}function oo(e,n,t){t.props=pi(e.type,e.memoizedProps),t.state=e.memoizedState;try{t.componentWillUnmount()}catch(r){uu(e,n,r)}}function co(e,n){try{var t=e.ref;if(null!==t){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof t?e.refCleanup=t(r):t.current=r}}catch(a){uu(e,n,a)}}function uo(e,n){var t=e.ref,r=e.refCleanup;if(null!==t)if("function"===typeof r)try{r()}catch(a){uu(e,n,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof t)try{t(null)}catch(s){uu(e,n,s)}else t.current=null}function fo(e){var n=e.type,t=e.memoizedProps,r=e.stateNode;try{e:switch(n){case"button":case"input":case"select":case"textarea":t.autoFocus&&r.focus();break e;case"img":t.src?r.src=t.src:t.srcSet&&(r.srcset=t.srcSet)}}catch(a){uu(e,e.return,a)}}function ho(e,n,t){try{var r=e.stateNode;!function(e,n,t,r){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,s=null,i=null,o=null,c=null,u=null,d=null;for(m in t){var f=t[m];if(t.hasOwnProperty(m)&&null!=f)switch(m){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(m)||Ju(e,n,m,null,r,f)}}for(var h in r){var m=r[h];if(f=t[h],r.hasOwnProperty(h)&&(null!=m||null!=f))switch(h){case"type":s=m;break;case"name":a=m;break;case"checked":u=m;break;case"defaultChecked":d=m;break;case"value":i=m;break;case"defaultValue":o=m;break;case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(l(137,n));break;default:m!==f&&Ju(e,n,h,m,r,f)}}return void vn(e,i,o,c,u,d,s,a);case"select":for(s in m=i=o=h=null,t)if(c=t[s],t.hasOwnProperty(s)&&null!=c)switch(s){case"value":break;case"multiple":m=c;default:r.hasOwnProperty(s)||Ju(e,n,s,null,r,c)}for(a in r)if(s=r[a],c=t[a],r.hasOwnProperty(a)&&(null!=s||null!=c))switch(a){case"value":h=s;break;case"defaultValue":o=s;break;case"multiple":i=s;default:s!==c&&Ju(e,n,a,s,r,c)}return n=o,t=i,r=m,void(null!=h?xn(e,!!t,h,!1):!!r!==!!t&&(null!=n?xn(e,!!t,n,!0):xn(e,!!t,t?[]:"",!1)));case"textarea":for(o in m=h=null,t)if(a=t[o],t.hasOwnProperty(o)&&null!=a&&!r.hasOwnProperty(o))switch(o){case"value":case"children":break;default:Ju(e,n,o,null,r,a)}for(i in r)if(a=r[i],s=t[i],r.hasOwnProperty(i)&&(null!=a||null!=s))switch(i){case"value":h=a;break;case"defaultValue":m=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(l(91));break;default:a!==s&&Ju(e,n,i,a,r,s)}return void jn(e,h,m);case"option":for(var p in t)if(h=t[p],t.hasOwnProperty(p)&&null!=h&&!r.hasOwnProperty(p))if("selected"===p)e.selected=!1;else Ju(e,n,p,null,r,h);for(c in r)if(h=r[c],m=t[c],r.hasOwnProperty(c)&&h!==m&&(null!=h||null!=m))if("selected"===c)e.selected=h&&"function"!==typeof h&&"symbol"!==typeof h;else Ju(e,n,c,h,r,m);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in t)h=t[g],t.hasOwnProperty(g)&&null!=h&&!r.hasOwnProperty(g)&&Ju(e,n,g,null,r,h);for(u in r)if(h=r[u],m=t[u],r.hasOwnProperty(u)&&h!==m&&(null!=h||null!=m))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(l(137,n));break;default:Ju(e,n,u,h,r,m)}return;default:if(Cn(n)){for(var v in t)h=t[v],t.hasOwnProperty(v)&&void 0!==h&&!r.hasOwnProperty(v)&&Zu(e,n,v,void 0,r,h);for(d in r)h=r[d],m=t[d],!r.hasOwnProperty(d)||h===m||void 0===h&&void 0===m||Zu(e,n,d,h,r,m);return}}for(var b in t)h=t[b],t.hasOwnProperty(b)&&null!=h&&!r.hasOwnProperty(b)&&Ju(e,n,b,null,r,h);for(f in r)h=r[f],m=t[f],!r.hasOwnProperty(f)||h===m||null==h&&null==m||Ju(e,n,f,h,r,m)}(r,e.type,t,n),r[Oe]=n}catch(a){uu(e,e.return,a)}}function mo(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&hd(e.type)||4===e.tag}function po(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||mo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&hd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function go(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?(9===t.nodeType?t.body:"HTML"===t.nodeName?t.ownerDocument.body:t).insertBefore(e,n):((n=9===t.nodeType?t.body:"HTML"===t.nodeName?t.ownerDocument.body:t).appendChild(e),null!==(t=t._reactRootContainer)&&void 0!==t||null!==n.onclick||(n.onclick=Yu));else if(4!==r&&(27===r&&hd(e.type)&&(t=e.stateNode,n=null),null!==(e=e.child)))for(go(e,n,t),e=e.sibling;null!==e;)go(e,n,t),e=e.sibling}function vo(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?t.insertBefore(e,n):t.appendChild(e);else if(4!==r&&(27===r&&hd(e.type)&&(t=e.stateNode),null!==(e=e.child)))for(vo(e,n,t),e=e.sibling;null!==e;)vo(e,n,t),e=e.sibling}function bo(e){var n=e.stateNode,t=e.memoizedProps;try{for(var r=e.type,a=n.attributes;a.length;)n.removeAttributeNode(a[0]);ed(n,r,t),n[Re]=e,n[Oe]=t}catch(s){uu(e,e.return,s)}}var yo=!1,xo=!1,jo=!1,wo="function"===typeof WeakSet?WeakSet:Set,So=null;function ko(e,n,t){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Io(e,t),4&r&&so(5,t);break;case 1:if(Io(e,t),4&r)if(e=t.stateNode,null===n)try{e.componentDidMount()}catch(l){uu(t,t.return,l)}else{var a=pi(t.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(a,n,e.__reactInternalSnapshotBeforeUpdate)}catch(i){uu(t,t.return,i)}}64&r&&io(t),512&r&&co(t,t.return);break;case 3:if(Io(e,t),64&r&&null!==(e=t.updateQueue)){if(n=null,null!==t.child)switch(t.child.tag){case 27:case 5:case 1:n=t.child.stateNode}try{fs(e,n)}catch(l){uu(t,t.return,l)}}break;case 27:null===n&&4&r&&bo(t);case 26:case 5:Io(e,t),null===n&&4&r&&fo(t),512&r&&co(t,t.return);break;case 12:Io(e,t);break;case 13:Io(e,t),4&r&&_o(e,t),64&r&&(null!==(e=t.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,n){var t=e.ownerDocument;if("$?"!==e.data||"complete"===t.readyState)n();else{var r=function(){n(),t.removeEventListener("DOMContentLoaded",r)};t.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,t=mu.bind(null,t))));break;case 22:if(!(r=null!==t.memoizedState||yo)){n=null!==n&&null!==n.memoizedState||xo,a=yo;var s=xo;yo=r,(xo=n)&&!s?$o(e,t,0!==(8772&t.subtreeFlags)):Io(e,t),yo=a,xo=s}break;case 30:break;default:Io(e,t)}}function Eo(e){var n=e.alternate;null!==n&&(e.alternate=null,Eo(n)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(n=e.stateNode)&&Fe(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var No=null,Co=!1;function To(e,n,t){for(t=t.child;null!==t;)Po(e,n,t),t=t.sibling}function Po(e,n,t){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,t)}catch(s){}switch(t.tag){case 26:xo||uo(t,n),To(e,n,t),t.memoizedState?t.memoizedState.count--:t.stateNode&&(t=t.stateNode).parentNode.removeChild(t);break;case 27:xo||uo(t,n);var r=No,a=Co;hd(t.type)&&(No=t.stateNode,Co=!1),To(e,n,t),jd(t.stateNode),No=r,Co=a;break;case 5:xo||uo(t,n);case 6:if(r=No,a=Co,No=null,To(e,n,t),Co=a,null!==(No=r))if(Co)try{(9===No.nodeType?No.body:"HTML"===No.nodeName?No.ownerDocument.body:No).removeChild(t.stateNode)}catch(l){uu(t,n,l)}else try{No.removeChild(t.stateNode)}catch(l){uu(t,n,l)}break;case 18:null!==No&&(Co?(md(9===(e=No).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,t.stateNode),Tf(e)):md(No,t.stateNode));break;case 4:r=No,a=Co,No=t.stateNode.containerInfo,Co=!0,To(e,n,t),No=r,Co=a;break;case 0:case 11:case 14:case 15:xo||lo(2,t,n),xo||lo(4,t,n),To(e,n,t);break;case 1:xo||(uo(t,n),"function"===typeof(r=t.stateNode).componentWillUnmount&&oo(t,n,r)),To(e,n,t);break;case 21:To(e,n,t);break;case 22:xo=(r=xo)||null!==t.memoizedState,To(e,n,t),xo=r;break;default:To(e,n,t)}}function _o(e,n){if(null===n.memoizedState&&(null!==(e=n.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Tf(e)}catch(t){uu(n,n.return,t)}}function Lo(e,n){var t=function(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return null===n&&(n=e.stateNode=new wo),n;case 22:return null===(n=(e=e.stateNode)._retryCache)&&(n=e._retryCache=new wo),n;default:throw Error(l(435,e.tag))}}(e);n.forEach(function(n){var r=pu.bind(null,e,n);t.has(n)||(t.add(n),n.then(r,r))})}function Ro(e,n){var t=n.deletions;if(null!==t)for(var r=0;r<t.length;r++){var a=t[r],s=e,i=n,o=i;e:for(;null!==o;){switch(o.tag){case 27:if(hd(o.type)){No=o.stateNode,Co=!1;break e}break;case 5:No=o.stateNode,Co=!1;break e;case 3:case 4:No=o.stateNode.containerInfo,Co=!0;break e}o=o.return}if(null===No)throw Error(l(160));Po(s,i,a),No=null,Co=!1,null!==(s=a.alternate)&&(s.return=null),a.return=null}if(13878&n.subtreeFlags)for(n=n.child;null!==n;)zo(n,e),n=n.sibling}var Oo=null;function zo(e,n){var t=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ro(n,e),Ao(e),4&r&&(lo(3,e,e.return),so(3,e),lo(5,e,e.return));break;case 1:Ro(n,e),Ao(e),512&r&&(xo||null===t||uo(t,t.return)),64&r&&yo&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===t?r:t.concat(r))));break;case 26:var a=Oo;if(Ro(n,e),Ao(e),512&r&&(xo||null===t||uo(t,t.return)),4&r){var s=null!==t?t.memoizedState:null;if(r=e.memoizedState,null===t)if(null===r)if(null===e.stateNode){e:{r=e.type,t=e.memoizedProps,a=a.ownerDocument||a;n:switch(r){case"title":(!(s=a.getElementsByTagName("title")[0])||s[$e]||s[Re]||"http://www.w3.org/2000/svg"===s.namespaceURI||s.hasAttribute("itemprop"))&&(s=a.createElement(r),a.head.insertBefore(s,a.querySelector("head > title"))),ed(s,r,t),s[Re]=e,We(s),r=s;break e;case"link":var i=$d("link","href",a).get(r+(t.href||""));if(i)for(var o=0;o<i.length;o++)if((s=i[o]).getAttribute("href")===(null==t.href||""===t.href?null:t.href)&&s.getAttribute("rel")===(null==t.rel?null:t.rel)&&s.getAttribute("title")===(null==t.title?null:t.title)&&s.getAttribute("crossorigin")===(null==t.crossOrigin?null:t.crossOrigin)){i.splice(o,1);break n}ed(s=a.createElement(r),r,t),a.head.appendChild(s);break;case"meta":if(i=$d("meta","content",a).get(r+(t.content||"")))for(o=0;o<i.length;o++)if((s=i[o]).getAttribute("content")===(null==t.content?null:""+t.content)&&s.getAttribute("name")===(null==t.name?null:t.name)&&s.getAttribute("property")===(null==t.property?null:t.property)&&s.getAttribute("http-equiv")===(null==t.httpEquiv?null:t.httpEquiv)&&s.getAttribute("charset")===(null==t.charSet?null:t.charSet)){i.splice(o,1);break n}ed(s=a.createElement(r),r,t),a.head.appendChild(s);break;default:throw Error(l(468,r))}s[Re]=e,We(s),r=s}e.stateNode=r}else Fd(a,e.type,e.stateNode);else e.stateNode=zd(a,r,e.memoizedProps);else s!==r?(null===s?null!==t.stateNode&&(t=t.stateNode).parentNode.removeChild(t):s.count--,null===r?Fd(a,e.type,e.stateNode):zd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ho(e,e.memoizedProps,t.memoizedProps)}break;case 27:Ro(n,e),Ao(e),512&r&&(xo||null===t||uo(t,t.return)),null!==t&&4&r&&ho(e,e.memoizedProps,t.memoizedProps);break;case 5:if(Ro(n,e),Ao(e),512&r&&(xo||null===t||uo(t,t.return)),32&e.flags){a=e.stateNode;try{Sn(a,"")}catch(m){uu(e,e.return,m)}}4&r&&null!=e.stateNode&&ho(e,a=e.memoizedProps,null!==t?t.memoizedProps:a),1024&r&&(jo=!0);break;case 6:if(Ro(n,e),Ao(e),4&r){if(null===e.stateNode)throw Error(l(162));r=e.memoizedProps,t=e.stateNode;try{t.nodeValue=r}catch(m){uu(e,e.return,m)}}break;case 3:if(Md=null,a=Oo,Oo=kd(n.containerInfo),Ro(n,e),Oo=a,Ao(e),4&r&&null!==t&&t.memoizedState.isDehydrated)try{Tf(n.containerInfo)}catch(m){uu(e,e.return,m)}jo&&(jo=!1,Do(e));break;case 4:r=Oo,Oo=kd(e.stateNode.containerInfo),Ro(n,e),Ao(e),Oo=r;break;case 12:default:Ro(n,e),Ao(e);break;case 13:Ro(n,e),Ao(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==t&&null!==t.memoizedState)&&(jc=ne()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Lo(e,r)));break;case 22:a=null!==e.memoizedState;var c=null!==t&&null!==t.memoizedState,u=yo,d=xo;if(yo=u||a,xo=d||c,Ro(n,e),xo=d,yo=u,Ao(e),8192&r)e:for(n=e.stateNode,n._visibility=a?-2&n._visibility:1|n._visibility,a&&(null===t||c||yo||xo||Mo(e)),t=null,n=e;;){if(5===n.tag||26===n.tag){if(null===t){c=t=n;try{if(s=c.stateNode,a)"function"===typeof(i=s.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{o=c.stateNode;var f=c.memoizedProps.style,h=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;o.style.display=null==h||"boolean"===typeof h?"":(""+h).trim()}}catch(m){uu(c,c.return,m)}}}else if(6===n.tag){if(null===t){c=n;try{c.stateNode.nodeValue=a?"":c.memoizedProps}catch(m){uu(c,c.return,m)}}}else if((22!==n.tag&&23!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;null===n.sibling;){if(null===n.return||n.return===e)break e;t===n&&(t=null),n=n.return}t===n&&(t=null),n.sibling.return=n.return,n=n.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(t=r.retryQueue)&&(r.retryQueue=null,Lo(e,t))));break;case 19:Ro(n,e),Ao(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Lo(e,r)));case 30:case 21:}}function Ao(e){var n=e.flags;if(2&n){try{for(var t,r=e.return;null!==r;){if(mo(r)){t=r;break}r=r.return}if(null==t)throw Error(l(160));switch(t.tag){case 27:var a=t.stateNode;vo(e,po(e),a);break;case 5:var s=t.stateNode;32&t.flags&&(Sn(s,""),t.flags&=-33),vo(e,po(e),s);break;case 3:case 4:var i=t.stateNode.containerInfo;go(e,po(e),i);break;default:throw Error(l(161))}}catch(o){uu(e,e.return,o)}e.flags&=-3}4096&n&&(e.flags&=-4097)}function Do(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var n=e;Do(n),5===n.tag&&1024&n.flags&&n.stateNode.reset(),e=e.sibling}}function Io(e,n){if(8772&n.subtreeFlags)for(n=n.child;null!==n;)ko(e,n.alternate,n),n=n.sibling}function Mo(e){for(e=e.child;null!==e;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:lo(4,n,n.return),Mo(n);break;case 1:uo(n,n.return);var t=n.stateNode;"function"===typeof t.componentWillUnmount&&oo(n,n.return,t),Mo(n);break;case 27:jd(n.stateNode);case 26:case 5:uo(n,n.return),Mo(n);break;case 22:null===n.memoizedState&&Mo(n);break;default:Mo(n)}e=e.sibling}}function $o(e,n,t){for(t=t&&0!==(8772&n.subtreeFlags),n=n.child;null!==n;){var r=n.alternate,a=e,s=n,l=s.flags;switch(s.tag){case 0:case 11:case 15:$o(a,s,t),so(4,s);break;case 1:if($o(a,s,t),"function"===typeof(a=(r=s).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){uu(r,r.return,c)}if(null!==(a=(r=s).updateQueue)){var i=r.stateNode;try{var o=a.shared.hiddenCallbacks;if(null!==o)for(a.shared.hiddenCallbacks=null,a=0;a<o.length;a++)ds(o[a],i)}catch(c){uu(r,r.return,c)}}t&&64&l&&io(s),co(s,s.return);break;case 27:bo(s);case 26:case 5:$o(a,s,t),t&&null===r&&4&l&&fo(s),co(s,s.return);break;case 12:$o(a,s,t);break;case 13:$o(a,s,t),t&&4&l&&_o(a,s);break;case 22:null===s.memoizedState&&$o(a,s,t),co(s,s.return);break;case 30:break;default:$o(a,s,t)}n=n.sibling}}function Fo(e,n){var t=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),e=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(e=n.memoizedState.cachePool.pool),e!==t&&(null!=e&&e.refCount++,null!=t&&za(t))}function Uo(e,n){e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&za(e))}function Bo(e,n,t,r){if(10256&n.subtreeFlags)for(n=n.child;null!==n;)Ho(e,n,t,r),n=n.sibling}function Ho(e,n,t,r){var a=n.flags;switch(n.tag){case 0:case 11:case 15:Bo(e,n,t,r),2048&a&&so(9,n);break;case 1:case 13:default:Bo(e,n,t,r);break;case 3:Bo(e,n,t,r),2048&a&&(e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&za(e)));break;case 12:if(2048&a){Bo(e,n,t,r),e=n.stateNode;try{var s=n.memoizedProps,l=s.id,i=s.onPostCommit;"function"===typeof i&&i(l,null===n.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(o){uu(n,n.return,o)}}else Bo(e,n,t,r);break;case 23:break;case 22:s=n.stateNode,l=n.alternate,null!==n.memoizedState?2&s._visibility?Bo(e,n,t,r):Wo(e,n):2&s._visibility?Bo(e,n,t,r):(s._visibility|=2,Vo(e,n,t,r,0!==(10256&n.subtreeFlags))),2048&a&&Fo(l,n);break;case 24:Bo(e,n,t,r),2048&a&&Uo(n.alternate,n)}}function Vo(e,n,t,r,a){for(a=a&&0!==(10256&n.subtreeFlags),n=n.child;null!==n;){var s=e,l=n,i=t,o=r,c=l.flags;switch(l.tag){case 0:case 11:case 15:Vo(s,l,i,o,a),so(8,l);break;case 23:break;case 22:var u=l.stateNode;null!==l.memoizedState?2&u._visibility?Vo(s,l,i,o,a):Wo(s,l):(u._visibility|=2,Vo(s,l,i,o,a)),a&&2048&c&&Fo(l.alternate,l);break;case 24:Vo(s,l,i,o,a),a&&2048&c&&Uo(l.alternate,l);break;default:Vo(s,l,i,o,a)}n=n.sibling}}function Wo(e,n){if(10256&n.subtreeFlags)for(n=n.child;null!==n;){var t=e,r=n,a=r.flags;switch(r.tag){case 22:Wo(t,r),2048&a&&Fo(r.alternate,r);break;case 24:Wo(t,r),2048&a&&Uo(r.alternate,r);break;default:Wo(t,r)}n=n.sibling}}var qo=8192;function Ko(e){if(e.subtreeFlags&qo)for(e=e.child;null!==e;)Qo(e),e=e.sibling}function Qo(e){switch(e.tag){case 26:Ko(e),e.flags&qo&&null!==e.memoizedState&&function(e,n,t){if(null===Bd)throw Error(l(475));var r=Bd;if("stylesheet"===n.type&&("string"!==typeof t.media||!1!==matchMedia(t.media).matches)&&0===(4&n.state.loading)){if(null===n.instance){var a=Pd(t.href),s=e.querySelector(_d(a));if(s)return null!==(e=s._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Vd.bind(r),e.then(r,r)),n.state.loading|=4,n.instance=s,void We(s);s=e.ownerDocument||e,t=Ld(t),(a=wd.get(a))&&Dd(t,a),We(s=s.createElement("link"));var i=s;i._p=new Promise(function(e,n){i.onload=e,i.onerror=n}),ed(s,"link",t),n.instance=s}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(n,e),(e=n.state.preload)&&0===(3&n.state.loading)&&(r.count++,n=Vd.bind(r),e.addEventListener("load",n),e.addEventListener("error",n))}}(Oo,e.memoizedState,e.memoizedProps);break;case 5:default:Ko(e);break;case 3:case 4:var n=Oo;Oo=kd(e.stateNode.containerInfo),Ko(e),Oo=n;break;case 22:null===e.memoizedState&&(null!==(n=e.alternate)&&null!==n.memoizedState?(n=qo,qo=16777216,Ko(e),qo=n):Ko(e))}}function Go(e){var n=e.alternate;if(null!==n&&null!==(e=n.child)){n.child=null;do{n=e.sibling,e.sibling=null,e=n}while(null!==e)}}function Xo(e){var n=e.deletions;if(0!==(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];So=r,Zo(r,e)}Go(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Yo(e),e=e.sibling}function Yo(e){switch(e.tag){case 0:case 11:case 15:Xo(e),2048&e.flags&&lo(9,e,e.return);break;case 3:case 12:default:Xo(e);break;case 22:var n=e.stateNode;null!==e.memoizedState&&2&n._visibility&&(null===e.return||13!==e.return.tag)?(n._visibility&=-3,Jo(e)):Xo(e)}}function Jo(e){var n=e.deletions;if(0!==(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];So=r,Zo(r,e)}Go(e)}for(e=e.child;null!==e;){switch((n=e).tag){case 0:case 11:case 15:lo(8,n,n.return),Jo(n);break;case 22:2&(t=n.stateNode)._visibility&&(t._visibility&=-3,Jo(n));break;default:Jo(n)}e=e.sibling}}function Zo(e,n){for(;null!==So;){var t=So;switch(t.tag){case 0:case 11:case 15:lo(8,t,n);break;case 23:case 22:if(null!==t.memoizedState&&null!==t.memoizedState.cachePool){var r=t.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:za(t.memoizedState.cache)}if(null!==(r=t.child))r.return=t,So=r;else e:for(t=e;null!==So;){var a=(r=So).sibling,s=r.return;if(Eo(r),r===t){So=null;break e}if(null!==a){a.return=s,So=a;break e}So=s}}}var ec={getCacheForType:function(e){var n=Na(Ra),t=n.data.get(e);return void 0===t&&(t=e(),n.data.set(e,t)),t}},nc="function"===typeof WeakMap?WeakMap:Map,tc=0,rc=null,ac=null,sc=0,lc=0,ic=null,oc=!1,cc=!1,uc=!1,dc=0,fc=0,hc=0,mc=0,pc=0,gc=0,vc=0,bc=null,yc=null,xc=!1,jc=0,wc=1/0,Sc=null,kc=null,Ec=0,Nc=null,Cc=null,Tc=0,Pc=0,_c=null,Lc=null,Rc=0,Oc=null;function zc(){if(0!==(2&tc)&&0!==sc)return sc&-sc;if(null!==z.T){return 0!==Ia?Ia:Pu()}return _e()}function Ac(){0===gc&&(gc=0===(536870912&sc)||sa?we():536870912);var e=ri.current;return null!==e&&(e.flags|=32),gc}function Dc(e,n,t){(e!==rc||2!==lc&&9!==lc)&&null===e.cancelPendingCommit||(Hc(e,0),Fc(e,sc,gc,!1)),Ee(e,t),0!==(2&tc)&&e===rc||(e===rc&&(0===(2&tc)&&(mc|=t),4===fc&&Fc(e,sc,gc,!1)),wu(e))}function Ic(e,n,t){if(0!==(6&tc))throw Error(l(327));for(var r=!t&&0===(124&n)&&0===(n&e.expiredLanes)||xe(e,n),a=r?function(e,n){var t=tc;tc|=2;var r=Wc(),a=qc();rc!==e||sc!==n?(Sc=null,wc=ne()+500,Hc(e,n)):cc=xe(e,n);e:for(;;)try{if(0!==lc&&null!==ac){n=ac;var s=ic;n:switch(lc){case 1:lc=0,ic=null,Zc(e,n,s,1);break;case 2:case 9:if(Ga(s)){lc=0,ic=null,Jc(n);break}n=function(){2!==lc&&9!==lc||rc!==e||(lc=7),wu(e)},s.then(n,n);break e;case 3:lc=7;break e;case 4:lc=5;break e;case 7:Ga(s)?(lc=0,ic=null,Jc(n)):(lc=0,ic=null,Zc(e,n,s,7));break;case 5:var i=null;switch(ac.tag){case 26:i=ac.memoizedState;case 5:case 27:var o=ac;if(!i||Ud(i)){lc=0,ic=null;var c=o.sibling;if(null!==c)ac=c;else{var u=o.return;null!==u?(ac=u,eu(u)):ac=null}break n}}lc=0,ic=null,Zc(e,n,s,5);break;case 6:lc=0,ic=null,Zc(e,n,s,6);break;case 8:Bc(),fc=6;break e;default:throw Error(l(462))}}Xc();break}catch(d){Vc(e,d)}return ba=va=null,z.H=r,z.A=a,tc=t,null!==ac?0:(rc=null,sc=0,Tr(),fc)}(e,n):Qc(e,n,!0),s=r;;){if(0===a){cc&&!r&&Fc(e,n,0,!1);break}if(t=e.current.alternate,!s||$c(t)){if(2===a){if(s=n,e.errorRecoveryDisabledLanes&s)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){n=i;e:{var o=e;a=bc;var c=o.current.memoizedState.isDehydrated;if(c&&(Hc(o,i).flags|=256),2!==(i=Qc(o,i,!1))){if(uc&&!c){o.errorRecoveryDisabledLanes|=s,mc|=s,a=4;break e}s=yc,yc=a,null!==s&&(null===yc?yc=s:yc.push.apply(yc,s))}a=i}if(s=!1,2!==a)continue}}if(1===a){Hc(e,0),Fc(e,n,0,!0);break}e:{switch(r=e,s=a){case 0:case 1:throw Error(l(345));case 4:if((4194048&n)!==n)break;case 6:Fc(r,n,gc,!oc);break e;case 2:yc=null;break;case 3:case 5:break;default:throw Error(l(329))}if((62914560&n)===n&&10<(a=jc+300-ne())){if(Fc(r,n,gc,!oc),0!==ye(r,0,!0))break e;r.timeoutHandle=od(Mc.bind(null,r,t,yc,Sc,xc,n,gc,mc,vc,oc,s,2,-0,0),a)}else Mc(r,t,yc,Sc,xc,n,gc,mc,vc,oc,s,0,-0,0)}break}a=Qc(e,n,!1),s=!1}wu(e)}function Mc(e,n,t,r,a,s,i,o,c,u,d,f,h,m){if(e.timeoutHandle=-1,(8192&(f=n.subtreeFlags)||16785408===(16785408&f))&&(Bd={stylesheets:null,count:0,unsuspend:Hd},Qo(n),null!==(f=function(){if(null===Bd)throw Error(l(475));var e=Bd;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(n){var t=setTimeout(function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(t)}}:null}())))return e.cancelPendingCommit=f(tu.bind(null,e,n,s,t,r,a,i,o,c,d,1,h,m)),void Fc(e,s,i,!u);tu(e,n,s,t,r,a,i,o,c)}function $c(e){for(var n=e;;){var t=n.tag;if((0===t||11===t||15===t)&&16384&n.flags&&(null!==(t=n.updateQueue)&&null!==(t=t.stores)))for(var r=0;r<t.length;r++){var a=t[r],s=a.getSnapshot;a=a.value;try{if(!Gt(s(),a))return!1}catch(l){return!1}}if(t=n.child,16384&n.subtreeFlags&&null!==t)t.return=n,n=t;else{if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Fc(e,n,t,r){n&=~pc,n&=~mc,e.suspendedLanes|=n,e.pingedLanes&=~n,r&&(e.warmLanes|=n),r=e.expirationTimes;for(var a=n;0<a;){var s=31-he(a),l=1<<s;r[s]=-1,a&=~l}0!==t&&Ne(e,t,n)}function Uc(){return 0!==(6&tc)||(Su(0,!1),!1)}function Bc(){if(null!==ac){if(0===lc)var e=ac.return;else ba=va=null,Is(e=ac),Ql=null,Gl=0,e=ac;for(;null!==e;)ao(e.alternate,e),e=e.return;ac=null}}function Hc(e,n){var t=e.timeoutHandle;-1!==t&&(e.timeoutHandle=-1,cd(t)),null!==(t=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,t()),Bc(),rc=e,ac=t=Mr(e.current,null),sc=n,lc=0,ic=null,oc=!1,cc=xe(e,n),uc=!1,vc=gc=pc=mc=hc=fc=0,yc=bc=null,xc=!1,0!==(8&n)&&(n|=32&n);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=n;0<r;){var a=31-he(r),s=1<<a;n|=e[a],r&=~s}return dc=n,Tr(),t}function Vc(e,n){ys=null,z.H=Vl,n===Wa||n===Ka?(n=Za(),lc=3):n===qa?(n=Za(),lc=4):lc=n===Ei?8:null!==n&&"object"===typeof n&&"function"===typeof n.then?6:1,ic=n,null===ac&&(fc=1,xi(e,kr(n,e.current)))}function Wc(){var e=z.H;return z.H=Vl,null===e?Vl:e}function qc(){var e=z.A;return z.A=ec,e}function Kc(){fc=4,oc||(4194048&sc)!==sc&&null!==ri.current||(cc=!0),0===(134217727&hc)&&0===(134217727&mc)||null===rc||Fc(rc,sc,gc,!1)}function Qc(e,n,t){var r=tc;tc|=2;var a=Wc(),s=qc();rc===e&&sc===n||(Sc=null,Hc(e,n)),n=!1;var l=fc;e:for(;;)try{if(0!==lc&&null!==ac){var i=ac,o=ic;switch(lc){case 8:Bc(),l=6;break e;case 3:case 2:case 9:case 6:null===ri.current&&(n=!0);var c=lc;if(lc=0,ic=null,Zc(e,i,o,c),t&&cc){l=0;break e}break;default:c=lc,lc=0,ic=null,Zc(e,i,o,c)}}Gc(),l=fc;break}catch(u){Vc(e,u)}return n&&e.shellSuspendCounter++,ba=va=null,tc=r,z.H=a,z.A=s,null===ac&&(rc=null,sc=0,Tr()),l}function Gc(){for(;null!==ac;)Yc(ac)}function Xc(){for(;null!==ac&&!Z();)Yc(ac)}function Yc(e){var n=Xi(e.alternate,e,dc);e.memoizedProps=e.pendingProps,null===n?eu(e):ac=n}function Jc(e){var n=e,t=n.alternate;switch(n.tag){case 15:case 0:n=Ai(t,n,n.pendingProps,n.type,void 0,sc);break;case 11:n=Ai(t,n,n.pendingProps,n.type.render,n.ref,sc);break;case 5:Is(n);default:ao(t,n),n=Xi(t,n=ac=$r(n,dc),dc)}e.memoizedProps=e.pendingProps,null===n?eu(e):ac=n}function Zc(e,n,t,r){ba=va=null,Is(n),Ql=null,Gl=0;var a=n.return;try{if(function(e,n,t,r,a){if(t.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(n=t.alternate)&&Sa(n,t,a,!0),null!==(t=ri.current)){switch(t.tag){case 13:return null===ai?Kc():null===t.alternate&&0===fc&&(fc=3),t.flags&=-257,t.flags|=65536,t.lanes=a,r===Qa?t.flags|=16384:(null===(n=t.updateQueue)?t.updateQueue=new Set([r]):n.add(r),du(e,r,a)),!1;case 22:return t.flags|=65536,r===Qa?t.flags|=16384:(null===(n=t.updateQueue)?(n={transitions:null,markerInstances:null,retryQueue:new Set([r])},t.updateQueue=n):null===(t=n.retryQueue)?n.retryQueue=new Set([r]):t.add(r),du(e,r,a)),!1}throw Error(l(435,t.tag))}return du(e,r,a),Kc(),!1}if(sa)return null!==(n=ri.current)?(0===(65536&n.flags)&&(n.flags|=256),n.flags|=65536,n.lanes=a,r!==oa&&pa(kr(e=Error(l(422),{cause:r}),t))):(r!==oa&&pa(kr(n=Error(l(423),{cause:r}),t)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=kr(r,t),is(e,a=wi(e.stateNode,r,a)),4!==fc&&(fc=2)),!1;var s=Error(l(520),{cause:r});if(s=kr(s,t),null===bc?bc=[s]:bc.push(s),4!==fc&&(fc=2),null===n)return!0;r=kr(r,t),t=n;do{switch(t.tag){case 3:return t.flags|=65536,e=a&-a,t.lanes|=e,is(t,e=wi(t.stateNode,r,e)),!1;case 1:if(n=t.type,s=t.stateNode,0===(128&t.flags)&&("function"===typeof n.getDerivedStateFromError||null!==s&&"function"===typeof s.componentDidCatch&&(null===kc||!kc.has(s))))return t.flags|=65536,a&=-a,t.lanes|=a,ki(a=Si(a),e,t,r),is(t,a),!1}t=t.return}while(null!==t);return!1}(e,a,n,t,sc))return fc=1,xi(e,kr(t,e.current)),void(ac=null)}catch(s){if(null!==a)throw ac=a,s;return fc=1,xi(e,kr(t,e.current)),void(ac=null)}32768&n.flags?(sa||1===r?e=!0:cc||0!==(536870912&sc)?e=!1:(oc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ri.current)&&13===r.tag&&(r.flags|=16384))),nu(n,e)):eu(n)}function eu(e){var n=e;do{if(0!==(32768&n.flags))return void nu(n,oc);e=n.return;var t=to(n.alternate,n,dc);if(null!==t)return void(ac=t);if(null!==(n=n.sibling))return void(ac=n);ac=n=e}while(null!==n);0===fc&&(fc=5)}function nu(e,n){do{var t=ro(e.alternate,e);if(null!==t)return t.flags&=32767,void(ac=t);if(null!==(t=e.return)&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!n&&null!==(e=e.sibling))return void(ac=e);ac=e=t}while(null!==e);fc=6,ac=null}function tu(e,n,t,r,a,s,i,o,c){e.cancelPendingCommit=null;do{iu()}while(0!==Ec);if(0!==(6&tc))throw Error(l(327));if(null!==n){if(n===e.current)throw Error(l(177));if(s=n.lanes|n.childLanes,function(e,n,t,r,a,s){var l=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0;var i=e.entanglements,o=e.expirationTimes,c=e.hiddenUpdates;for(t=l&~t;0<t;){var u=31-he(t),d=1<<u;i[u]=0,o[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var h=f[u];null!==h&&(h.lane&=-536870913)}t&=~d}0!==r&&Ne(e,r,0),0!==s&&0===a&&0!==e.tag&&(e.suspendedLanes|=s&~(l&~n))}(e,t,s|=Cr,i,o,c),e===rc&&(ac=rc=null,sc=0),Cc=n,Nc=e,Tc=t,Pc=s,_c=a,Lc=r,0!==(10256&n.subtreeFlags)||0!==(10256&n.flags)?(e.callbackNode=null,e.callbackPriority=0,Y(se,function(){return ou(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&n.flags),0!==(13878&n.subtreeFlags)||r){r=z.T,z.T=null,a=A.p,A.p=2,i=tc,tc|=4;try{!function(e,n){if(e=e.containerInfo,nd=tf,nr(e=er(e))){if("selectionStart"in e)var t={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(t=(t=e.ownerDocument)&&t.defaultView||window).getSelection&&t.getSelection();if(r&&0!==r.rangeCount){t=r.anchorNode;var a=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{t.nodeType,s.nodeType}catch(g){t=null;break e}var i=0,o=-1,c=-1,u=0,d=0,f=e,h=null;n:for(;;){for(var m;f!==t||0!==a&&3!==f.nodeType||(o=i+a),f!==s||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)h=f,f=m;for(;;){if(f===e)break n;if(h===t&&++u===a&&(o=i),h===s&&++d===r&&(c=i),null!==(m=f.nextSibling))break;h=(f=h).parentNode}f=m}t=-1===o||-1===c?null:{start:o,end:c}}else t=null}t=t||{start:0,end:0}}else t=null;for(td={focusedElem:e,selectionRange:t},tf=!1,So=n;null!==So;)if(e=(n=So).child,0!==(1024&n.subtreeFlags)&&null!==e)e.return=n,So=e;else for(;null!==So;){switch(s=(n=So).alternate,e=n.flags,n.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==s){e=void 0,t=n,a=s.memoizedProps,s=s.memoizedState,r=t.stateNode;try{var p=pi(t.type,a,(t.elementType,t.type));e=r.getSnapshotBeforeUpdate(p,s),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){uu(t,t.return,v)}}break;case 3:if(0!==(1024&e))if(9===(t=(e=n.stateNode.containerInfo).nodeType))pd(e);else if(1===t)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":pd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(l(163))}if(null!==(e=n.sibling)){e.return=n.return,So=e;break}So=n.return}}(e,n)}finally{tc=i,A.p=a,z.T=r}}Ec=1,ru(),au(),su()}}function ru(){if(1===Ec){Ec=0;var e=Nc,n=Cc,t=0!==(13878&n.flags);if(0!==(13878&n.subtreeFlags)||t){t=z.T,z.T=null;var r=A.p;A.p=2;var a=tc;tc|=4;try{zo(n,e);var s=td,l=er(e.containerInfo),i=s.focusedElem,o=s.selectionRange;if(l!==i&&i&&i.ownerDocument&&Zt(i.ownerDocument.documentElement,i)){if(null!==o&&nr(i)){var c=o.start,u=o.end;if(void 0===u&&(u=c),"selectionStart"in i)i.selectionStart=c,i.selectionEnd=Math.min(u,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var h=f.getSelection(),m=i.textContent.length,p=Math.min(o.start,m),g=void 0===o.end?p:Math.min(o.end,m);!h.extend&&p>g&&(l=g,g=p,p=l);var v=Jt(i,p),b=Jt(i,g);if(v&&b&&(1!==h.rangeCount||h.anchorNode!==v.node||h.anchorOffset!==v.offset||h.focusNode!==b.node||h.focusOffset!==b.offset)){var y=d.createRange();y.setStart(v.node,v.offset),h.removeAllRanges(),p>g?(h.addRange(y),h.extend(b.node,b.offset)):(y.setEnd(b.node,b.offset),h.addRange(y))}}}}for(d=[],h=i;h=h.parentNode;)1===h.nodeType&&d.push({element:h,left:h.scrollLeft,top:h.scrollTop});for("function"===typeof i.focus&&i.focus(),i=0;i<d.length;i++){var x=d[i];x.element.scrollLeft=x.left,x.element.scrollTop=x.top}}tf=!!nd,td=nd=null}finally{tc=a,A.p=r,z.T=t}}e.current=n,Ec=2}}function au(){if(2===Ec){Ec=0;var e=Nc,n=Cc,t=0!==(8772&n.flags);if(0!==(8772&n.subtreeFlags)||t){t=z.T,z.T=null;var r=A.p;A.p=2;var a=tc;tc|=4;try{ko(e,n.alternate,n)}finally{tc=a,A.p=r,z.T=t}}Ec=3}}function su(){if(4===Ec||3===Ec){Ec=0,ee();var e=Nc,n=Cc,t=Tc,r=Lc;0!==(10256&n.subtreeFlags)||0!==(10256&n.flags)?Ec=5:(Ec=0,Cc=Nc=null,lu(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(kc=null),Pe(t),n=n.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,n,void 0,128===(128&n.current.flags))}catch(o){}if(null!==r){n=z.T,a=A.p,A.p=2,z.T=null;try{for(var s=e.onRecoverableError,l=0;l<r.length;l++){var i=r[l];s(i.value,{componentStack:i.stack})}}finally{z.T=n,A.p=a}}0!==(3&Tc)&&iu(),wu(e),a=e.pendingLanes,0!==(4194090&t)&&0!==(42&a)?e===Oc?Rc++:(Rc=0,Oc=e):Rc=0,Su(0,!1)}}function lu(e,n){0===(e.pooledCacheLanes&=n)&&(null!=(n=e.pooledCache)&&(e.pooledCache=null,za(n)))}function iu(e){return ru(),au(),su(),ou()}function ou(){if(5!==Ec)return!1;var e=Nc,n=Pc;Pc=0;var t=Pe(Tc),r=z.T,a=A.p;try{A.p=32>t?32:t,z.T=null,t=_c,_c=null;var s=Nc,i=Tc;if(Ec=0,Cc=Nc=null,Tc=0,0!==(6&tc))throw Error(l(331));var o=tc;if(tc|=4,Yo(s.current),Ho(s,s.current,i,t),tc=o,Su(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,s)}catch(c){}return!0}finally{A.p=a,z.T=r,lu(e,n)}}function cu(e,n,t){n=kr(t,n),null!==(e=ss(e,n=wi(e.stateNode,n,2),2))&&(Ee(e,2),wu(e))}function uu(e,n,t){if(3===e.tag)cu(e,e,t);else for(;null!==n;){if(3===n.tag){cu(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===kc||!kc.has(r))){e=kr(t,e),null!==(r=ss(n,t=Si(2),2))&&(ki(t,r,n,e),Ee(r,2),wu(r));break}}n=n.return}}function du(e,n,t){var r=e.pingCache;if(null===r){r=e.pingCache=new nc;var a=new Set;r.set(n,a)}else void 0===(a=r.get(n))&&(a=new Set,r.set(n,a));a.has(t)||(uc=!0,a.add(t),e=fu.bind(null,e,n,t),n.then(e,e))}function fu(e,n,t){var r=e.pingCache;null!==r&&r.delete(n),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,rc===e&&(sc&t)===t&&(4===fc||3===fc&&(62914560&sc)===sc&&300>ne()-jc?0===(2&tc)&&Hc(e,0):pc|=t,vc===sc&&(vc=0)),wu(e)}function hu(e,n){0===n&&(n=Se()),null!==(e=Lr(e,n))&&(Ee(e,n),wu(e))}function mu(e){var n=e.memoizedState,t=0;null!==n&&(t=n.retryLane),hu(e,t)}function pu(e,n){var t=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(t=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(l(314))}null!==r&&r.delete(n),hu(e,t)}var gu=null,vu=null,bu=!1,yu=!1,xu=!1,ju=0;function wu(e){e!==vu&&null===e.next&&(null===vu?gu=vu=e:vu=vu.next=e),yu=!0,bu||(bu=!0,dd(function(){0!==(6&tc)?Y(re,ku):Eu()}))}function Su(e,n){if(!xu&&yu){xu=!0;do{for(var t=!1,r=gu;null!==r;){if(!n)if(0!==e){var a=r.pendingLanes;if(0===a)var s=0;else{var l=r.suspendedLanes,i=r.pingedLanes;s=(1<<31-he(42|e)+1)-1,s=201326741&(s&=a&~(l&~i))?201326741&s|1:s?2|s:0}0!==s&&(t=!0,Tu(r,s))}else s=sc,0===(3&(s=ye(r,r===rc?s:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||xe(r,s)||(t=!0,Tu(r,s));r=r.next}}while(t);xu=!1}}function ku(){Eu()}function Eu(){yu=bu=!1;var e=0;0!==ju&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==id&&(id=e,!0);return id=null,!1}()&&(e=ju),ju=0);for(var n=ne(),t=null,r=gu;null!==r;){var a=r.next,s=Nu(r,n);0===s?(r.next=null,null===t?gu=a:t.next=a,null===a&&(vu=t)):(t=r,(0!==e||0!==(3&s))&&(yu=!0)),r=a}Su(e,!1)}function Nu(e,n){for(var t=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,s=-62914561&e.pendingLanes;0<s;){var l=31-he(s),i=1<<l,o=a[l];-1===o?0!==(i&t)&&0===(i&r)||(a[l]=je(i,n)):o<=n&&(e.expiredLanes|=i),s&=~i}if(t=sc,t=ye(e,e===(n=rc)?t:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===t||e===n&&(2===lc||9===lc)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&J(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&t)||xe(e,t)){if((n=t&-t)===e.callbackPriority)return n;switch(null!==r&&J(r),Pe(t)){case 2:case 8:t=ae;break;case 32:default:t=se;break;case 268435456:t=ie}return r=Cu.bind(null,e),t=Y(t,r),e.callbackPriority=n,e.callbackNode=t,n}return null!==r&&null!==r&&J(r),e.callbackPriority=2,e.callbackNode=null,2}function Cu(e,n){if(0!==Ec&&5!==Ec)return e.callbackNode=null,e.callbackPriority=0,null;var t=e.callbackNode;if(iu()&&e.callbackNode!==t)return null;var r=sc;return 0===(r=ye(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Ic(e,r,n),Nu(e,ne()),null!=e.callbackNode&&e.callbackNode===t?Cu.bind(null,e):null)}function Tu(e,n){if(iu())return null;Ic(e,n,!0)}function Pu(){return 0===ju&&(ju=we()),ju}function _u(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:_n(""+e)}function Lu(e,n){var t=n.ownerDocument.createElement("input");return t.name=n.name,t.value=n.value,e.id&&t.setAttribute("form",e.id),n.parentNode.insertBefore(t,n),e=new FormData(e),t.parentNode.removeChild(t),e}for(var Ru=0;Ru<jr.length;Ru++){var Ou=jr[Ru];wr(Ou.toLowerCase(),"on"+(Ou[0].toUpperCase()+Ou.slice(1)))}wr(hr,"onAnimationEnd"),wr(mr,"onAnimationIteration"),wr(pr,"onAnimationStart"),wr("dblclick","onDoubleClick"),wr("focusin","onFocus"),wr("focusout","onBlur"),wr(gr,"onTransitionRun"),wr(vr,"onTransitionStart"),wr(br,"onTransitionCancel"),wr(yr,"onTransitionEnd"),Ge("onMouseEnter",["mouseout","mouseover"]),Ge("onMouseLeave",["mouseout","mouseover"]),Ge("onPointerEnter",["pointerout","pointerover"]),Ge("onPointerLeave",["pointerout","pointerover"]),Qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Au=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zu));function Du(e,n){n=0!==(4&n);for(var t=0;t<e.length;t++){var r=e[t],a=r.event;r=r.listeners;e:{var s=void 0;if(n)for(var l=r.length-1;0<=l;l--){var i=r[l],o=i.instance,c=i.currentTarget;if(i=i.listener,o!==s&&a.isPropagationStopped())break e;s=i,a.currentTarget=c;try{s(a)}catch(u){gi(u)}a.currentTarget=null,s=o}else for(l=0;l<r.length;l++){if(o=(i=r[l]).instance,c=i.currentTarget,i=i.listener,o!==s&&a.isPropagationStopped())break e;s=i,a.currentTarget=c;try{s(a)}catch(u){gi(u)}a.currentTarget=null,s=o}}}}function Iu(e,n){var t=n[Ae];void 0===t&&(t=n[Ae]=new Set);var r=e+"__bubble";t.has(r)||(Uu(n,e,2,!1),t.add(r))}function Mu(e,n,t){var r=0;n&&(r|=4),Uu(t,e,r,n)}var $u="_reactListening"+Math.random().toString(36).slice(2);function Fu(e){if(!e[$u]){e[$u]=!0,qe.forEach(function(n){"selectionchange"!==n&&(Au.has(n)||Mu(n,!1,e),Mu(n,!0,e))});var n=9===e.nodeType?e:e.ownerDocument;null===n||n[$u]||(n[$u]=!0,Mu("selectionchange",!1,n))}}function Uu(e,n,t,r){switch(uf(n)){case 2:var a=rf;break;case 8:a=af;break;default:a=sf}t=a.bind(null,n,t,e),a=void 0,!Fn||"touchstart"!==n&&"touchmove"!==n&&"wheel"!==n||(a=!0),r?void 0!==a?e.addEventListener(n,t,{capture:!0,passive:a}):e.addEventListener(n,t,!0):void 0!==a?e.addEventListener(n,t,{passive:a}):e.addEventListener(n,t,!1)}function Bu(e,n,t,r,a){var s=r;if(0===(1&n)&&0===(2&n)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a)break;if(4===l)for(l=r.return;null!==l;){var c=l.tag;if((3===c||4===c)&&l.stateNode.containerInfo===a)return;l=l.return}for(;null!==i;){if(null===(l=Ue(i)))return;if(5===(c=l.tag)||6===c||26===c||27===c){r=s=l;continue e}i=i.parentNode}}r=r.return}In(function(){var r=s,a=Rn(t),l=[];e:{var i=xr.get(e);if(void 0!==i){var c=et,u=e;switch(e){case"keypress":if(0===qn(t))break e;case"keydown":case"keyup":c=pt;break;case"focusin":u="focus",c=lt;break;case"focusout":u="blur",c=lt;break;case"beforeblur":case"afterblur":c=lt;break;case"click":if(2===t.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=at;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=st;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=vt;break;case hr:case mr:case pr:c=it;break;case yr:c=bt;break;case"scroll":case"scrollend":c=tt;break;case"wheel":c=yt;break;case"copy":case"cut":case"paste":c=ot;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gt;break;case"toggle":case"beforetoggle":c=xt}var d=0!==(4&n),f=!d&&("scroll"===e||"scrollend"===e),h=d?null!==i?i+"Capture":null:i;d=[];for(var m,p=r;null!==p;){var g=p;if(m=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===m||null===h||null!=(g=Mn(p,h))&&d.push(Hu(p,g,m)),f)break;p=p.return}0<d.length&&(i=new c(i,u,null,t,a),l.push({event:i,listeners:d}))}}if(0===(7&n)){if(c="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||t===Ln||!(u=t.relatedTarget||t.fromElement)||!Ue(u)&&!u[ze])&&(c||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,c?(c=r,null!==(u=(u=t.relatedTarget||t.toElement)?Ue(u):null)&&(f=o(u),d=u.tag,u!==f||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=at,g="onMouseLeave",h="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(d=gt,g="onPointerLeave",h="onPointerEnter",p="pointer"),f=null==c?i:He(c),m=null==u?i:He(u),(i=new d(g,p+"leave",c,t,a)).target=f,i.relatedTarget=m,g=null,Ue(a)===r&&((d=new d(h,p+"enter",u,t,a)).target=m,d.relatedTarget=f,g=d),f=g,c&&u)e:{for(h=u,p=0,m=d=c;m;m=Wu(m))p++;for(m=0,g=h;g;g=Wu(g))m++;for(;0<p-m;)d=Wu(d),p--;for(;0<m-p;)h=Wu(h),m--;for(;p--;){if(d===h||null!==h&&d===h.alternate)break e;d=Wu(d),h=Wu(h)}d=null}else d=null;null!==c&&qu(l,i,c,d,!1),null!==u&&null!==f&&qu(l,f,u,d,!0)}if("select"===(c=(i=r?He(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===c&&"file"===i.type)var v=Mt;else if(Rt(i))if($t)v=Qt;else{v=qt;var b=Wt}else!(c=i.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&Cn(r.elementType)&&(v=Mt):v=Kt;switch(v&&(v=v(e,r))?Ot(l,v,t,a):(b&&b(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&yn(i,"number",i.value)),b=r?He(r):window,e){case"focusin":(Rt(b)||"true"===b.contentEditable)&&(rr=b,ar=r,sr=null);break;case"focusout":sr=ar=rr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,ir(l,t,a);break;case"selectionchange":if(tr)break;case"keydown":case"keyup":ir(l,t,a)}var y;if(wt)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else _t?Tt(e,t)&&(x="onCompositionEnd"):"keydown"===e&&229===t.keyCode&&(x="onCompositionStart");x&&(Et&&"ko"!==t.locale&&(_t||"onCompositionStart"!==x?"onCompositionEnd"===x&&_t&&(y=Wn()):(Hn="value"in(Bn=a)?Bn.value:Bn.textContent,_t=!0)),0<(b=Vu(r,x)).length&&(x=new ct(x,e,null,t,a),l.push({event:x,listeners:b}),y?x.data=y:null!==(y=Pt(t))&&(x.data=y))),(y=kt?function(e,n){switch(e){case"compositionend":return Pt(n);case"keypress":return 32!==n.which?null:(Ct=!0,Nt);case"textInput":return(e=n.data)===Nt&&Ct?null:e;default:return null}}(e,t):function(e,n){if(_t)return"compositionend"===e||!wt&&Tt(e,n)?(e=Wn(),Vn=Hn=Bn=null,_t=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Et&&"ko"!==n.locale?null:n.data}}(e,t))&&(0<(x=Vu(r,"onBeforeInput")).length&&(b=new ct("onBeforeInput","beforeinput",null,t,a),l.push({event:b,listeners:x}),b.data=y)),function(e,n,t,r,a){if("submit"===n&&t&&t.stateNode===a){var s=_u((a[Oe]||null).action),l=r.submitter;l&&null!==(n=(n=l[Oe]||null)?_u(n.formAction):l.getAttribute("formAction"))&&(s=n,l=null);var i=new et("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==ju){var e=l?Lu(a,l):new FormData(a);_l(t,{pending:!0,data:e,method:a.method,action:s},null,e)}}else"function"===typeof s&&(i.preventDefault(),e=l?Lu(a,l):new FormData(a),_l(t,{pending:!0,data:e,method:a.method,action:s},s,e))},currentTarget:a}]})}}(l,e,r,t,a)}Du(l,n)})}function Hu(e,n,t){return{instance:e,listener:n,currentTarget:t}}function Vu(e,n){for(var t=n+"Capture",r=[];null!==e;){var a=e,s=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===s||(null!=(a=Mn(e,t))&&r.unshift(Hu(e,a,s)),null!=(a=Mn(e,n))&&r.push(Hu(e,a,s))),3===e.tag)return r;e=e.return}return[]}function Wu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qu(e,n,t,r,a){for(var s=n._reactName,l=[];null!==t&&t!==r;){var i=t,o=i.alternate,c=i.stateNode;if(i=i.tag,null!==o&&o===r)break;5!==i&&26!==i&&27!==i||null===c||(o=c,a?null!=(c=Mn(t,s))&&l.unshift(Hu(t,c,o)):a||null!=(c=Mn(t,s))&&l.push(Hu(t,c,o))),t=t.return}0!==l.length&&e.push({event:n,listeners:l})}var Ku=/\r\n?/g,Qu=/\u0000|\uFFFD/g;function Gu(e){return("string"===typeof e?e:""+e).replace(Ku,"\n").replace(Qu,"")}function Xu(e,n){return n=Gu(n),Gu(e)===n}function Yu(){}function Ju(e,n,t,r,a,s){switch(t){case"children":"string"===typeof r?"body"===n||"textarea"===n&&""===r||Sn(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==n&&Sn(e,""+r);break;case"className":tn(e,"class",r);break;case"tabIndex":tn(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":tn(e,t,r);break;case"style":Nn(e,r,s);break;case"data":if("object"!==n){tn(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==n||"href"!==t)){e.removeAttribute(t);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(t);break}r=_n(""+r),e.setAttribute(t,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(t,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof s&&("formAction"===t?("input"!==n&&Ju(e,n,"name",a.name,a,null),Ju(e,n,"formEncType",a.formEncType,a,null),Ju(e,n,"formMethod",a.formMethod,a,null),Ju(e,n,"formTarget",a.formTarget,a,null)):(Ju(e,n,"encType",a.encType,a,null),Ju(e,n,"method",a.method,a,null),Ju(e,n,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(t);break}r=_n(""+r),e.setAttribute(t,r);break;case"onClick":null!=r&&(e.onclick=Yu);break;case"onScroll":null!=r&&Iu("scroll",e);break;case"onScrollEnd":null!=r&&Iu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(t=r.__html)){if(null!=a.children)throw Error(l(60));e.innerHTML=t}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}t=_n(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,""+r):e.removeAttribute(t);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,""):e.removeAttribute(t);break;case"capture":case"download":!0===r?e.setAttribute(t,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,r):e.removeAttribute(t);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(t,r):e.removeAttribute(t);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(t):e.setAttribute(t,r);break;case"popover":Iu("beforetoggle",e),Iu("toggle",e),nn(e,"popover",r);break;case"xlinkActuate":rn(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rn(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rn(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rn(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rn(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rn(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rn(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":nn(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&nn(e,t=Tn.get(t)||t,r)}}function Zu(e,n,t,r,a,s){switch(t){case"style":Nn(e,r,s);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(t=r.__html)){if(null!=a.children)throw Error(l(60));e.innerHTML=t}}break;case"children":"string"===typeof r?Sn(e,r):("number"===typeof r||"bigint"===typeof r)&&Sn(e,""+r);break;case"onScroll":null!=r&&Iu("scroll",e);break;case"onScrollEnd":null!=r&&Iu("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Yu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(t)||("o"!==t[0]||"n"!==t[1]||(a=t.endsWith("Capture"),n=t.slice(2,a?t.length-7:void 0),"function"===typeof(s=null!=(s=e[Oe]||null)?s[t]:null)&&e.removeEventListener(n,s,a),"function"!==typeof r)?t in e?e[t]=r:!0===r?e.setAttribute(t,""):nn(e,t,r):("function"!==typeof s&&null!==s&&(t in e?e[t]=null:e.hasAttribute(t)&&e.removeAttribute(t)),e.addEventListener(n,r,a)))}}function ed(e,n,t){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Iu("error",e),Iu("load",e);var r,a=!1,s=!1;for(r in t)if(t.hasOwnProperty(r)){var i=t[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,n));default:Ju(e,n,r,i,t,null)}}return s&&Ju(e,n,"srcSet",t.srcSet,t,null),void(a&&Ju(e,n,"src",t.src,t,null));case"input":Iu("invalid",e);var o=r=i=s=null,c=null,u=null;for(a in t)if(t.hasOwnProperty(a)){var d=t[a];if(null!=d)switch(a){case"name":s=d;break;case"type":i=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(l(137,n));break;default:Ju(e,n,a,d,t,null)}}return bn(e,r,o,c,u,i,s,!1),void fn(e);case"select":for(s in Iu("invalid",e),a=i=r=null,t)if(t.hasOwnProperty(s)&&null!=(o=t[s]))switch(s){case"value":r=o;break;case"defaultValue":i=o;break;case"multiple":a=o;default:Ju(e,n,s,o,t,null)}return n=r,t=i,e.multiple=!!a,void(null!=n?xn(e,!!a,n,!1):null!=t&&xn(e,!!a,t,!0));case"textarea":for(i in Iu("invalid",e),r=s=a=null,t)if(t.hasOwnProperty(i)&&null!=(o=t[i]))switch(i){case"value":a=o;break;case"defaultValue":s=o;break;case"children":r=o;break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(l(91));break;default:Ju(e,n,i,o,t,null)}return wn(e,a,s,r),void fn(e);case"option":for(c in t)if(t.hasOwnProperty(c)&&null!=(a=t[c]))if("selected"===c)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Ju(e,n,c,a,t,null);return;case"dialog":Iu("beforetoggle",e),Iu("toggle",e),Iu("cancel",e),Iu("close",e);break;case"iframe":case"object":Iu("load",e);break;case"video":case"audio":for(a=0;a<zu.length;a++)Iu(zu[a],e);break;case"image":Iu("error",e),Iu("load",e);break;case"details":Iu("toggle",e);break;case"embed":case"source":case"link":Iu("error",e),Iu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in t)if(t.hasOwnProperty(u)&&null!=(a=t[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,n));default:Ju(e,n,u,a,t,null)}return;default:if(Cn(n)){for(d in t)t.hasOwnProperty(d)&&(void 0!==(a=t[d])&&Zu(e,n,d,a,t,void 0));return}}for(o in t)t.hasOwnProperty(o)&&(null!=(a=t[o])&&Ju(e,n,o,a,t,null))}var nd=null,td=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sd(e,n){if(0===e)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===n?0:e}function ld(e,n){return"textarea"===e||"noscript"===e||"string"===typeof n.children||"number"===typeof n.children||"bigint"===typeof n.children||"object"===typeof n.dangerouslySetInnerHTML&&null!==n.dangerouslySetInnerHTML&&null!=n.dangerouslySetInnerHTML.__html}var id=null;var od="function"===typeof setTimeout?setTimeout:void 0,cd="function"===typeof clearTimeout?clearTimeout:void 0,ud="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ud?function(e){return ud.resolve(null).then(e).catch(fd)}:od;function fd(e){setTimeout(function(){throw e})}function hd(e){return"head"===e}function md(e,n){var t=n,r=0,a=0;do{var s=t.nextSibling;if(e.removeChild(t),s&&8===s.nodeType)if("/$"===(t=s.data)){if(0<r&&8>r){t=r;var l=e.ownerDocument;if(1&t&&jd(l.documentElement),2&t&&jd(l.body),4&t)for(jd(t=l.head),l=t.firstChild;l;){var i=l.nextSibling,o=l.nodeName;l[$e]||"SCRIPT"===o||"STYLE"===o||"LINK"===o&&"stylesheet"===l.rel.toLowerCase()||t.removeChild(l),l=i}}if(0===a)return e.removeChild(s),void Tf(n);a--}else"$"===t||"$?"===t||"$!"===t?a++:r=t.charCodeAt(0)-48;else r=0;t=s}while(t);Tf(n)}function pd(e){var n=e.firstChild;for(n&&10===n.nodeType&&(n=n.nextSibling);n;){var t=n;switch(n=n.nextSibling,t.nodeName){case"HTML":case"HEAD":case"BODY":pd(t),Fe(t);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===t.rel.toLowerCase())continue}e.removeChild(t)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var n=e.nodeType;if(1===n||3===n)break;if(8===n){if("$"===(n=e.data)||"$!"===n||"$?"===n||"F!"===n||"F"===n)break;if("/$"===n)return null}}return e}var bd=null;function yd(e){e=e.previousSibling;for(var n=0;e;){if(8===e.nodeType){var t=e.data;if("$"===t||"$!"===t||"$?"===t){if(0===n)return e;n--}else"/$"===t&&n++}e=e.previousSibling}return null}function xd(e,n,t){switch(n=rd(t),e){case"html":if(!(e=n.documentElement))throw Error(l(452));return e;case"head":if(!(e=n.head))throw Error(l(453));return e;case"body":if(!(e=n.body))throw Error(l(454));return e;default:throw Error(l(451))}}function jd(e){for(var n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Fe(e)}var wd=new Map,Sd=new Set;function kd(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Ed=A.d;A.d={f:function(){var e=Ed.f(),n=Uc();return e||n},r:function(e){var n=Be(e);null!==n&&5===n.tag&&"form"===n.type?Rl(n):Ed.r(e)},D:function(e){Ed.D(e),Cd("dns-prefetch",e,null)},C:function(e,n){Ed.C(e,n),Cd("preconnect",e,n)},L:function(e,n,t){Ed.L(e,n,t);var r=Nd;if(r&&e&&n){var a='link[rel="preload"][as="'+gn(n)+'"]';"image"===n&&t&&t.imageSrcSet?(a+='[imagesrcset="'+gn(t.imageSrcSet)+'"]',"string"===typeof t.imageSizes&&(a+='[imagesizes="'+gn(t.imageSizes)+'"]')):a+='[href="'+gn(e)+'"]';var s=a;switch(n){case"style":s=Pd(e);break;case"script":s=Rd(e)}wd.has(s)||(e=f({rel:"preload",href:"image"===n&&t&&t.imageSrcSet?void 0:e,as:n},t),wd.set(s,e),null!==r.querySelector(a)||"style"===n&&r.querySelector(_d(s))||"script"===n&&r.querySelector(Od(s))||(ed(n=r.createElement("link"),"link",e),We(n),r.head.appendChild(n)))}},m:function(e,n){Ed.m(e,n);var t=Nd;if(t&&e){var r=n&&"string"===typeof n.as?n.as:"script",a='link[rel="modulepreload"][as="'+gn(r)+'"][href="'+gn(e)+'"]',s=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Rd(e)}if(!wd.has(s)&&(e=f({rel:"modulepreload",href:e},n),wd.set(s,e),null===t.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(t.querySelector(Od(s)))return}ed(r=t.createElement("link"),"link",e),We(r),t.head.appendChild(r)}}},X:function(e,n){Ed.X(e,n);var t=Nd;if(t&&e){var r=Ve(t).hoistableScripts,a=Rd(e),s=r.get(a);s||((s=t.querySelector(Od(a)))||(e=f({src:e,async:!0},n),(n=wd.get(a))&&Id(e,n),We(s=t.createElement("script")),ed(s,"link",e),t.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},r.set(a,s))}},S:function(e,n,t){Ed.S(e,n,t);var r=Nd;if(r&&e){var a=Ve(r).hoistableStyles,s=Pd(e);n=n||"default";var l=a.get(s);if(!l){var i={loading:0,preload:null};if(l=r.querySelector(_d(s)))i.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":n},t),(t=wd.get(s))&&Dd(e,t);var o=l=r.createElement("link");We(o),ed(o,"link",e),o._p=new Promise(function(e,n){o.onload=e,o.onerror=n}),o.addEventListener("load",function(){i.loading|=1}),o.addEventListener("error",function(){i.loading|=2}),i.loading|=4,Ad(l,n,r)}l={type:"stylesheet",instance:l,count:1,state:i},a.set(s,l)}}},M:function(e,n){Ed.M(e,n);var t=Nd;if(t&&e){var r=Ve(t).hoistableScripts,a=Rd(e),s=r.get(a);s||((s=t.querySelector(Od(a)))||(e=f({src:e,async:!0,type:"module"},n),(n=wd.get(a))&&Id(e,n),We(s=t.createElement("script")),ed(s,"link",e),t.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},r.set(a,s))}}};var Nd="undefined"===typeof document?null:document;function Cd(e,n,t){var r=Nd;if(r&&"string"===typeof n&&n){var a=gn(n);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof t&&(a+='[crossorigin="'+t+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:t,href:n},null===r.querySelector(a)&&(ed(n=r.createElement("link"),"link",e),We(n),r.head.appendChild(n)))}}function Td(e,n,t,r){var a,s,i,o,c=(c=V.current)?kd(c):null;if(!c)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof t.precedence&&"string"===typeof t.href?(n=Pd(t.href),(r=(t=Ve(c).hoistableStyles).get(n))||(r={type:"style",instance:null,count:0,state:null},t.set(n,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===t.rel&&"string"===typeof t.href&&"string"===typeof t.precedence){e=Pd(t.href);var u=Ve(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(_d(e)))&&!u._p&&(d.instance=u,d.state.loading=5),wd.has(e)||(t={rel:"preload",as:"style",href:t.href,crossOrigin:t.crossOrigin,integrity:t.integrity,media:t.media,hrefLang:t.hrefLang,referrerPolicy:t.referrerPolicy},wd.set(e,t),u||(a=c,s=e,i=t,o=d.state,a.querySelector('link[rel="preload"][as="style"]['+s+"]")?o.loading=1:(s=a.createElement("link"),o.preload=s,s.addEventListener("load",function(){return o.loading|=1}),s.addEventListener("error",function(){return o.loading|=2}),ed(s,"link",i),We(s),a.head.appendChild(s))))),n&&null===r)throw Error(l(528,""));return d}if(n&&null!==r)throw Error(l(529,""));return null;case"script":return n=t.async,"string"===typeof(t=t.src)&&n&&"function"!==typeof n&&"symbol"!==typeof n?(n=Rd(t),(r=(t=Ve(c).hoistableScripts).get(n))||(r={type:"script",instance:null,count:0,state:null},t.set(n,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function Pd(e){return'href="'+gn(e)+'"'}function _d(e){return'link[rel="stylesheet"]['+e+"]"}function Ld(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Rd(e){return'[src="'+gn(e)+'"]'}function Od(e){return"script[async]"+e}function zd(e,n,t){if(n.count++,null===n.instance)switch(n.type){case"style":var r=e.querySelector('style[data-href~="'+gn(t.href)+'"]');if(r)return n.instance=r,We(r),r;var a=f({},t,{"data-href":t.href,"data-precedence":t.precedence,href:null,precedence:null});return We(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Ad(r,t.precedence,e),n.instance=r;case"stylesheet":a=Pd(t.href);var s=e.querySelector(_d(a));if(s)return n.state.loading|=4,n.instance=s,We(s),s;r=Ld(t),(a=wd.get(a))&&Dd(r,a),We(s=(e.ownerDocument||e).createElement("link"));var i=s;return i._p=new Promise(function(e,n){i.onload=e,i.onerror=n}),ed(s,"link",r),n.state.loading|=4,Ad(s,t.precedence,e),n.instance=s;case"script":return s=Rd(t.src),(a=e.querySelector(Od(s)))?(n.instance=a,We(a),a):(r=t,(a=wd.get(s))&&Id(r=f({},t),a),We(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),n.instance=a);case"void":return null;default:throw Error(l(443,n.type))}else"stylesheet"===n.type&&0===(4&n.state.loading)&&(r=n.instance,n.state.loading|=4,Ad(r,t.precedence,e));return n.instance}function Ad(e,n,t){for(var r=t.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,s=a,l=0;l<r.length;l++){var i=r[l];if(i.dataset.precedence===n)s=i;else if(s!==a)break}s?s.parentNode.insertBefore(e,s.nextSibling):(n=9===t.nodeType?t.head:t).insertBefore(e,n.firstChild)}function Dd(e,n){null==e.crossOrigin&&(e.crossOrigin=n.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),null==e.title&&(e.title=n.title)}function Id(e,n){null==e.crossOrigin&&(e.crossOrigin=n.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),null==e.integrity&&(e.integrity=n.integrity)}var Md=null;function $d(e,n,t){if(null===Md){var r=new Map,a=Md=new Map;a.set(t,r)}else(r=(a=Md).get(t))||(r=new Map,a.set(t,r));if(r.has(e))return r;for(r.set(e,null),t=t.getElementsByTagName(e),a=0;a<t.length;a++){var s=t[a];if(!(s[$e]||s[Re]||"link"===e&&"stylesheet"===s.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==s.namespaceURI){var l=s.getAttribute(n)||"";l=e+l;var i=r.get(l);i?i.push(s):r.set(l,[s])}}return r}function Fd(e,n,t){(e=e.ownerDocument||e).head.insertBefore(t,"title"===n?e.querySelector("head > title"):null)}function Ud(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Bd=null;function Hd(){}function Vd(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Wd=null;function qd(e,n){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Wd=new Map,n.forEach(Kd,e),Wd=null,Vd.call(e))}function Kd(e,n){if(!(4&n.state.loading)){var t=Wd.get(e);if(t)var r=t.get(null);else{t=new Map,Wd.set(e,t);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<a.length;s++){var l=a[s];"LINK"!==l.nodeName&&"not all"===l.getAttribute("media")||(t.set(l.dataset.precedence,l),r=l)}r&&t.set(null,r)}l=(a=n.instance).getAttribute("data-precedence"),(s=t.get(l)||r)===r&&t.set(null,a),t.set(l,a),this.count++,r=Vd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),s?s.parentNode.insertBefore(a,s.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),n.state.loading|=4}}var Qd={$$typeof:j,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Gd(e,n,t,r,a,s,l,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ke(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ke(0),this.hiddenUpdates=ke(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=s,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Xd(e,n,t,r,a,s,l,i,o,c,u,d){return e=new Gd(e,n,t,l,i,o,c,d),n=1,!0===s&&(n|=24),s=Dr(3,null,null,n),e.current=s,s.stateNode=e,(n=Oa()).refCount++,e.pooledCache=n,n.refCount++,s.memoizedState={element:r,isDehydrated:t,cache:n},ts(s),e}function Yd(e){return e?e=zr:zr}function Jd(e,n,t,r,a,s){a=Yd(a),null===r.context?r.context=a:r.pendingContext=a,(r=as(n)).payload={element:t},null!==(s=void 0===s?null:s)&&(r.callback=s),null!==(t=ss(e,r,n))&&(Dc(t,0,n),ls(t,e,n))}function Zd(e,n){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var t=e.retryLane;e.retryLane=0!==t&&t<n?t:n}}function ef(e,n){Zd(e,n),(e=e.alternate)&&Zd(e,n)}function nf(e){if(13===e.tag){var n=Lr(e,67108864);null!==n&&Dc(n,0,67108864),ef(e,67108864)}}var tf=!0;function rf(e,n,t,r){var a=z.T;z.T=null;var s=A.p;try{A.p=2,sf(e,n,t,r)}finally{A.p=s,z.T=a}}function af(e,n,t,r){var a=z.T;z.T=null;var s=A.p;try{A.p=8,sf(e,n,t,r)}finally{A.p=s,z.T=a}}function sf(e,n,t,r){if(tf){var a=lf(r);if(null===a)Bu(e,n,r,of,t),yf(e,r);else if(function(e,n,t,r,a){switch(n){case"focusin":return ff=xf(ff,e,n,t,r,a),!0;case"dragenter":return hf=xf(hf,e,n,t,r,a),!0;case"mouseover":return mf=xf(mf,e,n,t,r,a),!0;case"pointerover":var s=a.pointerId;return pf.set(s,xf(pf.get(s)||null,e,n,t,r,a)),!0;case"gotpointercapture":return s=a.pointerId,gf.set(s,xf(gf.get(s)||null,e,n,t,r,a)),!0}return!1}(a,e,n,t,r))r.stopPropagation();else if(yf(e,r),4&n&&-1<bf.indexOf(e)){for(;null!==a;){var s=Be(a);if(null!==s)switch(s.tag){case 3:if((s=s.stateNode).current.memoizedState.isDehydrated){var l=be(s.pendingLanes);if(0!==l){var i=s;for(i.pendingLanes|=2,i.entangledLanes|=2;l;){var o=1<<31-he(l);i.entanglements[1]|=o,l&=~o}wu(s),0===(6&tc)&&(wc=ne()+500,Su(0,!1))}}break;case 13:null!==(i=Lr(s,2))&&Dc(i,0,2),Uc(),ef(s,2)}if(null===(s=lf(r))&&Bu(e,n,r,of,t),s===a)break;a=s}null!==a&&r.stopPropagation()}else Bu(e,n,r,null,t)}}function lf(e){return cf(e=Rn(e))}var of=null;function cf(e){if(of=null,null!==(e=Ue(e))){var n=o(e);if(null===n)e=null;else{var t=n.tag;if(13===t){if(null!==(e=c(n)))return e;e=null}else if(3===t){if(n.stateNode.current.memoizedState.isDehydrated)return 3===n.tag?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return of=e,null}function uf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(te()){case re:return 2;case ae:return 8;case se:case le:return 32;case ie:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,hf=null,mf=null,pf=new Map,gf=new Map,vf=[],bf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function yf(e,n){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":hf=null;break;case"mouseover":case"mouseout":mf=null;break;case"pointerover":case"pointerout":pf.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(n.pointerId)}}function xf(e,n,t,r,a,s){return null===e||e.nativeEvent!==s?(e={blockedOn:n,domEventName:t,eventSystemFlags:r,nativeEvent:s,targetContainers:[a]},null!==n&&(null!==(n=Be(n))&&nf(n)),e):(e.eventSystemFlags|=r,n=e.targetContainers,null!==a&&-1===n.indexOf(a)&&n.push(a),e)}function jf(e){var n=Ue(e.target);if(null!==n){var t=o(n);if(null!==t)if(13===(n=t.tag)){if(null!==(n=c(t)))return e.blockedOn=n,void function(e,n){var t=A.p;try{return A.p=e,n()}finally{A.p=t}}(e.priority,function(){if(13===t.tag){var e=zc();e=Te(e);var n=Lr(t,e);null!==n&&Dc(n,0,e),ef(t,e)}})}else if(3===n&&t.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===t.tag?t.stateNode.containerInfo:null)}e.blockedOn=null}function wf(e){if(null!==e.blockedOn)return!1;for(var n=e.targetContainers;0<n.length;){var t=lf(e.nativeEvent);if(null!==t)return null!==(n=Be(t))&&nf(n),e.blockedOn=t,!1;var r=new(t=e.nativeEvent).constructor(t.type,t);Ln=r,t.target.dispatchEvent(r),Ln=null,n.shift()}return!0}function Sf(e,n,t){wf(e)&&t.delete(n)}function kf(){df=!1,null!==ff&&wf(ff)&&(ff=null),null!==hf&&wf(hf)&&(hf=null),null!==mf&&wf(mf)&&(mf=null),pf.forEach(Sf),gf.forEach(Sf)}function Ef(e,n){e.blockedOn===n&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,kf)))}var Nf=null;function Cf(e){Nf!==e&&(Nf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Nf===e&&(Nf=null);for(var n=0;n<e.length;n+=3){var t=e[n],r=e[n+1],a=e[n+2];if("function"!==typeof r){if(null===cf(r||t))continue;break}var s=Be(t);null!==s&&(e.splice(n,3),n-=3,_l(s,{pending:!0,data:a,method:t.method,action:r},r,a))}}))}function Tf(e){function n(n){return Ef(n,e)}null!==ff&&Ef(ff,e),null!==hf&&Ef(hf,e),null!==mf&&Ef(mf,e),pf.forEach(n),gf.forEach(n);for(var t=0;t<vf.length;t++){var r=vf[t];r.blockedOn===e&&(r.blockedOn=null)}for(;0<vf.length&&null===(t=vf[0]).blockedOn;)jf(t),null===t.blockedOn&&vf.shift();if(null!=(t=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<t.length;r+=3){var a=t[r],s=t[r+1],l=a[Oe]||null;if("function"===typeof s)l||Cf(t);else if(l){var i=null;if(s&&s.hasAttribute("formAction")){if(a=s,l=s[Oe]||null)i=l.formAction;else if(null!==cf(a))continue}else i=l.action;"function"===typeof i?t[r+1]=i:(t.splice(r,3),r-=3),Cf(t)}}}function Pf(e){this._internalRoot=e}function _f(e){this._internalRoot=e}_f.prototype.render=Pf.prototype.render=function(e){var n=this._internalRoot;if(null===n)throw Error(l(409));Jd(n.current,zc(),e,n,null,null)},_f.prototype.unmount=Pf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var n=e.containerInfo;Jd(e.current,2,null,e,null,null),Uc(),n[ze]=null}},_f.prototype.unstable_scheduleHydration=function(e){if(e){var n=_e();e={blockedOn:null,target:e,priority:n};for(var t=0;t<vf.length&&0!==n&&n<vf[t].priority;t++);vf.splice(t,0,e),0===t&&jf(e)}};var Lf=a.version;if("19.1.0"!==Lf)throw Error(l(527,Lf,"19.1.0"));A.findDOMNode=function(e){var n=e._reactInternals;if(void 0===n){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=function(e){var n=e.alternate;if(!n){if(null===(n=o(e)))throw Error(l(188));return n!==e?null:e}for(var t=e,r=n;;){var a=t.return;if(null===a)break;var s=a.alternate;if(null===s){if(null!==(r=a.return)){t=r;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===t)return u(a),e;if(s===r)return u(a),n;s=s.sibling}throw Error(l(188))}if(t.return!==r.return)t=a,r=s;else{for(var i=!1,c=a.child;c;){if(c===t){i=!0,t=a,r=s;break}if(c===r){i=!0,r=a,t=s;break}c=c.sibling}if(!i){for(c=s.child;c;){if(c===t){i=!0,t=s,r=a;break}if(c===r){i=!0,r=s,t=a;break}c=c.sibling}if(!i)throw Error(l(189))}}if(t.alternate!==r)throw Error(l(190))}if(3!==t.tag)throw Error(l(188));return t.stateNode.current===t?e:n}(n),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Rf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Of=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Of.isDisabled&&Of.supportsFiber)try{ue=Of.inject(Rf),de=Of}catch(Af){}}n.createRoot=function(e,n){if(!i(e))throw Error(l(299));var t=!1,r="",a=vi,s=bi,o=yi;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(t=!0),void 0!==n.identifierPrefix&&(r=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(o=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks),n=Xd(e,1,!1,null,0,t,r,a,s,o,0,null),e[ze]=n.current,Fu(e),new Pf(n)},n.hydrateRoot=function(e,n,t){if(!i(e))throw Error(l(299));var r=!1,a="",s=vi,o=bi,c=yi,u=null;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(a=t.identifierPrefix),void 0!==t.onUncaughtError&&(s=t.onUncaughtError),void 0!==t.onCaughtError&&(o=t.onCaughtError),void 0!==t.onRecoverableError&&(c=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks,void 0!==t.formState&&(u=t.formState)),(n=Xd(e,1,!0,n,0,r,a,s,o,c,0,u)).context=Yd(null),t=n.current,(a=as(r=Te(r=zc()))).callback=null,ss(t,a,r),t=r,n.current.lanes=t,Ee(n,t),wu(n),e[ze]=n.current,Fu(e),new _f(n)},n.version="19.1.0"},119:(e,n,t)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}(),e.exports=t(863)},241:e=>{"use strict";var n=function(){};e.exports=n},340:(e,n,t)=>{"use strict";e.exports=t(487)},352:(e,n,t)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}(),e.exports=t(85)},414:(e,n,t)=>{"use strict";e.exports=t(916)},487:(e,n)=>{"use strict";function t(e,n){var t=e.length;e.push(n);e:for(;0<t;){var r=t-1>>>1,a=e[r];if(!(0<s(a,n)))break e;e[r]=n,e[t]=a,t=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var n=e[0],t=e.pop();if(t!==n){e[0]=t;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,o=e[i],c=i+1,u=e[c];if(0>s(o,t))c<a&&0>s(u,o)?(e[r]=u,e[c]=t,r=c):(e[r]=o,e[i]=t,r=i);else{if(!(c<a&&0>s(u,t)))break e;e[r]=u,e[c]=t,r=c}}}return n}function s(e,n){var t=e.sortIndex-n.sortIndex;return 0!==t?t:e.id-n.id}if(n.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var l=performance;n.unstable_now=function(){return l.now()}}else{var i=Date,o=i.now();n.unstable_now=function(){return i.now()-o}}var c=[],u=[],d=1,f=null,h=3,m=!1,p=!1,g=!1,v=!1,b="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function j(e){for(var n=r(u);null!==n;){if(null===n.callback)a(u);else{if(!(n.startTime<=e))break;a(u),n.sortIndex=n.expirationTime,t(c,n)}n=r(u)}}function w(e){if(g=!1,j(e),!p)if(null!==r(c))p=!0,k||(k=!0,S());else{var n=r(u);null!==n&&R(w,n.startTime-e)}}var S,k=!1,E=-1,N=5,C=-1;function T(){return!!v||!(n.unstable_now()-C<N)}function P(){if(v=!1,k){var e=n.unstable_now();C=e;var t=!0;try{e:{p=!1,g&&(g=!1,y(E),E=-1),m=!0;var s=h;try{n:{for(j(e),f=r(c);null!==f&&!(f.expirationTime>e&&T());){var l=f.callback;if("function"===typeof l){f.callback=null,h=f.priorityLevel;var i=l(f.expirationTime<=e);if(e=n.unstable_now(),"function"===typeof i){f.callback=i,j(e),t=!0;break n}f===r(c)&&a(c),j(e)}else a(c);f=r(c)}if(null!==f)t=!0;else{var o=r(u);null!==o&&R(w,o.startTime-e),t=!1}}break e}finally{f=null,h=s,m=!1}t=void 0}}finally{t?S():k=!1}}}if("function"===typeof x)S=function(){x(P)};else if("undefined"!==typeof MessageChannel){var _=new MessageChannel,L=_.port2;_.port1.onmessage=P,S=function(){L.postMessage(null)}}else S=function(){b(P,0)};function R(e,t){E=b(function(){e(n.unstable_now())},t)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(e){e.callback=null},n.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},n.unstable_getCurrentPriorityLevel=function(){return h},n.unstable_next=function(e){switch(h){case 1:case 2:case 3:var n=3;break;default:n=h}var t=h;h=n;try{return e()}finally{h=t}},n.unstable_requestPaint=function(){v=!0},n.unstable_runWithPriority=function(e,n){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var t=h;h=e;try{return n()}finally{h=t}},n.unstable_scheduleCallback=function(e,a,s){var l=n.unstable_now();switch("object"===typeof s&&null!==s?s="number"===typeof(s=s.delay)&&0<s?l+s:l:s=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:s,expirationTime:i=s+i,sortIndex:-1},s>l?(e.sortIndex=s,t(u,e),null===r(c)&&e===r(u)&&(g?(y(E),E=-1):g=!0,R(w,s-l))):(e.sortIndex=i,t(c,e),p||m||(p=!0,k||(k=!0,S()))),e},n.unstable_shouldYield=T,n.unstable_wrapCallback=function(e){var n=h;return function(){var t=h;h=n;try{return e.apply(this,arguments)}finally{h=t}}}},488:(e,n,t)=>{"use strict";var r=t(959);function a(){}function s(){}s.resetWarningCache=a,e.exports=function(){function e(e,n,t,a,s,l){if(l!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function n(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:n,element:e,elementType:e,instanceOf:n,node:e,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:s,resetWarningCache:a};return t.PropTypes=t,t}},738:(e,n)=>{var t;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e="",n=0;n<arguments.length;n++){var t=arguments[n];t&&(e=l(e,s(t)))}return e}function s(e){if("string"===typeof e||"number"===typeof e)return e;if("object"!==typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var n="";for(var t in e)r.call(e,t)&&e[t]&&(n=l(n,t));return n}function l(e,n){return n?e?e+" "+n:e+n:e}e.exports?(a.default=a,e.exports=a):void 0===(t=function(){return a}.apply(n,[]))||(e.exports=t)}()},863:(e,n,t)=>{"use strict";var r=t(950);function a(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var l={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},i=Symbol.for("react.portal");var o=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,n){return"font"===e?"":"string"===typeof n?"use-credentials"===n?n:"":void 0}n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,n.createPortal=function(e,n){var t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(a(299));return function(e,n,t){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:n,implementation:t}}(e,n,null,t)},n.flushSync=function(e){var n=o.T,t=l.p;try{if(o.T=null,l.p=2,e)return e()}finally{o.T=n,l.p=t,l.d.f()}},n.preconnect=function(e,n){"string"===typeof e&&(n?n="string"===typeof(n=n.crossOrigin)?"use-credentials"===n?n:"":void 0:n=null,l.d.C(e,n))},n.prefetchDNS=function(e){"string"===typeof e&&l.d.D(e)},n.preinit=function(e,n){if("string"===typeof e&&n&&"string"===typeof n.as){var t=n.as,r=c(t,n.crossOrigin),a="string"===typeof n.integrity?n.integrity:void 0,s="string"===typeof n.fetchPriority?n.fetchPriority:void 0;"style"===t?l.d.S(e,"string"===typeof n.precedence?n.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:s}):"script"===t&&l.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:s,nonce:"string"===typeof n.nonce?n.nonce:void 0})}},n.preinitModule=function(e,n){if("string"===typeof e)if("object"===typeof n&&null!==n){if(null==n.as||"script"===n.as){var t=c(n.as,n.crossOrigin);l.d.M(e,{crossOrigin:t,integrity:"string"===typeof n.integrity?n.integrity:void 0,nonce:"string"===typeof n.nonce?n.nonce:void 0})}}else null==n&&l.d.M(e)},n.preload=function(e,n){if("string"===typeof e&&"object"===typeof n&&null!==n&&"string"===typeof n.as){var t=n.as,r=c(t,n.crossOrigin);l.d.L(e,t,{crossOrigin:r,integrity:"string"===typeof n.integrity?n.integrity:void 0,nonce:"string"===typeof n.nonce?n.nonce:void 0,type:"string"===typeof n.type?n.type:void 0,fetchPriority:"string"===typeof n.fetchPriority?n.fetchPriority:void 0,referrerPolicy:"string"===typeof n.referrerPolicy?n.referrerPolicy:void 0,imageSrcSet:"string"===typeof n.imageSrcSet?n.imageSrcSet:void 0,imageSizes:"string"===typeof n.imageSizes?n.imageSizes:void 0,media:"string"===typeof n.media?n.media:void 0})}},n.preloadModule=function(e,n){if("string"===typeof e)if(n){var t=c(n.as,n.crossOrigin);l.d.m(e,{as:"string"===typeof n.as&&"script"!==n.as?n.as:void 0,crossOrigin:t,integrity:"string"===typeof n.integrity?n.integrity:void 0})}else l.d.m(e)},n.requestFormReset=function(e){l.d.r(e)},n.unstable_batchedUpdates=function(e,n){return e(n)},n.useFormState=function(e,n,t){return o.H.useFormState(e,n,t)},n.useFormStatus=function(){return o.H.useHostTransitionStatus()},n.version="19.1.0"},916:(e,n)=>{"use strict";var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,n,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==n.key&&(a=""+n.key),"key"in n)for(var s in r={},n)"key"!==s&&(r[s]=n[s]);else r=n;return n=r.ref,{$$typeof:t,type:e,key:a,ref:void 0!==n?n:null,props:r}}n.Fragment=r,n.jsx=a,n.jsxs=a},942:(e,n,t)=>{e.exports=t(488)()},949:e=>{"use strict";e.exports=function(e,n,t,r,a,s,l,i){if(!e){var o;if(void 0===n)o=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[t,r,a,s,l,i],u=0;(o=new Error(n.replace(/%s/g,function(){return c[u++]}))).name="Invariant Violation"}throw o.framesToPop=1,o}}},950:(e,n,t)=>{"use strict";e.exports=t(983)},959:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},983:(e,n)=>{"use strict";var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),o=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},p=Object.assign,g={};function v(e,n,t){this.props=e,this.context=n,this.refs=g,this.updater=t||m}function b(){}function y(e,n,t){this.props=e,this.context=n,this.refs=g,this.updater=t||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,n){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var x=y.prototype=new b;x.constructor=y,p(x,v.prototype),x.isPureReactComponent=!0;var j=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function k(e,n,r,a,s,l){return r=l.ref,{$$typeof:t,type:e,key:n,ref:void 0!==r?r:null,props:l}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===t}var N=/\/+/g;function C(e,n){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var n={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return n[e]})}(""+e.key):n.toString(36)}function T(){}function P(e,n,a,s,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var o,c,u=!1;if(null===e)u=!0;else switch(i){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case t:case r:u=!0;break;case f:return P((u=e._init)(e._payload),n,a,s,l)}}if(u)return l=l(e),u=""===s?"."+C(e,0):s,j(l)?(a="",null!=u&&(a=u.replace(N,"$&/")+"/"),P(l,n,a,"",function(e){return e})):null!=l&&(E(l)&&(o=l,c=a+(null==l.key||e&&e.key===l.key?"":(""+l.key).replace(N,"$&/")+"/")+u,l=k(o.type,c,void 0,0,0,o.props)),n.push(l)),1;u=0;var d,m=""===s?".":s+":";if(j(e))for(var p=0;p<e.length;p++)u+=P(s=e[p],n,a,i=m+C(s,p),l);else if("function"===typeof(p=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=h&&d[h]||d["@@iterator"])?d:null))for(e=p.call(e),p=0;!(s=e.next()).done;)u+=P(s=s.value,n,a,i=m+C(s,p++),l);else if("object"===i){if("function"===typeof e.then)return P(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(T,T):(e.status="pending",e.then(function(n){"pending"===e.status&&(e.status="fulfilled",e.value=n)},function(n){"pending"===e.status&&(e.status="rejected",e.reason=n)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),n,a,s,l);throw n=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===n?"object with keys {"+Object.keys(e).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}return u}function _(e,n,t){if(null==e)return e;var r=[],a=0;return P(e,r,"","",function(e){return n.call(t,e,a++)}),r}function L(e){if(-1===e._status){var n=e._result;(n=n()).then(function(n){0!==e._status&&-1!==e._status||(e._status=1,e._result=n)},function(n){0!==e._status&&-1!==e._status||(e._status=2,e._result=n)}),-1===e._status&&(e._status=0,e._result=n)}if(1===e._status)return e._result.default;throw e._result}var R="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function O(){}n.Children={map:_,forEach:function(e,n,t){_(e,function(){n.apply(this,arguments)},t)},count:function(e){var n=0;return _(e,function(){n++}),n},toArray:function(e){return _(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},n.Component=v,n.Fragment=a,n.Profiler=l,n.PureComponent=y,n.StrictMode=s,n.Suspense=u,n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,n.__COMPILER_RUNTIME={__proto__:null,c:function(e){return w.H.useMemoCache(e)}},n.cache=function(e){return function(){return e.apply(null,arguments)}},n.cloneElement=function(e,n,t){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=p({},e.props),a=e.key;if(null!=n)for(s in void 0!==n.ref&&void 0,void 0!==n.key&&(a=""+n.key),n)!S.call(n,s)||"key"===s||"__self"===s||"__source"===s||"ref"===s&&void 0===n.ref||(r[s]=n[s]);var s=arguments.length-2;if(1===s)r.children=t;else if(1<s){for(var l=Array(s),i=0;i<s;i++)l[i]=arguments[i+2];r.children=l}return k(e.type,a,void 0,0,0,r)},n.createContext=function(e){return(e={$$typeof:o,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},n.createElement=function(e,n,t){var r,a={},s=null;if(null!=n)for(r in void 0!==n.key&&(s=""+n.key),n)S.call(n,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=n[r]);var l=arguments.length-2;if(1===l)a.children=t;else if(1<l){for(var i=Array(l),o=0;o<l;o++)i[o]=arguments[o+2];a.children=i}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===a[r]&&(a[r]=l[r]);return k(e,s,void 0,0,0,a)},n.createRef=function(){return{current:null}},n.forwardRef=function(e){return{$$typeof:c,render:e}},n.isValidElement=E,n.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},n.memo=function(e,n){return{$$typeof:d,type:e,compare:void 0===n?null:n}},n.startTransition=function(e){var n=w.T,t={};w.T=t;try{var r=e(),a=w.S;null!==a&&a(t,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(O,R)}catch(s){R(s)}finally{w.T=n}},n.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},n.use=function(e){return w.H.use(e)},n.useActionState=function(e,n,t){return w.H.useActionState(e,n,t)},n.useCallback=function(e,n){return w.H.useCallback(e,n)},n.useContext=function(e){return w.H.useContext(e)},n.useDebugValue=function(){},n.useDeferredValue=function(e,n){return w.H.useDeferredValue(e,n)},n.useEffect=function(e,n,t){var r=w.H;if("function"===typeof t)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,n)},n.useId=function(){return w.H.useId()},n.useImperativeHandle=function(e,n,t){return w.H.useImperativeHandle(e,n,t)},n.useInsertionEffect=function(e,n){return w.H.useInsertionEffect(e,n)},n.useLayoutEffect=function(e,n){return w.H.useLayoutEffect(e,n)},n.useMemo=function(e,n){return w.H.useMemo(e,n)},n.useOptimistic=function(e,n){return w.H.useOptimistic(e,n)},n.useReducer=function(e,n,t){return w.H.useReducer(e,n,t)},n.useRef=function(e){return w.H.useRef(e)},n.useState=function(e){return w.H.useState(e)},n.useSyncExternalStore=function(e,n,t){return w.H.useSyncExternalStore(e,n,t)},n.useTransition=function(){return w.H.useTransition()},n.version="19.1.0"}},n={};function t(r){var a=n[r];if(void 0!==a)return a.exports;var s=n[r]={exports:{}};return e[r](s,s.exports,t),s.exports}t.m=e,t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.f={},t.e=e=>Promise.all(Object.keys(t.f).reduce((n,r)=>(t.f[r](e,n),n),[])),t.u=e=>"static/js/"+e+"."+{206:"671b5d46",954:"3e3e3205"}[e]+".chunk.js",t.miniCssF=e=>{},t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),(()=>{var e={},n="xui-importer:";t.l=(r,a,s,l)=>{if(e[r])e[r].push(a);else{var i,o;if(void 0!==s)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==n+s){i=d;break}}i||(o=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,t.nc&&i.setAttribute("nonce",t.nc),i.setAttribute("data-webpack",n+s),i.src=r),e[r]=[a];var f=(n,t)=>{i.onerror=i.onload=null,clearTimeout(h);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach(e=>e(t)),n)return n(t)},h=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),o&&document.head.appendChild(i)}}})(),t.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.p="./",(()=>{t.b=document.baseURI||self.location.href;var e={792:0};t.f.j=(n,r)=>{var a=t.o(e,n)?e[n]:void 0;if(0!==a)if(a)r.push(a[2]);else{var s=new Promise((t,r)=>a=e[n]=[t,r]);r.push(a[2]=s);var l=t.p+t.u(n),i=new Error;t.l(l,r=>{if(t.o(e,n)&&(0!==(a=e[n])&&(e[n]=void 0),a)){var s=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;i.message="Loading chunk "+n+" failed.\n("+s+": "+l+")",i.name="ChunkLoadError",i.type=s,i.request=l,a[1](i)}},"chunk-"+n,n)}};var n=(n,r)=>{var a,s,l=r[0],i=r[1],o=r[2],c=0;if(l.some(n=>0!==e[n])){for(a in i)t.o(i,a)&&(t.m[a]=i[a]);if(o)o(t)}for(n&&n(r);c<l.length;c++)s=l[c],t.o(e,s)&&e[s]&&e[s][0](),e[s]=0},r=self.webpackChunkxui_importer=self.webpackChunkxui_importer||[];r.forEach(n.bind(null,0)),r.push=n.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e=t(950),n=t(352),r=t(414);const a=e=>{let{setCurrentPage:n,currentPage:t}=e;return(0,r.jsxs)("div",{className:"sidebar d-flex flex-column p-3 text-white",children:[(0,r.jsx)("h4",{className:"text-center mb-4",children:"\ud83c\udfaf RGS XUI"}),(0,r.jsx)("ul",{className:"list-unstyled",children:[{id:"dashboard",label:"\ud83d\udcca Dashboard"},{id:"connections",label:"\ud83d\udd17 Connections"},{id:"import-series",label:"\ud83d\udcfa Import Series M3U"},{id:"import-vod",label:"\ud83c\udfac Import VOD M3U"},{id:"history",label:"\ud83d\udcdc History"},{id:"profile",label:"\ud83d\udc64 Profile"}].map(e=>(0,r.jsx)("li",{className:"mb-2",children:(0,r.jsx)("button",{className:"btn w-100 text-start "+(t===e.id?"btn-warning":"btn-outline-light"),onClick:()=>n(e.id),children:e.label})},e.id))})]})};var s=t(738),l=t.n(s);const i=["xxl","xl","lg","md","sm","xs"],o="xs",c=e.createContext({prefixes:{},breakpoints:i,minBreakpoint:o}),{Consumer:u,Provider:d}=c;function f(n,t){const{prefixes:r}=(0,e.useContext)(c);return n||r[t]||t}function h(){const{breakpoints:n}=(0,e.useContext)(c);return n}function m(){const{minBreakpoint:n}=(0,e.useContext)(c);return n}const p=e.forwardRef((e,n)=>{let{bsPrefix:t,variant:a,animation:s="border",size:i,as:o="div",className:c,...u}=e;t=f(t,"spinner");const d=`${t}-${s}`;return(0,r.jsx)(o,{ref:n,...u,className:l()(c,d,i&&`${d}-${i}`,a&&`text-${a}`)})});p.displayName="Spinner";const g=p,v=["as","disabled"];function b(e){let{tagName:n,disabled:t,href:r,target:a,rel:s,role:l,onClick:i,tabIndex:o=0,type:c}=e;n||(n=null!=r||null!=a||null!=s?"a":"button");const u={tagName:n};if("button"===n)return[{type:c||"button",disabled:t},u];const d=e=>{(t||"a"===n&&function(e){return!e||"#"===e.trim()}(r))&&e.preventDefault(),t?e.stopPropagation():null==i||i(e)};return"a"===n&&(r||(r="#"),t&&(r=void 0)),[{role:null!=l?l:"button",disabled:void 0,tabIndex:t?void 0:o,href:r,target:"a"===n?a:void 0,"aria-disabled":t||void 0,rel:"a"===n?s:void 0,onClick:d,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),d(e))}},u]}const y=e.forwardRef((e,n)=>{let{as:t,disabled:a}=e,s=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,v);const[l,{tagName:i}]=b(Object.assign({tagName:t,disabled:a},s));return(0,r.jsx)(i,Object.assign({},s,l,{ref:n}))});y.displayName="Button";const x=y,j=e.forwardRef((e,n)=>{let{as:t,bsPrefix:a,variant:s="primary",size:i,active:o=!1,disabled:c=!1,className:u,...d}=e;const h=f(a,"btn"),[m,{tagName:p}]=b({tagName:t,disabled:c,...d}),g=p;return(0,r.jsx)(g,{...m,...d,ref:n,disabled:c,className:l()(u,h,o&&"active",s&&`${h}-${s}`,i&&`${h}-${i}`,d.href&&c&&"disabled")})});j.displayName="Button";const w=j;function S(){return S=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},S.apply(null,arguments)}function k(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==n.indexOf(r))continue;t[r]=e[r]}return t}t(949);function E(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function N(e){var n=function(e,n){if("object"!==typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!==typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"===typeof n?n:String(n)}function C(n,t){return Object.keys(t).reduce(function(r,a){var s,l=r,i=l[E(a)],o=l[a],c=k(l,[E(a),a].map(N)),u=t[a],d=function(n,t,r){var a=(0,e.useRef)(void 0!==n),s=(0,e.useState)(t),l=s[0],i=s[1],o=void 0!==n,c=a.current;return a.current=o,!o&&c&&l!==t&&i(t),[o?n:l,(0,e.useCallback)(function(e){for(var n=arguments.length,t=new Array(n>1?n-1:0),a=1;a<n;a++)t[a-1]=arguments[a];r&&r.apply(void 0,[e].concat(t)),i(e)},[r])]}(o,i,n[u]),f=d[0],h=d[1];return S({},c,((s={})[a]=f,s[u]=h,s))},n)}function T(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!==e&&void 0!==e&&this.setState(e)}function P(e){this.setState(function(n){var t=this.constructor.getDerivedStateFromProps(e,n);return null!==t&&void 0!==t?t:null}.bind(this))}function _(e,n){try{var t=this.props,r=this.state;this.props=e,this.state=n,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(t,r)}finally{this.props=t,this.state=r}}T.__suppressDeprecationWarning=!0,P.__suppressDeprecationWarning=!0,_.__suppressDeprecationWarning=!0;const L=function(n){const t=(0,e.useRef)(n);return(0,e.useEffect)(()=>{t.current=n},[n]),t};function R(n){const t=L(n);return(0,e.useCallback)(function(){return t.current&&t.current(...arguments)},[t])}const O=n=>e.forwardRef((e,t)=>(0,r.jsx)("div",{...e,ref:t,className:l()(e.className,n)})),z=O("h4");z.displayName="DivStyledAsH4";const A=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s=z,...i}=e;return a=f(a,"alert-heading"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});A.displayName="AlertHeading";const D=A;const I=function(n){const t=(0,e.useRef)(n);return(0,e.useEffect)(()=>{t.current=n},[n]),t};function M(n){const t=I(n);return(0,e.useCallback)(function(){return t.current&&t.current(...arguments)},[t])}const $="undefined"!==typeof t.g&&t.g.navigator&&"ReactNative"===t.g.navigator.product,F="undefined"!==typeof document||$?e.useLayoutEffect:e.useEffect;new WeakMap;const U=["onKeyDown"];const B=e.forwardRef((e,n)=>{let{onKeyDown:t}=e,a=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,U);const[s]=b(Object.assign({tagName:"a"},a)),l=M(e=>{s.onKeyDown(e),null==t||t(e)});return(i=a.href)&&"#"!==i.trim()&&"button"!==a.role?(0,r.jsx)("a",Object.assign({ref:n},a,{onKeyDown:t})):(0,r.jsx)("a",Object.assign({ref:n},a,s,{onKeyDown:l}));var i});B.displayName="Anchor";const H=B,V=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s=H,...i}=e;return a=f(a,"alert-link"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});V.displayName="AlertLink";const W=V;function q(e,n){return q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,n){return e.__proto__=n,e},q(e,n)}var K=t(119);const Q=!1,G=e.createContext(null);var X="unmounted",Y="exited",J="entering",Z="entered",ee="exiting",ne=function(n){var t,r;function a(e,t){var r;r=n.call(this,e,t)||this;var a,s=t&&!t.isMounting?e.enter:e.appear;return r.appearStatus=null,e.in?s?(a=Y,r.appearStatus=J):a=Z:a=e.unmountOnExit||e.mountOnEnter?X:Y,r.state={status:a},r.nextCallback=null,r}r=n,(t=a).prototype=Object.create(r.prototype),t.prototype.constructor=t,q(t,r),a.getDerivedStateFromProps=function(e,n){return e.in&&n.status===X?{status:Y}:null};var s=a.prototype;return s.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},s.componentDidUpdate=function(e){var n=null;if(e!==this.props){var t=this.state.status;this.props.in?t!==J&&t!==Z&&(n=J):t!==J&&t!==Z||(n=ee)}this.updateStatus(!1,n)},s.componentWillUnmount=function(){this.cancelNextCallback()},s.getTimeouts=function(){var e,n,t,r=this.props.timeout;return e=n=t=r,null!=r&&"number"!==typeof r&&(e=r.exit,n=r.enter,t=void 0!==r.appear?r.appear:n),{exit:e,enter:n,appear:t}},s.updateStatus=function(e,n){if(void 0===e&&(e=!1),null!==n)if(this.cancelNextCallback(),n===J){if(this.props.unmountOnExit||this.props.mountOnEnter){var t=this.props.nodeRef?this.props.nodeRef.current:K.findDOMNode(this);t&&function(e){e.scrollTop}(t)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Y&&this.setState({status:X})},s.performEnter=function(e){var n=this,t=this.props.enter,r=this.context?this.context.isMounting:e,a=this.props.nodeRef?[r]:[K.findDOMNode(this),r],s=a[0],l=a[1],i=this.getTimeouts(),o=r?i.appear:i.enter;!e&&!t||Q?this.safeSetState({status:Z},function(){n.props.onEntered(s)}):(this.props.onEnter(s,l),this.safeSetState({status:J},function(){n.props.onEntering(s,l),n.onTransitionEnd(o,function(){n.safeSetState({status:Z},function(){n.props.onEntered(s,l)})})}))},s.performExit=function(){var e=this,n=this.props.exit,t=this.getTimeouts(),r=this.props.nodeRef?void 0:K.findDOMNode(this);n&&!Q?(this.props.onExit(r),this.safeSetState({status:ee},function(){e.props.onExiting(r),e.onTransitionEnd(t.exit,function(){e.safeSetState({status:Y},function(){e.props.onExited(r)})})})):this.safeSetState({status:Y},function(){e.props.onExited(r)})},s.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},s.safeSetState=function(e,n){n=this.setNextCallback(n),this.setState(e,n)},s.setNextCallback=function(e){var n=this,t=!0;return this.nextCallback=function(r){t&&(t=!1,n.nextCallback=null,e(r))},this.nextCallback.cancel=function(){t=!1},this.nextCallback},s.onTransitionEnd=function(e,n){this.setNextCallback(n);var t=this.props.nodeRef?this.props.nodeRef.current:K.findDOMNode(this),r=null==e&&!this.props.addEndListener;if(t&&!r){if(this.props.addEndListener){var a=this.props.nodeRef?[this.nextCallback]:[t,this.nextCallback],s=a[0],l=a[1];this.props.addEndListener(s,l)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},s.render=function(){var n=this.state.status;if(n===X)return null;var t=this.props,r=t.children,a=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,k(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return e.createElement(G.Provider,{value:null},"function"===typeof r?r(n,a):e.cloneElement(e.Children.only(r),a))},a}(e.Component);function te(){}ne.contextType=G,ne.propTypes={},ne.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:te,onEntering:te,onEntered:te,onExit:te,onExiting:te,onExited:te},ne.UNMOUNTED=X,ne.EXITED=Y,ne.ENTERING=J,ne.ENTERED=Z,ne.EXITING=ee;const re=ne;function ae(n){if(!n||"function"===typeof n)return null;const{major:t}=function(){const n=e.version.split(".");return{major:+n[0],minor:+n[1],patch:+n[2]}}();return t>=19?n.props.ref:n.ref}function se(e){return e&&e.ownerDocument||document}function le(e,n){return function(e){var n=se(e);return n&&n.defaultView||window}(e).getComputedStyle(e,n)}var ie=/([A-Z])/g;var oe=/^ms-/;function ce(e){return function(e){return e.replace(ie,"-$1").toLowerCase()}(e).replace(oe,"-ms-")}var ue=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;const de=function(e,n){var t="",r="";if("string"===typeof n)return e.style.getPropertyValue(ce(n))||le(e).getPropertyValue(ce(n));Object.keys(n).forEach(function(a){var s=n[a];s||0===s?!function(e){return!(!e||!ue.test(e))}(a)?t+=ce(a)+": "+s+";":r+=a+"("+s+") ":e.style.removeProperty(ce(a))}),r&&(t+="transform: "+r+";"),e.style.cssText+=";"+t},fe=!("undefined"===typeof window||!window.document||!window.document.createElement);var he=!1,me=!1;try{var pe={get passive(){return he=!0},get once(){return me=he=!0}};fe&&(window.addEventListener("test",pe,pe),window.removeEventListener("test",pe,!0))}catch($a){}const ge=function(e,n,t,r){if(r&&"boolean"!==typeof r&&!me){var a=r.once,s=r.capture,l=t;!me&&a&&(l=t.__once||function e(r){this.removeEventListener(n,e,s),t.call(this,r)},t.__once=l),e.addEventListener(n,l,he?r:s)}e.addEventListener(n,t,r)};const ve=function(e,n,t,r){var a=r&&"boolean"!==typeof r?r.capture:r;e.removeEventListener(n,t,a),t.__once&&e.removeEventListener(n,t.__once,a)};const be=function(e,n,t,r){return ge(e,n,t,r),function(){ve(e,n,t,r)}};function ye(e,n,t){void 0===t&&(t=5);var r=!1,a=setTimeout(function(){r||function(e,n,t,r){if(void 0===t&&(t=!1),void 0===r&&(r=!0),e){var a=document.createEvent("HTMLEvents");a.initEvent(n,t,r),e.dispatchEvent(a)}}(e,"transitionend",!0)},n+t),s=be(e,"transitionend",function(){r=!0},{once:!0});return function(){clearTimeout(a),s()}}function xe(e,n,t,r){null==t&&(t=function(e){var n=de(e,"transitionDuration")||"",t=-1===n.indexOf("ms")?1e3:1;return parseFloat(n)*t}(e)||0);var a=ye(e,t,r),s=be(e,"transitionend",n);return function(){a(),s()}}function je(e,n){const t=de(e,n)||"",r=-1===t.indexOf("ms")?1e3:1;return parseFloat(t)*r}function we(e,n){const t=je(e,"transitionDuration"),r=je(e,"transitionDelay"),a=xe(e,t=>{t.target===e&&(a(),n(t))},t+r)}const Se=e=>e&&"function"!==typeof e?n=>{e.current=n}:e;const ke=function(n,t){return(0,e.useMemo)(()=>function(e,n){const t=Se(e),r=Se(n);return e=>{t&&t(e),r&&r(e)}}(n,t),[n,t])};const Ee=e.forwardRef((n,t)=>{let{onEnter:a,onEntering:s,onEntered:l,onExit:i,onExiting:o,onExited:c,addEndListener:u,children:d,childRef:f,...h}=n;const m=(0,e.useRef)(null),p=ke(m,f),g=e=>{var n;p((n=e)&&"setState"in n?K.findDOMNode(n):null!=n?n:null)},v=e=>n=>{e&&m.current&&e(m.current,n)},b=(0,e.useCallback)(v(a),[a]),y=(0,e.useCallback)(v(s),[s]),x=(0,e.useCallback)(v(l),[l]),j=(0,e.useCallback)(v(i),[i]),w=(0,e.useCallback)(v(o),[o]),S=(0,e.useCallback)(v(c),[c]),k=(0,e.useCallback)(v(u),[u]);return(0,r.jsx)(re,{ref:t,...h,onEnter:b,onEntered:x,onEntering:y,onExit:j,onExited:S,onExiting:w,addEndListener:k,nodeRef:m,children:"function"===typeof d?(e,n)=>d(e,{...n,ref:g}):e.cloneElement(d,{ref:g})})});Ee.displayName="TransitionWrapper";const Ne=Ee,Ce={[J]:"show",[Z]:"show"},Te=e.forwardRef((n,t)=>{let{className:a,children:s,transitionClasses:i={},onEnter:o,...c}=n;const u={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...c},d=(0,e.useCallback)((e,n)=>{!function(e){e.offsetHeight}(e),null==o||o(e,n)},[o]);return(0,r.jsx)(Ne,{ref:t,addEndListener:we,...u,onEnter:d,childRef:ae(s),children:(n,t)=>e.cloneElement(s,{...t,className:l()("fade",a,s.props.className,Ce[n],i[n])})})});Te.displayName="Fade";const Pe=Te;var _e=t(942),Le=t.n(_e);const Re={"aria-label":Le().string,onClick:Le().func,variant:Le().oneOf(["white"])},Oe=e.forwardRef((e,n)=>{let{className:t,variant:a,"aria-label":s="Close",...i}=e;return(0,r.jsx)("button",{ref:n,type:"button",className:l()("btn-close",a&&`btn-close-${a}`,t),"aria-label":s,...i})});Oe.displayName="CloseButton",Oe.propTypes=Re;const ze=Oe,Ae=e.forwardRef((e,n)=>{const{bsPrefix:t,show:a=!0,closeLabel:s="Close alert",closeVariant:i,className:o,children:c,variant:u="primary",onClose:d,dismissible:h,transition:m=Pe,...p}=C(e,{show:"onClose"}),g=f(t,"alert"),v=R(e=>{d&&d(!1,e)}),b=!0===m?Pe:m,y=(0,r.jsxs)("div",{role:"alert",...b?void 0:p,ref:n,className:l()(o,g,u&&`${g}-${u}`,h&&`${g}-dismissible`),children:[h&&(0,r.jsx)(ze,{onClick:v,"aria-label":s,variant:i}),c]});return b?(0,r.jsx)(b,{unmountOnExit:!0,...p,ref:void 0,in:a,children:y}):a?y:null});Ae.displayName="Alert";const De=Object.assign(Ae,{Link:W,Heading:D}),Ie=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,as:s="div",...i}=e;const o=f(t,"row"),c=h(),u=m(),d=`${o}-cols`,p=[];return c.forEach(e=>{const n=i[e];let t;delete i[e],null!=n&&"object"===typeof n?({cols:t}=n):t=n;const r=e!==u?`-${e}`:"";null!=t&&p.push(`${d}${r}-${t}`)}),(0,r.jsx)(s,{ref:n,...i,className:l()(a,o,...p)})});Ie.displayName="Row";const Me=Ie;const $e=e.forwardRef((e,n)=>{const[{className:t,...a},{as:s="div",bsPrefix:i,spans:o}]=function(e){let{as:n,bsPrefix:t,className:r,...a}=e;t=f(t,"col");const s=h(),i=m(),o=[],c=[];return s.forEach(e=>{const n=a[e];let r,s,l;delete a[e],"object"===typeof n&&null!=n?({span:r,offset:s,order:l}=n):r=n;const u=e!==i?`-${e}`:"";r&&o.push(!0===r?`${t}${u}`:`${t}${u}-${r}`),null!=l&&c.push(`order${u}-${l}`),null!=s&&c.push(`offset${u}-${s}`)}),[{...a,className:l()(r,...o,...c)},{as:n,bsPrefix:t,spans:o}]}(e);return(0,r.jsx)(s,{...a,ref:n,className:l()(t,!o.length&&i)})});$e.displayName="Col";const Fe=$e,Ue=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"card-body"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});Ue.displayName="CardBody";const Be=Ue,He=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"card-footer"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});He.displayName="CardFooter";const Ve=He,We=e.createContext(null);We.displayName="CardHeaderContext";const qe=We,Ke=e.forwardRef((n,t)=>{let{bsPrefix:a,className:s,as:i="div",...o}=n;const c=f(a,"card-header"),u=(0,e.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,r.jsx)(qe.Provider,{value:u,children:(0,r.jsx)(i,{ref:t,...o,className:l()(s,c)})})});Ke.displayName="CardHeader";const Qe=Ke,Ge=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,variant:s,as:i="img",...o}=e;const c=f(t,"card-img");return(0,r.jsx)(i,{ref:n,className:l()(s?`${c}-${s}`:c,a),...o})});Ge.displayName="CardImg";const Xe=Ge,Ye=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"card-img-overlay"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});Ye.displayName="CardImgOverlay";const Je=Ye,Ze=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="a",...i}=e;return a=f(a,"card-link"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});Ze.displayName="CardLink";const en=Ze,nn=O("h6"),tn=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s=nn,...i}=e;return a=f(a,"card-subtitle"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});tn.displayName="CardSubtitle";const rn=tn,an=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="p",...i}=e;return a=f(a,"card-text"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});an.displayName="CardText";const sn=an,ln=O("h5"),on=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s=ln,...i}=e;return a=f(a,"card-title"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});on.displayName="CardTitle";const cn=on,un=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,bg:s,text:i,border:o,body:c=!1,children:u,as:d="div",...h}=e;const m=f(t,"card");return(0,r.jsx)(d,{ref:n,...h,className:l()(a,m,s&&`bg-${s}`,i&&`text-${i}`,o&&`border-${o}`),children:c?(0,r.jsx)(Be,{children:u}):u})});un.displayName="Card";const dn=Object.assign(un,{Img:Xe,Title:cn,Subtitle:rn,Body:Be,Link:en,Text:sn,Header:Qe,Footer:Ve,ImgOverlay:Je}),fn=e.forwardRef((e,n)=>{let{bsPrefix:t,bg:a="primary",pill:s=!1,text:i,className:o,as:c="span",...u}=e;const d=f(t,"badge");return(0,r.jsx)(c,{ref:n,...u,className:l()(o,d,s&&"rounded-pill",i&&`text-${i}`,a&&`bg-${a}`)})});fn.displayName="Badge";const hn=fn,mn=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,striped:s,bordered:i,borderless:o,hover:c,size:u,variant:d,responsive:h,...m}=e;const p=f(t,"table"),g=l()(a,p,d&&`${p}-${d}`,u&&`${p}-${u}`,s&&`${p}-${"string"===typeof s?`striped-${s}`:"striped"}`,i&&`${p}-bordered`,o&&`${p}-borderless`,c&&`${p}-hover`),v=(0,r.jsx)("table",{...m,className:g,ref:n});if(h){let e=`${p}-responsive`;return"string"===typeof h&&(e=`${e}-${h}`),(0,r.jsx)("div",{className:e,children:v})}return v});mn.displayName="Table";const pn=mn,gn=()=>(console.log("\ud83d\udd27 Usando API URL desde variable de entorno:","http://localhost:5001/api"),"http://localhost:5001/api"),vn=()=>({environment:"production",apiBaseUrl:gn(),currentLocation:{protocol:window.location.protocol,hostname:window.location.hostname,port:window.location.port,pathname:window.location.pathname},environmentVariables:{REACT_APP_API_URL:"http://localhost:5001/api",NODE_ENV:"production"}});console.log("\ud83d\udd27 Configuraci\xf3n de API cargada:",vn());const bn=async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const t=`${gn()}${e}`,r={...{headers:{"Content-Type":"application/json",...n.headers},timeout:3e4},...n};try{window.debugLog?window.debugLog("info",`\ud83c\udf10 API Request: ${r.method||"GET"} ${t}`):console.log(`\ud83c\udf10 API Request: ${r.method||"GET"} ${t}`);const n=await fetch(t,r);let s;try{s=await n.json()}catch(a){throw new Error(`Respuesta no v\xe1lida del servidor. Verifique que el backend est\xe9 ejecut\xe1ndose en ${gn()}`)}if(!n.ok)throw new Error(s.message||`HTTP ${n.status}: ${n.statusText}`);return window.debugLog?window.debugLog("success",`\u2705 API Response: ${e}`):console.log(`\u2705 API Response: ${e}`),s}catch(s){let n=s.message;"TypeError"===s.name&&s.message.includes("fetch")?n=`No se puede conectar al servidor backend. Aseg\xfarese de que el servidor est\xe9 ejecut\xe1ndose en ${gn()}`:s.message.includes("Failed to fetch")&&(n=`Error de conexi\xf3n con el servidor backend en ${gn()}. Verifique que el servidor est\xe9 activo.`),window.debugLog?window.debugLog("error",`\u274c API Error: ${e} - ${n}`):console.error(`\u274c API Error: ${e} - ${n}`);const r=new Error(n);throw r.originalError=s,r.endpoint=e,r.url=t,r}},yn={importSeries:async e=>bn("/import/series",{method:"POST",body:JSON.stringify(e)}),previewSeries:async e=>bn("/import/preview/series",{method:"POST",body:JSON.stringify(e)}),getImportSummary:async(e,n)=>{const t=new URLSearchParams;return e&&t.append("date_from",e),n&&t.append("date_to",n),bn(`/import/summary?${t.toString()}`)},reprocessSeries:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return bn(`/import/reprocess/${e}`,{method:"POST",body:JSON.stringify(n)})}},xn={testConnection:async e=>bn("/database/test",{method:"POST",body:JSON.stringify(e)}),testCurrentConnection:async()=>bn("/database/test/current"),getStats:async()=>bn("/database/stats"),verifyTables:async()=>bn("/database/verify/tables"),getTablesInfo:async()=>bn("/database/info/tables"),repairIntegrity:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return bn("/database/repair/integrity",{method:"POST",body:JSON.stringify({execute_repair:e})})},cleanupTestData:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"TEST_%";return bn("/database/cleanup/test-data",{method:"DELETE",body:JSON.stringify({confirm_cleanup:!0,test_pattern:e})})},getServers:async()=>bn("/database/servers"),getCategories:async()=>bn("/database/categories"),getServerStats:async()=>bn("/database/server-stats"),getDashboardData:async()=>bn("/database/dashboard-data"),getStreamTypes:async()=>bn("/database/stream-types"),getStreamsSample:async function(){return bn(`/database/streams-sample?limit=${arguments.length>0&&void 0!==arguments[0]?arguments[0]:100}`)},getSeriesStructureAnalysis:async()=>bn("/database/series-structure-analysis"),getStreams:async function(){return bn(`/database/streams?page=${arguments.length>0&&void 0!==arguments[0]?arguments[0]:1}&limit=${arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3}`)},getContentStats:async()=>bn("/database/content-stats"),optimizeIndexes:async()=>bn("/database/optimize-indexes",{method:"POST"}),getQueryPerformance:async()=>bn("/database/query-performance")},jn={importAPI:yn,seriesAPI:{getSeries:async function(){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const t=new URLSearchParams({page:(arguments.length>0&&void 0!==arguments[0]?arguments[0]:1).toString(),limit:e.toString(),...n});return bn(`/series?${t.toString()}`)},getSeriesById:async e=>bn(`/series/${e}`),createSeries:async(e,n)=>bn("/series",{method:"POST",body:JSON.stringify({seriesData:e,episodesData:n})}),updateSeries:async function(e,n,t){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return bn(`/series/${e}`,{method:"PUT",body:JSON.stringify({seriesData:n,episodesData:t,updateEpisodes:r})})},deleteSeries:async e=>bn(`/series/${e}`,{method:"DELETE"}),searchSeries:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const t=new URLSearchParams({title:e});return n&&t.append("tmdb_id",n),bn(`/series/search/${encodeURIComponent(e)}?${t.toString()}`)},enrichSeries:async function(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return bn(`/series/${e}/enrich`,{method:"POST",body:JSON.stringify({force_update:n})})},getSeriesStats:async()=>bn("/series/stats/overview")},databaseAPI:xn,tmdbAPI:{searchSeries:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const t=new URLSearchParams({title:e,page:(arguments.length>2&&void 0!==arguments[2]?arguments[2]:1).toString()});return n&&t.append("year",n),bn(`/tmdb/search/series?${t.toString()}`)},searchMovie:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const t=new URLSearchParams({title:e,page:(arguments.length>2&&void 0!==arguments[2]?arguments[2]:1).toString()});return n&&t.append("year",n),bn(`/tmdb/search/movie?${t.toString()}`)},searchMulti:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const t=new URLSearchParams({title:e});return n&&t.append("year",n),bn(`/tmdb/search/multi?${t.toString()}`)},getSeriesDetails:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const t=new URLSearchParams;return n&&t.append("append_to_response",n),bn(`/tmdb/series/${e}?${t.toString()}`)},getMovieDetails:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const t=new URLSearchParams;return n&&t.append("append_to_response",n),bn(`/tmdb/movie/${e}?${t.toString()}`)},getSeriesSeasons:async e=>bn(`/tmdb/series/${e}/seasons`),getSeasonEpisodes:async(e,n)=>bn(`/tmdb/series/${e}/season/${n}`),getSeriesImages:async e=>bn(`/tmdb/series/${e}/images`),getMovieImages:async e=>bn(`/tmdb/movie/${e}/images`),getImageUrl:async function(e){const n=new URLSearchParams({path:e,size:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"w500"});return bn(`/tmdb/image/url?${n.toString()}`)},formatForXUI:async function(e){return bn(`/tmdb/format/${arguments.length>1&&void 0!==arguments[1]?arguments[1]:"series"}`,{method:"POST",body:JSON.stringify({tmdb_data:e})})},searchAdvanced:async e=>bn("/tmdb/search/advanced",{method:"POST",body:JSON.stringify(e)}),testConnection:async()=>bn("/tmdb/test")},m3uAPI:{uploadFile:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto",t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=new FormData;return r.append("m3uFile",e),r.append("parse_type",n),r.append("preview_only",t.toString()),bn("/m3u/upload",{method:"POST",body:r,headers:{}})},parseContent:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto",t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return bn("/m3u/parse",{method:"POST",body:JSON.stringify({content:e,parse_type:n,preview_only:t})})},validateContent:async e=>bn("/m3u/validate",{method:"POST",body:JSON.stringify({content:e})}),analyzeContent:async e=>bn("/m3u/analyze",{method:"POST",body:JSON.stringify({content:e})}),analyzeFile:async e=>{try{const n=await new Promise((n,t)=>{const r=new FileReader;r.onload=e=>n(e.target.result),r.onerror=()=>t(new Error("Error leyendo archivo")),r.readAsText(e)});return bn("/m3u/analyze",{method:"POST",body:JSON.stringify({content:n})})}catch(n){throw new Error(`Error procesando archivo: ${n.message}`)}},convertContent:async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"xui",t=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return bn("/m3u/convert",{method:"POST",body:JSON.stringify({content:e,target_format:n,include_metadata:t})})}},systemAPI:{getHealth:async()=>bn("/health"),getStats:async()=>bn("/stats")},utilsAPI:{testConnectivity:async()=>(async()=>{const e=[gn(),"http://localhost:5001/api","http://localhost:5000/api","http://localhost:3000/api","http://127.0.0.1:5001/api","http://127.0.0.1:5000/api","http://127.0.0.1:3000/api"];console.log("\ud83e\uddea Probando conectividad con URLs:",e);for(const t of e)try{if((await fetch(`${t}/health`,{method:"GET",timeout:5e3})).ok)return console.log("\u2705 Conectividad exitosa con:",t),t}catch(n){console.log("\u274c Error conectando a:",t,n.message)}return console.error("\u274c No se pudo conectar a ninguna URL del API"),null})(),getConfigInfo:async()=>bn("/system/config"),healthCheck:async()=>bn("/health")}},wn=(0,e.createContext)(),Sn={databaseConnection:{isConnected:!1,host:"",database:"",username:"",connectionId:null,lastConnected:null,serverInfo:null},backendStatus:"checking",backendInfo:null,availableServers:[],selectedServer:null,categories:[],isLoading:!1,error:null,dataReading:{isReading:!1,progress:0,currentStep:"",currentMessage:"",error:null,result:null},userPreferences:{autoConnect:!0,rememberConnection:!0,defaultContentType:"",defaultServer:null}},kn="SET_DATABASE_CONNECTION",En="CLEAR_DATABASE_CONNECTION",Nn="SET_BACKEND_STATUS",Cn="SET_AVAILABLE_SERVERS",Tn="SET_SELECTED_SERVER",Pn="SET_CATEGORIES",_n="SET_LOADING",Ln="SET_ERROR",Rn="SET_USER_PREFERENCES",On="RESTORE_STATE",zn="SET_DATA_READING",An=(e,n)=>{switch(n.type){case kn:return{...e,databaseConnection:{...e.databaseConnection,...n.payload,lastConnected:(new Date).toISOString()}};case En:return{...e,databaseConnection:{...Sn.databaseConnection}};case Nn:return{...e,backendStatus:n.payload.status,backendInfo:n.payload.info||e.backendInfo};case Cn:return{...e,availableServers:n.payload};case Tn:return{...e,selectedServer:n.payload};case Pn:return{...e,categories:n.payload};case _n:return{...e,isLoading:n.payload};case Ln:return{...e,error:n.payload};case Rn:return{...e,userPreferences:{...e.userPreferences,...n.payload}};case On:return{...e,...n.payload};case zn:return{...e,dataReading:{...e.dataReading,...n.payload}};default:return e}},Dn=n=>{let{children:a}=n;const[s,l]=(0,e.useReducer)(An,Sn),i=(()=>{const[n,r]=(0,e.useState)(!1),[a,s]=(0,e.useState)(0),[l,i]=(0,e.useState)(""),[o,c]=(0,e.useState)(""),[u,d]=(0,e.useState)(null),[f,h]=(0,e.useState)(null),m=(0,e.useRef)(null),p=(0,e.useRef)(!1),g=(0,e.useCallback)(()=>{m.current&&m.current.terminate();try{return m.current=new Worker(new URL(t.p+t.u(954),t.b)),m.current.onmessage=e=>{const{type:n,data:t}=e.data;switch(n){case"progress":s(t.progress||0),i(t.step||""),c(t.message||""),t.result&&h(e=>({...e,...t.result}));break;case"complete":s(100),i("complete"),c(t.message||"\u2705 Operaci\xf3n completada"),h(t.result),r(!1),p.current=!1;break;case"error":d(t.error||"Error desconocido"),r(!1),p.current=!1;break;case"clear_data":h({streamTypes:[],streams:[],series:[],episodes:[],categories:[],stats:{totalStreams:0,totalSeries:0,totalEpisodes:0,totalCategories:0}}),d(null),console.log("\ud83e\uddf9 Datos limpiados antes de nueva lectura");break;default:console.warn("Tipo de mensaje desconocido del worker:",n)}},m.current.onerror=e=>{console.error("Error en Web Worker:",e),d(`Error en Web Worker: ${e.message}`),r(!1),p.current=!1},!0}catch(u){return console.error("Error inicializando Web Worker:",u),d(`Error inicializando Web Worker: ${u.message}`),!1}},[]),v=(0,e.useCallback)(()=>{if(p.current)return console.warn("Ya hay una operaci\xf3n en progreso"),!1;if(r(!0),s(0),i("initializing"),c("Inicializando..."),d(null),h(null),!m.current&&!g())return r(!1),!1;try{return p.current=!0,m.current.postMessage({type:"READ_ALL_DATA",data:{}}),!0}catch(u){return console.error("Error enviando mensaje al worker:",u),d(`Error comunic\xe1ndose con el worker: ${u.message}`),r(!1),p.current=!1,!1}},[g]),b=(0,e.useCallback)(()=>{m.current&&p.current&&(m.current.terminate(),m.current=null,p.current=!1,r(!1),c("Operaci\xf3n cancelada"),d("Operaci\xf3n cancelada por el usuario"))},[]),y=(0,e.useCallback)(()=>{r(!1),s(0),i(""),c(""),d(null),h(null)},[]);(0,e.useEffect)(()=>()=>{m.current&&(m.current.terminate(),m.current=null),p.current=!1},[]);const x=(0,e.useCallback)(()=>({percentage:Math.round(a),step:l,message:o,isComplete:a>=100&&!n,hasError:!!u}),[a,l,o,n,u]),j=(0,e.useCallback)(()=>"undefined"!==typeof Worker,[]);return{isLoading:n,progress:a,currentStep:l,currentMessage:o,error:u,result:f,readAllDatabaseContent:v,cancelOperation:b,resetState:y,initializeWorker:g,getProgressStats:x,isWorkerSupported:j,isWorkerActive:p.current}})(),o=e=>{const n={databaseConnection:e.databaseConnection,selectedServer:e.selectedServer,userPreferences:e.userPreferences};localStorage.setItem("xuiImporterState",JSON.stringify(n))},c=()=>{try{const e=localStorage.getItem("xuiImporterState");if(e){const n=JSON.parse(e);return l({type:On,payload:n}),n}}catch(e){console.error("Error restaurando estado:",e)}return null};(0,e.useEffect)(()=>{o(s)},[s.databaseConnection,s.selectedServer,s.userPreferences,s]),(0,e.useEffect)(()=>{l({type:zn,payload:{isReading:i.isLoading,progress:i.progress,currentStep:i.currentStep,currentMessage:i.currentMessage,error:i.error,result:i.result}})},[i.isLoading,i.progress,i.currentStep,i.currentMessage,i.error,i.result]),(0,e.useEffect)(()=>{const e=c();console.log("\ud83d\udd04 Estado restaurado:",e?"Con datos guardados":"Estado limpio")},[]);const u=e=>{l({type:kn,payload:e})},d=()=>{l({type:En})},f=e=>{l({type:Cn,payload:e})},h=e=>{l({type:Tn,payload:e})},m=e=>{l({type:Pn,payload:e})},p=e=>{l({type:_n,payload:e})},g=e=>{l({type:Ln,payload:e})},v={state:s,connectToDatabase:async function(e){try{p(!0),g(null);const n={host:e.host,port:e.port||3306,user:e.username||e.user,password:e.password,database:e.database},t=await jn.databaseAPI.testConnection(n);if(t.success){u({isConnected:!0,host:e.host,database:e.database,username:e.username,connectionId:t.data.connectionId,serverInfo:t.data.serverInfo}),console.log("\ud83d\ude80 Iniciando lectura autom\xe1tica de datos...");return i.readAllDatabaseContent()||console.warn("\u26a0\ufe0f No se pudo iniciar la lectura de datos autom\xe1ticamente"),{success:!0,message:"Conexi\xf3n exitosa - Leyendo datos..."}}return g(t.error||"Error de conexi\xf3n"),{success:!1,error:t.error}}catch(s){var n,t,r,a;console.error("Error conectando a BD:",s);let e=s.message;return null!==(n=s.message)&&void 0!==n&&n.includes("Failed to fetch")||null!==(t=s.message)&&void 0!==t&&t.includes("fetch")?e="No se puede conectar al servidor backend. Aseg\xfarese de que el servidor est\xe9 ejecut\xe1ndose en el puerto 5000.":null!==(r=s.message)&&void 0!==r&&r.includes("Network Error")?e="Error de red. Verifique su conexi\xf3n a internet y que el servidor backend est\xe9 activo.":null!==(a=s.message)&&void 0!==a&&a.includes("Se requieren")&&(e="Faltan par\xe1metros de conexi\xf3n. Verifique que todos los campos est\xe9n completos."),g(`Error de conexi\xf3n: ${e}`),{success:!1,error:e}}finally{p(!1)}},disconnectFromDatabase:async()=>{try{s.databaseConnection.connectionId&&await jn.databaseAPI.closeConnection(s.databaseConnection.connectionId)}catch(e){console.error("Error cerrando conexi\xf3n:",e)}finally{d(),f([]),m([]),h(null)}},reconnectDatabase:async()=>{if(!s.databaseConnection.isConnected)return!1;try{return p(!0),s.databaseConnection.host&&s.databaseConnection.database&&s.databaseConnection.username?(d(),g("Sesi\xf3n expirada. Por favor, con\xe9ctese nuevamente."),!1):(console.error("Datos de conexi\xf3n incompletos para reconexi\xf3n"),d(),g("Datos de conexi\xf3n incompletos. Inicie sesi\xf3n nuevamente."),!1)}catch(e){return console.error("Error reconectando:",e),d(),g(`Error de reconexi\xf3n: ${e.message}`),!1}finally{p(!1)}},loadInitialData:async()=>{try{const e=await jn.databaseAPI.getServers();e.success&&f(e.data.servers);const n=await jn.databaseAPI.getCategories();n.success&&m(n.data.categories);const t=await jn.databaseAPI.getServerStats();t.success&&u({...s.databaseConnection,serverInfo:t.data})}catch(e){console.error("Error cargando datos iniciales:",e),g(`Error cargando datos: ${e.message}`)}},analyzeXUIStructure:async()=>{try{console.log("\ud83c\udfef Iniciando an\xe1lisis completo de estructura XUI..."),console.log("\ud83c\udf8c Paso 1: Leyendo tabla streams_types...");const e=await jn.databaseAPI.getStreamTypes();if(!e.success)throw new Error("No se pudieron obtener los tipos de stream");const n=e.data.types||[];console.log(`\u2705 Encontrados ${n.length} tipos de stream:`,n),console.log("\ud83d\udfe2 Paso 2: Leyendo muestra de tabla streams...");const t=await jn.databaseAPI.getStreamsSample();if(!t.success)throw new Error("No se pudieron obtener los streams");const r=t.data.streams||[];console.log(`\u2705 Muestra de ${r.length} streams obtenida`);const a={streamTypes:{},contentAnalysis:{live:{count:0,samples:[]},movies:{count:0,samples:[]},series:{count:0,samples:[]},other:{count:0,samples:[]}},seriesStructure:{totalSeries:0,totalEpisodes:0,sampleSeries:[]}};n.forEach(e=>{a.streamTypes[e.type_id]={name:e.type_name,count:0}});for(const s of r){const e=s.type;a.streamTypes[e]&&a.streamTypes[e].count++,2===e?(a.contentAnalysis.movies.count++,a.contentAnalysis.movies.samples.length<5&&a.contentAnalysis.movies.samples.push({id:s.id,name:s.stream_display_name,tmdb_id:s.tmdb_id,has_source:!!s.stream_source})):5===e?(a.contentAnalysis.series.count++,a.contentAnalysis.series.samples.length<5&&a.contentAnalysis.series.samples.push({id:s.id,name:s.stream_display_name,series_id:s.series_id})):1===e?(a.contentAnalysis.live.count++,a.contentAnalysis.live.samples.length<5&&a.contentAnalysis.live.samples.push({id:s.id,name:s.stream_display_name,has_epg:!!s.epg_id})):a.contentAnalysis.other.count++}if(a.contentAnalysis.series.count>0){console.log("\ud83d\udd34 Analizando estructura de series...");const e=await jn.databaseAPI.getSeriesStructureAnalysis();e.success&&(a.seriesStructure={...a.seriesStructure,...e.data})}return console.log("\ud83c\udf89 An\xe1lisis completo de estructura XUI:",a),u({...s.databaseConnection,xuiAnalysis:a}),a}catch(e){return console.error("\u274c Error analizando estructura XUI:",e),g(`Error analizando estructura: ${e.message}`),null}},startDataReading:()=>i.readAllDatabaseContent(),cancelDataReading:()=>{i.cancelOperation()},resetDataReading:()=>{i.resetState()},setDatabaseConnection:u,clearDatabaseConnection:d,setBackendStatus:function(e){l({type:Nn,payload:{status:e,info:arguments.length>1&&void 0!==arguments[1]?arguments[1]:null}})},setAvailableServers:f,setSelectedServer:h,setCategories:m,setLoading:p,setError:g,setUserPreferences:e=>{l({type:Rn,payload:e})},persistState:o,restoreState:c,clearAllState:()=>{localStorage.removeItem("xuiImporterState"),localStorage.removeItem("xuiConnections"),l({type:On,payload:Sn}),console.log("\ud83e\uddf9 Estado limpiado completamente")},getDebugInfo:()=>({currentState:s,localStorage:{xuiImporterState:localStorage.getItem("xuiImporterState"),xuiConnections:localStorage.getItem("xuiConnections")},timestamp:(new Date).toISOString()}),isWorkerSupported:i.isWorkerSupported};return(0,r.jsx)(wn.Provider,{value:v,children:a})},In=()=>{const n=(0,e.useContext)(wn);if(!n)throw new Error("useApp debe ser usado dentro de AppProvider");return n},Mn=()=>{var n,t,a,s,l,i,o,c,u,d,f,h,m,p;const{state:v}=In(),[b,y]=(0,e.useState)({recentSeries:[],servers:[],categories:[],contentByType:[]}),[x,j]=(0,e.useState)(!1);(0,e.useEffect)(()=>{window.debugLog&&window.debugLog("\ud83d\udcca Dashboard component loaded","success"),v.databaseConnection.isConnected&&S()},[v.databaseConnection.isConnected]);const S=async()=>{j(!0);try{window.debugLog&&window.debugLog("\ud83d\udcc8 Loading dashboard data from database...","info");const e=await xn.getDashboardData();e.success?(y(e.data),window.debugLog&&window.debugLog("\u2705 Dashboard data loaded successfully","success")):(console.error("Error loading dashboard data:",e.message),window.debugLog&&window.debugLog(`\u274c Error loading dashboard: ${e.message}`,"error"))}catch(e){console.error("Error loading dashboard data:",e),window.debugLog&&window.debugLog(`\u274c Dashboard error: ${e.message}`,"error")}finally{j(!1)}},k=b||{totalSeries:0,totalEpisodes:0,totalStreams:0,categories:[],servers:[],recentSeries:[]};return(0,r.jsxs)("div",{style:{width:"100%",maxWidth:"none"},children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-primary",children:"\ud83d\udcca Dashboard - RGS IMPORT TOOL XUI"}),v.databaseConnection.isConnected&&(0,r.jsxs)("small",{className:"text-success",children:["\u2705 Connected to ",v.databaseConnection.host,":",v.databaseConnection.database]})]}),(0,r.jsxs)("div",{children:[x&&(0,r.jsx)(g,{animation:"border",size:"sm",className:"me-2"}),(0,r.jsx)(w,{variant:"success",size:"sm",onClick:()=>{window.debugLog&&window.debugLog("\ud83d\udd04 Refreshing dashboard data...","info"),S()},disabled:x||!v.databaseConnection.isConnected,children:"\ud83d\udd04 Refresh Data"})]})]}),!v.databaseConnection.isConnected&&(0,r.jsxs)(De,{variant:"warning",className:"mb-4",children:[(0,r.jsx)(De.Heading,{children:"\ud83d\udd17 No Database Connection"}),(0,r.jsxs)("p",{children:["Connect to your XUI database in the ",(0,r.jsx)("strong",{children:"Connections"})," tab to view real data."]})]}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"success",text:"white",className:"mb-2 shadow",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\ud83d\udcfa TV Series"}),(0,r.jsx)("h2",{className:"display-4",children:(null===(n=k.stats)||void 0===n||null===(t=n.tvSeries)||void 0===t?void 0:t.toLocaleString())||0}),(0,r.jsx)("small",{children:"Series Available"})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"info",text:"white",className:"mb-2 shadow",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\ud83c\udfac Movies"}),(0,r.jsx)("h2",{className:"display-4",children:(null===(a=k.stats)||void 0===a||null===(s=a.movies)||void 0===s?void 0:s.toLocaleString())||0}),(0,r.jsx)("small",{children:"Movies Available"})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"warning",text:"white",className:"mb-2 shadow",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\ud83c\udf9e\ufe0f Episodes"}),(0,r.jsx)("h2",{className:"display-4",children:(null===(l=k.stats)||void 0===l||null===(i=l.episodes)||void 0===i?void 0:i.toLocaleString())||0}),(0,r.jsx)("small",{children:"Total Episodes"})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"danger",text:"white",className:"mb-2 shadow",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\ud83d\udcfa Live TV"}),(0,r.jsx)("h2",{className:"display-4",children:(null===(o=k.stats)||void 0===o||null===(c=o.liveStreams)||void 0===c?void 0:c.toLocaleString())||0}),(0,r.jsx)("small",{children:"Live Channels"})]})})})]}),(null===(u=k.contentByType)||void 0===u?void 0:u.length)>0&&(0,r.jsx)(Me,{className:"mb-4",children:(0,r.jsx)(Fe,{lg:12,children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-dark text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcca Content Breakdown by Type"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsx)(Me,{children:k.contentByType.map((e,n)=>{var t;return(0,r.jsx)(Fe,{md:4,className:"mb-3",children:(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsx)("h3",{className:"mb-1",children:(null===(t=e.count)||void 0===t?void 0:t.toLocaleString())||0}),(0,r.jsx)(hn,{bg:"primary",className:"mb-2",children:e.type}),(0,r.jsx)("p",{className:"mb-0 text-muted",children:e.type||"Unknown Type"})]})},`content-type-${e.type}-${n}`)})})})]})})}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{lg:8,children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-primary text-white",children:(0,r.jsxs)("h5",{className:"mb-0",children:["\ud83d\udcda Recent Series ",(null===(d=k.recentSeries)||void 0===d?void 0:d.length)>0&&`(${k.recentSeries.length})`]})}),(0,r.jsx)(dn.Body,{children:(null===(f=k.recentSeries)||void 0===f?void 0:f.length)>0?(0,r.jsxs)(pn,{striped:!0,hover:!0,children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"\ud83d\udcfa Series Name"}),(0,r.jsx)("th",{children:"\ud83c\udf9e\ufe0f Episodes"}),(0,r.jsx)("th",{children:"\ud83d\udcc5 Added"}),(0,r.jsx)("th",{children:"\ud83c\udff7\ufe0f Category"})]})}),(0,r.jsx)("tbody",{children:k.recentSeries.map((e,n)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:e.title||"Unknown Series"})}),(0,r.jsx)("td",{children:(0,r.jsxs)(hn,{bg:"info",children:[e.episode_count||0," eps"]})}),(0,r.jsx)("td",{children:e.year||"Invalid Date"}),(0,r.jsx)("td",{children:(0,r.jsx)(hn,{bg:"secondary",children:"Unknown"})})]},`series-${e.title||e.id||n}`))})]}):(0,r.jsx)("p",{className:"text-muted text-center py-3",children:v.databaseConnection.isConnected?"No series found in database":"Connect to database to view series"})})]})}),(0,r.jsx)(Fe,{lg:4,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-success text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\u26a1 Quick Actions"})}),(0,r.jsxs)(dn.Body,{className:"d-flex flex-column",children:[(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)(w,{variant:"primary",className:"w-100 mb-2",size:"lg",children:"\ud83d\udce5 Import M3U File"}),(0,r.jsx)(w,{variant:"info",className:"w-100 mb-2",children:"\ud83d\udd17 Manage Connections"}),(0,r.jsx)(w,{variant:"warning",className:"w-100 mb-2",children:"\ud83d\udcca View Import History"})]}),(0,r.jsxs)("div",{className:"mt-auto",children:[(0,r.jsx)("h6",{children:"\ud83d\udda5\ufe0f Available Servers:"}),(null===(h=k.servers)||void 0===h?void 0:h.length)>0?(0,r.jsxs)("ul",{className:"list-unstyled",children:[(k.servers||[]).slice(0,3).map((e,n)=>(0,r.jsxs)("li",{children:[(0,r.jsx)(hn,{bg:"outline-primary",className:"me-1",children:"\ud83d\udce1"}),e.server_name]},`server-${e.server_name||n}-${n}`)),(null===(m=k.servers)||void 0===m?void 0:m.length)>3&&(0,r.jsx)("li",{children:(0,r.jsxs)("small",{className:"text-muted",children:["+",k.servers.length-3," more..."]})})]}):(0,r.jsx)("p",{className:"text-muted",children:"No servers configured"})]})]})]})})]}),(0,r.jsx)(Me,{children:(0,r.jsx)(Fe,{children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-info text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcca System Status"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(Me,{children:[(0,r.jsxs)(Fe,{md:4,children:[(0,r.jsx)("h6",{children:"\ud83d\uddc4\ufe0f Database Connection:"}),(0,r.jsx)(hn,{bg:v.databaseConnection.isConnected?"success":"danger",children:v.databaseConnection.isConnected?"\u2705 Connected":"\u274c Disconnected"}),v.databaseConnection.isConnected&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("small",{className:"text-muted",children:["Host: ",v.databaseConnection.host,(0,r.jsx)("br",{}),"Database: ",v.databaseConnection.database]})})]}),(0,r.jsxs)(Fe,{md:4,children:[(0,r.jsx)("h6",{children:"\ud83c\udf10 Backend Status:"}),(0,r.jsx)(hn,{bg:"connected"===v.backendStatus?"success":"warning",children:"connected"===v.backendStatus?"\u2705 Online":"\u26a0\ufe0f Checking..."})]}),(0,r.jsxs)(Fe,{md:4,children:[(0,r.jsx)("h6",{children:"\ud83c\udfaf Categories:"}),(0,r.jsxs)(hn,{bg:"primary",children:[(null===(p=k.categories)||void 0===p?void 0:p.length)||0," configured"]})]})]})})]})})})]})},$n={type:Le().string,tooltip:Le().bool,as:Le().elementType},Fn=e.forwardRef((e,n)=>{let{as:t="div",className:a,type:s="valid",tooltip:i=!1,...o}=e;return(0,r.jsx)(t,{...o,ref:n,className:l()(a,`${s}-${i?"tooltip":"feedback"}`)})});Fn.displayName="Feedback",Fn.propTypes=$n;const Un=Fn,Bn=e.createContext({}),Hn=e.forwardRef((n,t)=>{let{id:a,bsPrefix:s,className:i,type:o="checkbox",isValid:c=!1,isInvalid:u=!1,as:d="input",...h}=n;const{controlId:m}=(0,e.useContext)(Bn);return s=f(s,"form-check-input"),(0,r.jsx)(d,{...h,ref:t,type:o,id:a||m,className:l()(i,s,c&&"is-valid",u&&"is-invalid")})});Hn.displayName="FormCheckInput";const Vn=Hn,Wn=e.forwardRef((n,t)=>{let{bsPrefix:a,className:s,htmlFor:i,...o}=n;const{controlId:c}=(0,e.useContext)(Bn);return a=f(a,"form-check-label"),(0,r.jsx)("label",{...o,ref:t,htmlFor:i||c,className:l()(s,a)})});Wn.displayName="FormCheckLabel";const qn=Wn;function Kn(n,t){let r=0;return e.Children.map(n,n=>e.isValidElement(n)?t(n,r++):n)}const Qn=e.forwardRef((n,t)=>{let{id:a,bsPrefix:s,bsSwitchPrefix:i,inline:o=!1,reverse:c=!1,disabled:u=!1,isValid:d=!1,isInvalid:h=!1,feedbackTooltip:m=!1,feedback:p,feedbackType:g,className:v,style:b,title:y="",type:x="checkbox",label:j,children:w,as:S="input",...k}=n;s=f(s,"form-check"),i=f(i,"form-switch");const{controlId:E}=(0,e.useContext)(Bn),N=(0,e.useMemo)(()=>({controlId:a||E}),[E,a]),C=!w&&null!=j&&!1!==j||function(n,t){return e.Children.toArray(n).some(n=>e.isValidElement(n)&&n.type===t)}(w,qn),T=(0,r.jsx)(Vn,{...k,type:"switch"===x?"checkbox":x,ref:t,isValid:d,isInvalid:h,disabled:u,as:S});return(0,r.jsx)(Bn.Provider,{value:N,children:(0,r.jsx)("div",{style:b,className:l()(v,C&&s,o&&`${s}-inline`,c&&`${s}-reverse`,"switch"===x&&i),children:w||(0,r.jsxs)(r.Fragment,{children:[T,C&&(0,r.jsx)(qn,{title:y,children:j}),p&&(0,r.jsx)(Un,{type:g,tooltip:m,children:p})]})})})});Qn.displayName="FormCheck";const Gn=Object.assign(Qn,{Input:Vn,Label:qn});t(241);const Xn=e.forwardRef((n,t)=>{let{bsPrefix:a,type:s,size:i,htmlSize:o,id:c,className:u,isValid:d=!1,isInvalid:h=!1,plaintext:m,readOnly:p,as:g="input",...v}=n;const{controlId:b}=(0,e.useContext)(Bn);return a=f(a,"form-control"),(0,r.jsx)(g,{...v,type:s,size:o,ref:t,readOnly:p,id:c||b,className:l()(u,m?`${a}-plaintext`:a,i&&`${a}-${i}`,"color"===s&&`${a}-color`,d&&"is-valid",h&&"is-invalid")})});Xn.displayName="FormControl";const Yn=Object.assign(Xn,{Feedback:Un}),Jn=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"form-floating"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});Jn.displayName="FormFloating";const Zn=Jn,et=e.forwardRef((n,t)=>{let{controlId:a,as:s="div",...l}=n;const i=(0,e.useMemo)(()=>({controlId:a}),[a]);return(0,r.jsx)(Bn.Provider,{value:i,children:(0,r.jsx)(s,{...l,ref:t})})});et.displayName="FormGroup";const nt=et,tt=e.forwardRef((n,t)=>{let{as:a="label",bsPrefix:s,column:i=!1,visuallyHidden:o=!1,className:c,htmlFor:u,...d}=n;const{controlId:h}=(0,e.useContext)(Bn);s=f(s,"form-label");let m="col-form-label";"string"===typeof i&&(m=`${m} ${m}-${i}`);const p=l()(c,s,o&&"visually-hidden",i&&m);return u=u||h,i?(0,r.jsx)(Fe,{ref:t,as:"label",className:p,htmlFor:u,...d}):(0,r.jsx)(a,{ref:t,className:p,htmlFor:u,...d})});tt.displayName="FormLabel";const rt=tt,at=e.forwardRef((n,t)=>{let{bsPrefix:a,className:s,id:i,...o}=n;const{controlId:c}=(0,e.useContext)(Bn);return a=f(a,"form-range"),(0,r.jsx)("input",{...o,type:"range",ref:t,className:l()(s,a),id:i||c})});at.displayName="FormRange";const st=at,lt=e.forwardRef((n,t)=>{let{bsPrefix:a,size:s,htmlSize:i,className:o,isValid:c=!1,isInvalid:u=!1,id:d,...h}=n;const{controlId:m}=(0,e.useContext)(Bn);return a=f(a,"form-select"),(0,r.jsx)("select",{...h,size:i,ref:t,className:l()(o,a,s&&`${a}-${s}`,c&&"is-valid",u&&"is-invalid"),id:d||m})});lt.displayName="FormSelect";const it=lt,ot=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,as:s="small",muted:i,...o}=e;return t=f(t,"form-text"),(0,r.jsx)(s,{...o,ref:n,className:l()(a,t,i&&"text-muted")})});ot.displayName="FormText";const ct=ot,ut=e.forwardRef((e,n)=>(0,r.jsx)(Gn,{...e,ref:n,type:"switch"}));ut.displayName="Switch";const dt=Object.assign(ut,{Input:Gn.Input,Label:Gn.Label}),ft=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,children:s,controlId:i,label:o,...c}=e;return t=f(t,"form-floating"),(0,r.jsxs)(nt,{ref:n,className:l()(a,t),controlId:i,...c,children:[s,(0,r.jsx)("label",{htmlFor:i,children:o})]})});ft.displayName="FloatingLabel";const ht=ft,mt={_ref:Le().any,validated:Le().bool,as:Le().elementType},pt=e.forwardRef((e,n)=>{let{className:t,validated:a,as:s="form",...i}=e;return(0,r.jsx)(s,{...i,ref:n,className:l()(t,a&&"was-validated")})});pt.displayName="Form",pt.propTypes=mt;const gt=Object.assign(pt,{Group:nt,Control:Yn,Floating:Zn,Check:Gn,Switch:dt,Label:rt,Text:ct,Range:st,Select:it,FloatingLabel:ht});function vt(e,n,t){const r=(e-n)/(t-n)*100;return Math.round(1e3*r)/1e3}function bt(e,n){let{min:t,now:a,max:s,label:i,visuallyHidden:o,striped:c,animated:u,className:d,style:f,variant:h,bsPrefix:m,...p}=e;return(0,r.jsx)("div",{ref:n,...p,role:"progressbar",className:l()(d,`${m}-bar`,{[`bg-${h}`]:h,[`${m}-bar-animated`]:u,[`${m}-bar-striped`]:u||c}),style:{width:`${vt(a,t,s)}%`,...f},"aria-valuenow":a,"aria-valuemin":t,"aria-valuemax":s,children:o?(0,r.jsx)("span",{className:"visually-hidden",children:i}):i})}const yt=e.forwardRef((n,t)=>{let{isChild:a=!1,...s}=n;const i={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...s};if(i.bsPrefix=f(i.bsPrefix,"progress"),a)return bt(i,t);const{min:o,now:c,max:u,label:d,visuallyHidden:h,striped:m,animated:p,bsPrefix:g,variant:v,className:b,children:y,...x}=i;return(0,r.jsx)("div",{ref:t,...x,className:l()(b,g),children:y?Kn(y,n=>(0,e.cloneElement)(n,{isChild:!0})):bt({min:o,now:c,max:u,label:d,visuallyHidden:h,striped:m,animated:p,bsPrefix:g,variant:v},t)})});yt.displayName="ProgressBar";const xt=yt;var jt;function wt(e){if((!jt&&0!==jt||e)&&fe){var n=document.createElement("div");n.style.position="absolute",n.style.top="-9999px",n.style.width="50px",n.style.height="50px",n.style.overflow="scroll",document.body.appendChild(n),jt=n.offsetWidth-n.clientWidth,document.body.removeChild(n)}return jt}function St(n){const t=function(n){const t=(0,e.useRef)(n);return t.current=n,t}(n);(0,e.useEffect)(()=>()=>t.current(),[])}function kt(e){void 0===e&&(e=se());try{var n=e.activeElement;return n&&n.nodeName?n:null}catch($a){return e.body}}function Et(e,n){return e.contains?e.contains(n):e.compareDocumentPosition?e===n||!!(16&e.compareDocumentPosition(n)):void 0}function Nt(n){const t=function(n){const t=(0,e.useRef)(n);return t.current=n,t}(n);(0,e.useEffect)(()=>()=>t.current(),[])}function Ct(e){return`data-rr-ui-${e}`}const Tt=Ct("modal-open");const Pt=class{constructor(){let{ownerDocument:e,handleContainerOverflow:n=!0,isRTL:t=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.handleContainerOverflow=n,this.isRTL=t,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;const n=e.defaultView;return Math.abs(n.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){const n={overflow:"hidden"},t=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[t]:r.style[t]},e.scrollBarWidth&&(n[t]=`${parseInt(de(r,t)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(Tt,""),de(r,n)}reset(){[...this.modals].forEach(e=>this.remove(e))}removeContainerStyle(e){const n=this.getElement();n.removeAttribute(Tt),Object.assign(n.style,e.style)}add(e){let n=this.modals.indexOf(e);return-1!==n?n:(n=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==n||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state)),n)}remove(e){const n=this.modals.indexOf(e);-1!==n&&(this.modals.splice(n,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}},_t=(0,e.createContext)(fe?window:void 0);_t.Provider;function Lt(){return(0,e.useContext)(_t)}const Rt=(e,n)=>fe?null==e?(n||se()).body:("function"===typeof e&&(e=e()),e&&"current"in e&&(e=e.current),e&&("nodeType"in e||e.getBoundingClientRect)?e:null):null;const Ot=e=>e&&"function"!==typeof e?n=>{e.current=n}:e;const zt=function(n,t){return(0,e.useMemo)(()=>function(e,n){const t=Ot(e),r=Ot(n);return e=>{t&&t(e),r&&r(e)}}(n,t),[n,t])};const At=function(n){let{children:t,in:r,onExited:a,mountOnEnter:s,unmountOnExit:l}=n;const i=(0,e.useRef)(null),o=(0,e.useRef)(r),c=M(a);(0,e.useEffect)(()=>{r?o.current=!0:c(i.current)},[r,c]);const u=zt(i,ae(t)),d=(0,e.cloneElement)(t,{ref:u});return r?d:l||!o.current&&s?null:d},Dt=["onEnter","onEntering","onEntered","onExit","onExiting","onExited","addEndListener","children"];const It=["component"];const Mt=e.forwardRef((n,t)=>{let{component:a}=n;const s=function(n){let{onEnter:t,onEntering:r,onEntered:a,onExit:s,onExiting:l,onExited:i,addEndListener:o,children:c}=n,u=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(n,Dt);const d=(0,e.useRef)(null),f=zt(d,ae(c)),h=e=>n=>{e&&d.current&&e(d.current,n)},m=(0,e.useCallback)(h(t),[t]),p=(0,e.useCallback)(h(r),[r]),g=(0,e.useCallback)(h(a),[a]),v=(0,e.useCallback)(h(s),[s]),b=(0,e.useCallback)(h(l),[l]),y=(0,e.useCallback)(h(i),[i]),x=(0,e.useCallback)(h(o),[o]);return Object.assign({},u,{nodeRef:d},t&&{onEnter:m},r&&{onEntering:p},a&&{onEntered:g},s&&{onExit:v},l&&{onExiting:b},i&&{onExited:y},o&&{addEndListener:x},{children:"function"===typeof c?(e,n)=>c(e,Object.assign({},n,{ref:f})):(0,e.cloneElement)(c,{ref:f})})}(function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(n,It));return(0,r.jsx)(a,Object.assign({ref:t},s))});function $t(n){let{children:t,in:r,onExited:a,onEntered:s,transition:l}=n;const[i,o]=(0,e.useState)(!r);r&&i&&o(!1);const c=function(n){let{in:t,onTransition:r}=n;const a=(0,e.useRef)(null),s=(0,e.useRef)(!0),l=M(r);return F(()=>{if(!a.current)return;let e=!1;return l({in:t,element:a.current,initial:s.current,isStale:()=>e}),()=>{e=!0}},[t,l]),F(()=>(s.current=!1,()=>{s.current=!0}),[]),a}({in:!!r,onTransition:e=>{Promise.resolve(l(e)).then(()=>{e.isStale()||(e.in?null==s||s(e.element,e.initial):(o(!0),null==a||a(e.element)))},n=>{throw e.in||o(!0),n})}}),u=zt(c,ae(t));return i&&!r?null:(0,e.cloneElement)(t,{ref:u})}function Ft(e,n,t){return e?(0,r.jsx)(Mt,Object.assign({},t,{component:e})):n?(0,r.jsx)($t,Object.assign({},t,{transition:n})):(0,r.jsx)(At,Object.assign({},t))}const Ut=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let Bt;function Ht(n){const t=Lt(),r=n||function(e){return Bt||(Bt=new Pt({ownerDocument:null==e?void 0:e.document})),Bt}(t),a=(0,e.useRef)({dialog:null,backdrop:null});return Object.assign(a.current,{add:()=>r.add(a.current),remove:()=>r.remove(a.current),isTopModal:()=>r.isTopModal(a.current),setDialogRef:(0,e.useCallback)(e=>{a.current.dialog=e},[]),setBackdropRef:(0,e.useCallback)(e=>{a.current.backdrop=e},[])})}const Vt=(0,e.forwardRef)((n,t)=>{let{show:a=!1,role:s="dialog",className:l,style:i,children:o,backdrop:c=!0,keyboard:u=!0,onBackdropClick:d,onEscapeKeyDown:f,transition:h,runTransition:m,backdropTransition:p,runBackdropTransition:g,autoFocus:v=!0,enforceFocus:b=!0,restoreFocus:y=!0,restoreFocusOptions:x,renderDialog:j,renderBackdrop:w=e=>(0,r.jsx)("div",Object.assign({},e)),manager:S,container:k,onShow:E,onHide:N=()=>{},onExit:C,onExited:T,onExiting:P,onEnter:_,onEntering:L,onEntered:R}=n,O=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(n,Ut);const z=Lt(),A=function(n,t){const r=Lt(),[a,s]=(0,e.useState)(()=>Rt(n,null==r?void 0:r.document));if(!a){const e=Rt(n);e&&s(e)}return(0,e.useEffect)(()=>{t&&a&&t(a)},[t,a]),(0,e.useEffect)(()=>{const e=Rt(n);e!==a&&s(e)},[n,a]),a}(k),D=Ht(S),I=function(){const n=(0,e.useRef)(!0),t=(0,e.useRef)(()=>n.current);return(0,e.useEffect)(()=>(n.current=!0,()=>{n.current=!1}),[]),t.current}(),$=function(n){const t=(0,e.useRef)(null);return(0,e.useEffect)(()=>{t.current=n}),t.current}(a),[F,U]=(0,e.useState)(!a),B=(0,e.useRef)(null);(0,e.useImperativeHandle)(t,()=>D,[D]),fe&&!$&&a&&(B.current=kt(null==z?void 0:z.document)),a&&F&&U(!1);const H=M(()=>{if(D.add(),X.current=be(document,"keydown",Q),G.current=be(document,"focus",()=>setTimeout(W),!0),E&&E(),v){var e,n;const t=kt(null!=(e=null==(n=D.dialog)?void 0:n.ownerDocument)?e:null==z?void 0:z.document);D.dialog&&t&&!Et(D.dialog,t)&&(B.current=t,D.dialog.focus())}}),V=M(()=>{var e;(D.remove(),null==X.current||X.current(),null==G.current||G.current(),y)&&(null==(e=B.current)||null==e.focus||e.focus(x),B.current=null)});(0,e.useEffect)(()=>{a&&A&&H()},[a,A,H]),(0,e.useEffect)(()=>{F&&V()},[F,V]),Nt(()=>{V()});const W=M(()=>{if(!b||!I()||!D.isTopModal())return;const e=kt(null==z?void 0:z.document);D.dialog&&e&&!Et(D.dialog,e)&&D.dialog.focus()}),q=M(e=>{e.target===e.currentTarget&&(null==d||d(e),!0===c&&N())}),Q=M(e=>{u&&function(e){return"Escape"===e.code||27===e.keyCode}(e)&&D.isTopModal()&&(null==f||f(e),e.defaultPrevented||N())}),G=(0,e.useRef)(),X=(0,e.useRef)();if(!A)return null;const Y=Object.assign({role:s,ref:D.setDialogRef,"aria-modal":"dialog"===s||void 0},O,{style:i,className:l,tabIndex:-1});let J=j?j(Y):(0,r.jsx)("div",Object.assign({},Y,{children:e.cloneElement(o,{role:"document"})}));J=Ft(h,m,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!a,onExit:C,onExiting:P,onExited:function(){U(!0),null==T||T(...arguments)},onEnter:_,onEntering:L,onEntered:R,children:J});let Z=null;return c&&(Z=w({ref:D.setBackdropRef,onClick:q}),Z=Ft(p,g,{in:!!a,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:Z})),(0,r.jsx)(r.Fragment,{children:K.createPortal((0,r.jsxs)(r.Fragment,{children:[Z,J]}),A)})});Vt.displayName="Modal";const Wt=Object.assign(Vt,{Manager:Pt});var qt=Function.prototype.bind.call(Function.prototype.call,[].slice);function Kt(e,n){return qt(e.querySelectorAll(n))}function Qt(e,n){return e.replace(new RegExp("(^|\\s)"+n+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const Gt=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Xt=".sticky-top",Yt=".navbar-toggler";class Jt extends Pt{adjustAndStore(e,n,t){const r=n.style[e];n.dataset[e]=r,de(n,{[e]:`${parseFloat(de(n,e))+t}px`})}restore(e,n){const t=n.dataset[e];void 0!==t&&(delete n.dataset[e],de(n,{[e]:t}))}setContainerStyle(e){super.setContainerStyle(e);const n=this.getElement();var t,r;if(r="modal-open",(t=n).classList?t.classList.add(r):function(e,n){return e.classList?!!n&&e.classList.contains(n):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+n+" ")}(t,r)||("string"===typeof t.className?t.className=t.className+" "+r:t.setAttribute("class",(t.className&&t.className.baseVal||"")+" "+r)),!e.scrollBarWidth)return;const a=this.isRTL?"paddingLeft":"paddingRight",s=this.isRTL?"marginLeft":"marginRight";Kt(n,Gt).forEach(n=>this.adjustAndStore(a,n,e.scrollBarWidth)),Kt(n,Xt).forEach(n=>this.adjustAndStore(s,n,-e.scrollBarWidth)),Kt(n,Yt).forEach(n=>this.adjustAndStore(s,n,e.scrollBarWidth))}removeContainerStyle(e){super.removeContainerStyle(e);const n=this.getElement();var t,r;r="modal-open",(t=n).classList?t.classList.remove(r):"string"===typeof t.className?t.className=Qt(t.className,r):t.setAttribute("class",Qt(t.className&&t.className.baseVal||"",r));const a=this.isRTL?"paddingLeft":"paddingRight",s=this.isRTL?"marginLeft":"marginRight";Kt(n,Gt).forEach(e=>this.restore(a,e)),Kt(n,Xt).forEach(e=>this.restore(s,e)),Kt(n,Yt).forEach(e=>this.restore(s,e))}}let Zt;const er=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"modal-body"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});er.displayName="ModalBody";const nr=er,tr=e.createContext({onHide(){}}),rr=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,contentClassName:s,centered:i,size:o,fullscreen:c,children:u,scrollable:d,...h}=e;t=f(t,"modal");const m=`${t}-dialog`,p="string"===typeof c?`${t}-fullscreen-${c}`:`${t}-fullscreen`;return(0,r.jsx)("div",{...h,ref:n,className:l()(m,a,o&&`${t}-${o}`,i&&`${m}-centered`,d&&`${m}-scrollable`,c&&p),children:(0,r.jsx)("div",{className:l()(`${t}-content`,s),children:u})})});rr.displayName="ModalDialog";const ar=rr,sr=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"modal-footer"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});sr.displayName="ModalFooter";const lr=sr,ir=e.forwardRef((n,t)=>{let{closeLabel:a="Close",closeVariant:s,closeButton:l=!1,onHide:i,children:o,...c}=n;const u=(0,e.useContext)(tr),d=R(()=>{null==u||u.onHide(),null==i||i()});return(0,r.jsxs)("div",{ref:t,...c,children:[o,l&&(0,r.jsx)(ze,{"aria-label":a,variant:s,onClick:d})]})});ir.displayName="AbstractModalHeader";const or=ir,cr=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,closeLabel:s="Close",closeButton:i=!1,...o}=e;return t=f(t,"modal-header"),(0,r.jsx)(or,{ref:n,...o,className:l()(a,t),closeLabel:s,closeButton:i})});cr.displayName="ModalHeader";const ur=cr,dr=O("h4"),fr=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s=dr,...i}=e;return a=f(a,"modal-title"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});fr.displayName="ModalTitle";const hr=fr;function mr(e){return(0,r.jsx)(Pe,{...e,timeout:null})}function pr(e){return(0,r.jsx)(Pe,{...e,timeout:null})}const gr=e.forwardRef((n,t)=>{let{bsPrefix:a,className:s,style:i,dialogClassName:o,contentClassName:u,children:d,dialogAs:h=ar,"data-bs-theme":m,"aria-labelledby":p,"aria-describedby":g,"aria-label":v,show:b=!1,animation:y=!0,backdrop:x=!0,keyboard:j=!0,onEscapeKeyDown:w,onShow:S,onHide:k,container:E,autoFocus:N=!0,enforceFocus:C=!0,restoreFocus:T=!0,restoreFocusOptions:P,onEntered:_,onExit:L,onExiting:O,onEnter:z,onEntering:A,onExited:D,backdropClassName:I,manager:M,...$}=n;const[F,U]=(0,e.useState)({}),[B,H]=(0,e.useState)(!1),V=(0,e.useRef)(!1),W=(0,e.useRef)(!1),q=(0,e.useRef)(null),[K,Q]=(0,e.useState)(null),G=ke(t,Q),X=R(k),Y=function(){const{dir:n}=(0,e.useContext)(c);return"rtl"===n}();a=f(a,"modal");const J=(0,e.useMemo)(()=>({onHide:X}),[X]);function Z(){return M||function(e){return Zt||(Zt=new Jt(e)),Zt}({isRTL:Y})}function ee(e){if(!fe)return;const n=Z().getScrollbarWidth()>0,t=e.scrollHeight>se(e).documentElement.clientHeight;U({paddingRight:n&&!t?wt():void 0,paddingLeft:!n&&t?wt():void 0})}const ne=R(()=>{K&&ee(K.dialog)});St(()=>{ve(window,"resize",ne),null==q.current||q.current()});const te=()=>{V.current=!0},re=e=>{V.current&&K&&e.target===K.dialog&&(W.current=!0),V.current=!1},ae=()=>{H(!0),q.current=xe(K.dialog,()=>{H(!1)})},le=e=>{"static"!==x?W.current||e.target!==e.currentTarget?W.current=!1:null==k||k():(e=>{e.target===e.currentTarget&&ae()})(e)},ie=(0,e.useCallback)(e=>(0,r.jsx)("div",{...e,className:l()(`${a}-backdrop`,I,!y&&"show")}),[y,I,a]),oe={...i,...F};oe.display="block";return(0,r.jsx)(tr.Provider,{value:J,children:(0,r.jsx)(Wt,{show:b,ref:G,backdrop:x,container:E,keyboard:!0,autoFocus:N,enforceFocus:C,restoreFocus:T,restoreFocusOptions:P,onEscapeKeyDown:e=>{j?null==w||w(e):(e.preventDefault(),"static"===x&&ae())},onShow:S,onHide:k,onEnter:(e,n)=>{e&&ee(e),null==z||z(e,n)},onEntering:(e,n)=>{null==A||A(e,n),ge(window,"resize",ne)},onEntered:_,onExit:e=>{null==q.current||q.current(),null==L||L(e)},onExiting:O,onExited:e=>{e&&(e.style.display=""),null==D||D(e),ve(window,"resize",ne)},manager:Z(),transition:y?mr:void 0,backdropTransition:y?pr:void 0,renderBackdrop:ie,renderDialog:e=>(0,r.jsx)("div",{role:"dialog",...e,style:oe,className:l()(s,a,B&&`${a}-static`,!y&&"show"),onClick:x?le:void 0,onMouseUp:re,"data-bs-theme":m,"aria-label":v,"aria-labelledby":p,"aria-describedby":g,children:(0,r.jsx)(h,{...$,onMouseDown:te,className:o,contentClassName:u,children:d})})})})});gr.displayName="Modal";const vr=Object.assign(gr,{Body:nr,Header:ur,Title:hr,Footer:lr,Dialog:ar,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150}),br=function(e,n,t){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";let a=`${e} - S${String(n).padStart(2,"0")}E${String(t).padStart(2,"0")}`;return r&&r.trim()&&(a+=` - ${r.trim()}`),a},yr=e=>{const n=e.split("\n"),t=new Map;let r=null;for(const a of n)if(a.startsWith("#EXTINF:")){const e=a.match(/#EXTINF:([^,]*),(.+)/);if(e){r={duration:e[1].trim(),title:e[2].trim()};const n=/(.+?)\s*[-\s]*[Ss](\d+)[Ee](\d+)(?:\s*[-\s]*(.+))?/,a=/(.+?)\s*[-\s]*Season\s*(\d+)\s*Episode\s*(\d+)(?:\s*[-\s]*(.+))?/i;let s=r.title.match(n)||r.title.match(a);if(s){const e=s[1].trim(),n=parseInt(s[2]),a=parseInt(s[3]),l=s[4]?s[4].trim():"";t.has(e)||t.set(e,{title:e,episodes:[]}),r.series={title:e,season_num:n,episode_num:a,episode_title:l}}}}else if(a.trim()&&!a.startsWith("#")&&r){if(r.series){t.get(r.series.title).episodes.push({season_num:r.series.season_num,episode_num:r.series.episode_num,episode_title:r.series.episode_title,display_name:br(r.series.title,r.series.season_num,r.series.episode_num,r.series.episode_title),sources:[a.trim()],duration:r.duration})}r=null}return Array.from(t.values())},xr=async()=>{try{const e=await jn.systemAPI.getHealth();return e.success?window.debugLog("success","\u2705 Sistema operativo"):window.debugLog("error","\u274c Problemas en el sistema"),e}catch(e){return window.debugLog("error",`\u274c Error verificando sistema: ${e.message}`),{success:!1,message:"Backend no disponible",error:e.message}}},jr=n=>{let{selectedFile:t,isImporting:a,onSeriesDetected:s}=n;const[l,i]=(0,e.useState)([]),[o,c]=(0,e.useState)(new Set),[u,d]=(0,e.useState)(!1),[f,h]=(0,e.useState)(null),[m,p]=(0,e.useState)({}),[g,v]=(0,e.useState)({});(0,e.useEffect)(()=>{t&&"application/x-mpegurl"===t.type&&b()},[t]);const b=async()=>{window.debugLog("info","\ud83d\udd0d Analizando archivo M3U para series...");try{const e=(async e=>{window.debugLog("info","\ud83d\udd0d Analizando M3U para detectar series via API...");try{var n,t;const r=await jn.m3uAPI.parseContent(e,"series",!1);if(!r.success)throw new Error(r.message||"Error parseando M3U");const a=(null===(n=r.parse_results)||void 0===n||null===(t=n.series)||void 0===t?void 0:t.data)||[];return window.debugLog("success",`\ud83c\udf89 Detectadas ${a.length} series en M3U via backend`),a}catch(r){return window.debugLog("error",`\u274c Error parseando M3U: ${r.message}`),window.debugLog("info","\ud83d\udd04 Intentando parsing local como fallback..."),yr(e)}})(await t.text());i(e);const n={};e.forEach(e=>{n[e.title]={category_id:[],tmdb_id:null,auto_fetch_metadata:!0,merge_similar:!1}}),v(n),window.debugLog("success",`\u2705 Detectadas ${e.length} series en el archivo`),s&&s(e)}catch(e){window.debugLog("error",`\u274c Error analizando M3U: ${e.message}`)}};return t&&0!==l.length?(0,r.jsxs)(dn,{className:"mt-3",children:[(0,r.jsxs)(dn.Header,{className:"d-flex justify-content-between align-items-center",children:[(0,r.jsxs)("h6",{className:"mb-0",children:["\ud83d\udcfa Series Detectadas (",l.length,")"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{variant:"outline-primary",size:"sm",onClick:()=>{o.size===l.length?c(new Set):c(new Set(l.map(e=>e.title)))},className:"me-2",children:o.size===l.length?"Deseleccionar Todo":"Seleccionar Todo"}),(0,r.jsxs)(hn,{bg:"info",children:[o.size," seleccionadas"]})]})]}),(0,r.jsx)(dn.Body,{style:{maxHeight:"400px",overflowY:"auto"},children:l.map((e,n)=>{var t,s,l;return(0,r.jsx)(dn,{className:"mb-3 border",children:(0,r.jsxs)(dn.Body,{className:"py-2",children:[(0,r.jsxs)(Me,{className:"align-items-center",children:[(0,r.jsx)(Fe,{md:1,children:(0,r.jsx)(gt.Check,{type:"checkbox",checked:o.has(e.title),onChange:n=>((e,n)=>{const t=new Set(o);n?t.add(e):t.delete(e),c(t)})(e.title,n.target.checked),disabled:a})}),(0,r.jsxs)(Fe,{md:4,children:[(0,r.jsx)("strong",{children:e.title}),(0,r.jsx)("br",{}),(0,r.jsxs)("small",{className:"text-muted",children:[e.episodes.length," episodios"]})]}),(0,r.jsx)(Fe,{md:2,children:(0,r.jsx)("div",{className:"d-flex flex-wrap gap-1",children:[...new Set(e.episodes.map(e=>`S${e.season_num}`))].map(e=>(0,r.jsx)(hn,{bg:"secondary",style:{fontSize:"0.7em"},children:e},e))})}),(0,r.jsx)(Fe,{md:2,children:m[e.title]?(0,r.jsx)(hn,{bg:"success",children:"\u2705 TMDB"}):(0,r.jsx)(w,{variant:"outline-info",size:"sm",onClick:()=>(async e=>{window.debugLog("info",`\ud83d\udd0d Buscando "${e}" en TMDB...`),setTimeout(()=>{p(n=>({...n,[e]:{found:!0,tmdb_id:Math.floor(1e5*Math.random()),title:e,overview:`Serie detectada: ${e}`,poster_path:"/mock-poster.jpg",first_air_date:"2023-01-01",vote_average:8.5}})),window.debugLog("success",`\u2705 Metadata encontrada para "${e}"`)},1e3)})(e.title),disabled:a,children:"\ud83d\udd0d TMDB"})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsxs)("div",{className:"d-flex gap-1",children:[(0,r.jsx)(w,{variant:"outline-primary",size:"sm",onClick:()=>(e=>{h(e),d(!0)})(e),children:"\ud83d\udc41\ufe0f Ver"}),(0,r.jsxs)(gt.Select,{size:"sm",value:(null===(t=g[e.title])||void 0===t||null===(s=t.category_id)||void 0===s?void 0:s[0])||"",onChange:n=>{return t=e.title,r="category_id",a=[parseInt(n.target.value)],void v(e=>({...e,[t]:{...e[t],[r]:a}}));var t,r,a},disabled:a,children:[(0,r.jsx)("option",{value:"",children:"Categor\xeda..."}),(0,r.jsx)("option",{value:"1",children:"Drama Series"}),(0,r.jsx)("option",{value:"2",children:"Comedy Series"}),(0,r.jsx)("option",{value:"3",children:"Action Series"}),(0,r.jsx)("option",{value:"4",children:"Sci-Fi Series"})]})]})})]}),m[e.title]&&(0,r.jsx)(Me,{className:"mt-2",children:(0,r.jsx)(Fe,{children:(0,r.jsx)(De,{variant:"success",className:"py-1 mb-0",children:(0,r.jsxs)("small",{children:[(0,r.jsx)("strong",{children:"TMDB:"})," ",m[e.title].title,"(",null===(l=m[e.title].first_air_date)||void 0===l?void 0:l.split("-")[0],") - \u2b50 ",m[e.title].vote_average]})})})})]})},`series-${e.title}-${n}`)})}),o.size>0&&(0,r.jsxs)(dn.Footer,{children:[(0,r.jsxs)(De,{variant:"success",className:"mb-2",children:["\u2705 ",o.size," series seleccionadas para importar"]}),(0,r.jsxs)(gt.Group,{className:"mb-2",children:[(0,r.jsx)(gt.Label,{children:"\u2699\ufe0f Configuraci\xf3n Global para Series Seleccionadas"}),(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{md:4,children:(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udd0d Auto-buscar metadata en TMDB",defaultChecked:!0})}),(0,r.jsx)(Fe,{md:4,children:(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udd17 Merge episodios similares",defaultChecked:!1})}),(0,r.jsx)(Fe,{md:4,children:(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udcdd Generar descripciones autom\xe1ticas",defaultChecked:!0})})]})]})]}),(0,r.jsxs)(vr,{show:u,onHide:()=>d(!1),size:"lg",children:[(0,r.jsx)(vr.Header,{closeButton:!0,children:(0,r.jsxs)(vr.Title,{children:["\ud83d\udcfa Preview: ",null===f||void 0===f?void 0:f.title]})}),(0,r.jsx)(vr.Body,{children:f&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Me,{className:"mb-3",children:(0,r.jsxs)(Fe,{children:[(0,r.jsx)("strong",{children:"Total de Episodios:"})," ",f.episodes.length,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Temporadas:"})," ",[...new Set(f.episodes.map(e=>e.season_num))].join(", ")]})}),(0,r.jsx)("div",{style:{maxHeight:"300px",overflowY:"auto"},children:(0,r.jsxs)(pn,{striped:!0,size:"sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Episodio"}),(0,r.jsx)("th",{children:"Nombre"}),(0,r.jsx)("th",{children:"Duraci\xf3n"})]})}),(0,r.jsx)("tbody",{children:f.episodes.sort((e,n)=>e.season_num-n.season_num||e.episode_num-n.episode_num).map((e,n)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsxs)(hn,{bg:"primary",children:["S",String(e.season_num).padStart(2,"0"),"E",String(e.episode_num).padStart(2,"0")]})}),(0,r.jsx)("td",{children:e.episode_title||"Sin t\xedtulo"}),(0,r.jsx)("td",{children:e.duration})]},`episode-S${e.season_num}E${e.episode_num}-${n}`))})]})})]})}),(0,r.jsx)(vr.Footer,{children:(0,r.jsx)(w,{variant:"secondary",onClick:()=>d(!1),children:"Cerrar"})})]})]}):(0,r.jsx)(De,{variant:"info",children:"\ud83d\udcfa Selecciona un archivo M3U para detectar series autom\xe1ticamente"})},wr=e=>{let{status:n,onRetry:t,showRetry:a=!0,size:s="sm"}=e;return(0,r.jsx)("div",{className:"backend-status",children:(()=>{switch(n){case"connected":return(0,r.jsxs)(hn,{bg:"success",className:"d-flex align-items-center gap-1",children:[(0,r.jsx)("i",{className:"bi bi-wifi"}),"Backend Conectado"]});case"error":return(0,r.jsxs)(hn,{bg:"danger",className:"d-flex align-items-center gap-1",children:[(0,r.jsx)("i",{className:"bi bi-wifi-off"}),"Backend Desconectado",a&&(0,r.jsx)(w,{variant:"outline-light",size:"sm",className:"ms-2 py-0 px-1",onClick:t,children:(0,r.jsx)("i",{className:"bi bi-arrow-clockwise"})})]});case"checking":return(0,r.jsxs)(hn,{bg:"secondary",className:"d-flex align-items-center gap-1",children:[(0,r.jsx)(g,{animation:"border",size:"sm"}),"Verificando Backend..."]});default:return(0,r.jsx)(hn,{bg:"warning",children:"Estado Desconocido"})}})()})},Sr=()=>{var n,t,a,s,l,i,o,c,u;const[d,f]=(0,e.useState)(null),[h,m]=(0,e.useState)(!1),[p,g]=(0,e.useState)(0),[v,b]=(0,e.useState)(!1),[y,x]=(0,e.useState)(""),[j,S]=(0,e.useState)("info"),[k,E]=(0,e.useState)("checking"),[N,C]=(0,e.useState)(null),[T,P]=(0,e.useState)(!1),_="series",[L,R]=(0,e.useState)(""),[O,z]=(0,e.useState)({directSource:!0,directProxy:!1,loadBalancing:!1}),[A,D]=(0,e.useState)([]),[I,M]=(0,e.useState)([]),[$,F]=(0,e.useState)([]),U=async()=>{try{const e=await xr();E(e.success?"connected":"error"),e.success||q("warning","Backend no disponible. Funcionando en modo offline.")}catch(e){E("error"),q("danger","No se puede conectar al backend")}},B=async()=>{try{console.log("\ud83d\udd04 Iniciando carga de servidores reales...");const e=await fetch("http://localhost:5001/api/database/streaming-servers");console.log("\ud83d\udce1 Respuesta del servidor:",e.status,e.statusText);const n=await e.json();if(console.log("\ud83d\udcca Datos recibidos:",n),!n.success||!n.data)throw console.error("\u274c Respuesta no exitosa:",n),new Error(n.error||"No se pudieron cargar servidores");{const e=n.data.map(e=>({id:e.server_id,name:e.server_name||`Server ${e.server_id}`,ip:e.server_ip||"Unknown IP",load:`${e.total_streams||0} streams`,total_streams:e.total_streams||0,status:1===e.server_status?"Active":"Inactive"}));console.log("\ud83d\udda5\ufe0f Servidores mapeados:",e),M(e),console.log("\u2705 Estado actualizado con",e.length,"servidores"),window.debugLog&&window.debugLog("success",`\u2705 Cargados ${e.length} servidores reales desde BD`)}}catch(e){throw console.error("\u274c Error cargando servidores:",e),window.debugLog&&window.debugLog("error",`\u274c Error cargando servidores: ${e.message}`),e}},H=async()=>{try{const e=await xn.getCategories();if(!e.success||!e.data)throw new Error("No se pudieron cargar categor\xedas");{const n=e.data.map(e=>({id:e.category_id,name:e.category_name,type:V(e.category_name),parent_id:e.parent_id}));F(n),window.debugLog&&window.debugLog("success",`\u2705 Cargadas ${n.length} categor\xedas reales desde BD`)}}catch(e){throw console.error("Error cargando categor\xedas:",e),window.debugLog&&window.debugLog("error",`\u274c Error cargando categor\xedas: ${e.message}`),e}},V=e=>{const n=e.toLowerCase();return n.includes("movie")||n.includes("film")||n.includes("cinema")?"movie":n.includes("series")||n.includes("show")||n.includes("drama")?"series":n.includes("live")||n.includes("tv")||n.includes("channel")||n.includes("news")||n.includes("sport")?"live":n.includes("radio")||n.includes("music")||n.includes("fm")?"radio":"live"},W=()=>{M([{id:1,name:"Main Server US",ip:"*************",load:"45%"},{id:2,name:"EU Server",ip:"*************",load:"32%"},{id:3,name:"Asia Server",ip:"*************",load:"67%"},{id:4,name:"Backup Server",ip:"*************",load:"12%"}]),F([{id:1,name:"Action Movies",type:"movie"},{id:2,name:"Comedy Movies",type:"movie"},{id:3,name:"Drama Series",type:"series"},{id:4,name:"Comedy Series",type:"series"},{id:5,name:"News Channels",type:"live"},{id:6,name:"Sports Channels",type:"live"},{id:7,name:"Entertainment",type:"live"},{id:8,name:"Music Radio",type:"radio"},{id:9,name:"Talk Radio",type:"radio"}]),window.debugLog&&window.debugLog("warning","\u26a0\ufe0f Usando datos mock - backend no disponible")},q=(e,n)=>{!function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";x(e),S(n),b(!0),setTimeout(()=>b(!1),5e3)}(n,e)};(0,e.useEffect)(()=>{U()},[]),(0,e.useEffect)(()=>{"checking"!==k&&(async()=>{try{console.log(`\ud83d\udd04 Cargando datos iniciales. Backend status: ${k}`),"connected"===k?(console.log("\u2705 Backend conectado, cargando datos reales..."),await B(),await H()):(console.log("\u26a0\ufe0f Backend no conectado, usando datos mock..."),W())}catch(e){console.error("Error cargando datos iniciales:",e),window.debugLog&&window.debugLog("error",`Error cargando datos iniciales: ${e.message}`),W()}})()},[k]);const K=async e=>{const n=e.target.files[0];f(n),C(null),n&&(q("info",`Archivo seleccionado: ${n.name} (${(n.size/1024/1024).toFixed(2)} MB)`),window.debugLog&&(window.debugLog(`\ud83d\udcc1 File selected: ${n.name}`,"info"),window.debugLog(`\ud83d\udcca File size: ${(n.size/1024/1024).toFixed(2)} MB`,"info")))};return(0,r.jsxs)("div",{style:{width:"100%",maxWidth:"none"},children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{className:"text-primary",children:"\ud83d\udcfa Import Series M3U Files"}),(0,r.jsx)(wr,{status:k,onRetry:U})]}),v&&(0,r.jsxs)(De,{variant:j,dismissible:!0,onClose:()=>b(!1),children:[(0,r.jsxs)(De.Heading,{children:["success"===j&&"\u2705 Success!","danger"===j&&"\u274c Error!","warning"===j&&"\u26a0\ufe0f Warning!","info"===j&&"\u2139\ufe0f Information"]}),(0,r.jsx)("p",{children:y})]}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsxs)(dn.Header,{className:"bg-primary text-white d-flex justify-content-between align-items-center",children:[(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcc2 File Upload"}),"connected"===k&&(0,r.jsxs)(hn,{bg:"success",children:[(0,r.jsx)("i",{className:"bi bi-cloud-check"})," Backend Ready"]})]}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(gt,{children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"Select M3U File"}),(0,r.jsx)(gt.Control,{type:"file",accept:".m3u,.m3u8",onChange:K,disabled:h}),(0,r.jsx)(gt.Text,{className:"text-muted",children:"Supported formats: .m3u, .m3u8 (Max size: 50MB)"})]}),d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("strong",{children:"Selected File:"})," ",d.name,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Size:"})," ",(d.size/1024/1024).toFixed(2)," MB"]}),T&&(0,r.jsx)(De,{variant:"secondary",children:(0,r.jsxs)("div",{className:"d-flex align-items-center",children:[(0,r.jsx)("div",{className:"spinner-border spinner-border-sm me-2",role:"status"}),"Analizando archivo..."]})}),N&&!T&&(0,r.jsxs)(De,{variant:"success",children:[(0,r.jsx)("h6",{children:"\ud83d\udcca An\xe1lisis del Archivo"}),(0,r.jsxs)(Me,{children:[(0,r.jsxs)(Fe,{md:6,children:[(0,r.jsx)("strong",{children:"Total Lines:"})," ",(null===(n=N.basic_analysis)||void 0===n?void 0:n.total_lines)||0,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"EXTINF Entries:"})," ",(null===(t=N.basic_analysis)||void 0===t?void 0:t.extinf_lines)||0,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"URL Entries:"})," ",(null===(a=N.basic_analysis)||void 0===a?void 0:a.url_lines)||0]}),(0,r.jsxs)(Fe,{md:6,children:[(0,r.jsx)("strong",{children:"Estimated Entries:"})," ",(null===(s=N.basic_analysis)||void 0===s?void 0:s.estimated_entries)||0,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Valid M3U:"})," ",null!==(l=N.basic_analysis)&&void 0!==l&&l.has_valid_m3u_header?"\u2705 Yes":"\u274c No",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"File Size:"})," ",(null===(i=N.file_info)||void 0===i?void 0:i.size_mb)||0," MB"]})]}),(null===(o=N.parse_results)||void 0===o||null===(c=o.series)||void 0===c?void 0:c.success)&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("strong",{children:"Series Detected:"})," ",(null===(u=N.parse_results.series.data)||void 0===u?void 0:u.length)||0]})]})]}),(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)(w,{onClick:()=>{const e=document.createElement("input");e.type="file",e.accept=".m3u,.m3u8",e.onchange=e=>{const n=e.target.files[0];n&&K({target:{files:[n]}})},e.click()},variant:"primary",disabled:T,children:"Seleccionar Archivo M3U"}),d&&!N&&(0,r.jsx)(w,{onClick:async()=>{if(d){P(!0),C(null);try{q("info","Analizando archivo M3U...");const n=await jn.m3uAPI.analyzeFile(d);if(!n.success)throw new Error(n.error||"Error analizando archivo");var e;C(n.data),q("success",`\u2705 Archivo analizado correctamente. Se detectaron ${(null===(e=n.data.basic_analysis)||void 0===e?void 0:e.estimated_entries)||0} entradas. Configurado para importar como series.`)}catch(n){console.error("Error analyzing file:",n),q("danger",`\u274c Error analizando archivo: ${n.message}`)}finally{P(!1)}}},variant:"info",className:"ms-2",disabled:T,children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status"}),"Analizando..."]}):"Analizar Archivo"}),N&&(0,r.jsx)(w,{onClick:()=>{C(null),f(null),q("info","An\xe1lisis limpiado. Selecciona un nuevo archivo.")},variant:"outline-secondary",className:"ms-2",disabled:T,children:"Limpiar An\xe1lisis"})]}),(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsxs)("div",{className:"alert alert-info",children:[(0,r.jsx)("strong",{children:"\ud83c\udfaf Tipo de Contenido:"})," \ud83d\udcfa Series/Episodios (fijo)",(0,r.jsx)("br",{}),(0,r.jsx)("small",{children:"Este importador est\xe1 configurado espec\xedficamente para series y episodios."})]})}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-2",children:[(0,r.jsx)(gt.Label,{className:"mb-0",children:"\ud83d\udda5\ufe0f Target Streams Server"}),(0,r.jsx)(w,{variant:"outline-secondary",size:"sm",onClick:B,disabled:h||"connected"!==k,children:"\ud83d\udd04 Refresh"})]}),(0,r.jsxs)(gt.Select,{value:L,onChange:e=>R(e.target.value),disabled:h,children:[(0,r.jsx)("option",{value:"",children:"Select streams server..."}),I.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.name," (",e.ip,") - ",e.load]},e.id))]}),(0,r.jsxs)(gt.Text,{className:"text-muted",children:["Server where streams will be hosted and served from.",I.length>0&&(0,r.jsxs)("span",{className:"text-success",children:[" \u2705 ",I.length," servers loaded"]})]})]}),L&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udd17 Source Configuration"}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\u2705 Direct Source (recommended for better performance)",checked:O.directSource,onChange:e=>z(n=>({...n,directSource:e.target.checked})),disabled:h}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udd04 Direct Proxy (for geo-restricted content)",checked:O.directProxy,onChange:e=>z(n=>({...n,directProxy:e.target.checked})),disabled:h}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\u2696\ufe0f Load Balancing (distribute across servers)",checked:O.loadBalancing,onChange:e=>z(n=>({...n,loadBalancing:e.target.checked})),disabled:h})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83c\udff7\ufe0f Categories Assignment"}),(0,r.jsx)("div",{style:{maxHeight:"120px",overflowY:"auto",border:"1px solid #ddd",padding:"8px",borderRadius:"4px"},children:$.filter(e=>"series"===e.type||"live"===e.type).map(e=>(0,r.jsx)(gt.Check,{type:"checkbox",label:`${e.name} (${e.type})`,checked:A.includes(e.id),onChange:n=>{n.target.checked?D(n=>[...n,e.id]):D(n=>n.filter(n=>n!==e.id))},disabled:h},e.id))}),(0,r.jsx)(gt.Text,{className:"text-muted",children:"Select existing categories or new ones will be created automatically"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\u2699\ufe0f Import Settings"}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udd04 Auto-rename with TMDB data",defaultChecked:!0}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udcc2 Auto-assign categories",defaultChecked:!0}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83c\udfac Process VOD metadata",defaultChecked:!0})]}),h&&(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"Import Progress"}),(0,r.jsx)(xt,{now:p,label:`${p}%`,variant:100===p?"success":"primary",animated:p<100})]}),d&&(0,r.jsx)(jr,{selectedFile:d,isImporting:h,onSeriesDetected:e=>{window.debugLog("info",`\ud83d\udcfa Detectadas ${e.length} series para importar`)}}),(0,r.jsx)(w,{variant:"success",size:"lg",onClick:async()=>{if(d&&L)if(N){window.debugLog&&(window.debugLog(`\ud83d\udce5 Starting import of ${d.name}`,"info"),window.debugLog(`\ud83d\udcca File size: ${(d.size/1024/1024).toFixed(2)} MB`,"info"),window.debugLog(`\ud83c\udfaf Content type: ${_}`,"info"),window.debugLog(`\ud83d\udda5\ufe0f Target server: ${L}`,"info")),m(!0),g(0);try{const e={contentType:_,streamsServer:L,sourceConfig:O,categories:A,tmdbEnabled:!0,autoAssignCategories:!0};q("info","\ud83d\udd0d Iniciando proceso de importaci\xf3n..."),g(10);const n=new FormData;n.append("file",d),n.append("config",JSON.stringify(e)),window.debugLog&&window.debugLog("\ud83d\udce4 Uploading file to backend...","info");const t=await jn.m3uAPI.analyzeFile(d);if(g(30),!t.success)throw new Error(t.error||"Error analyzing file");q("info","\ud83c\udfaf Archivo subido, procesando contenido...");const r=await new Promise((e,n)=>{const t=new FileReader;t.onload=n=>e(n.target.result),t.onerror=()=>n(new Error("Error reading file")),t.readAsText(d)});g(40);const a=await fetch("http://localhost:5001/api/import/parse-episodes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({m3uContent:r})}),s=await a.json();if(!s.success)throw new Error(s.error||"Error parsing content");g(60);const l={episodes:s.data.episodes,server_id:parseInt(L),category_id:A.length>0?parseInt(A[0]):null,tmdb_search:O.tmdbEnrichment||!0},i=await fetch("http://localhost:5001/api/import/episodes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)}),o=await i.json();if(console.log("\ud83d\udd0d Import Response:",o),!o.success)throw new Error(o.error||"Error importing content");g(100);const c=o.data||o,u=`\u2705 Importaci\xf3n completada exitosamente!\n\ud83d\udcca Estad\xedsticas:\n\u2022 ${c.imported||0} elementos importados\n\u2022 ${c.errors||0} errores\n\u2022 ${c.series_created||0} series creadas\n\u2022 ${c.episodes_created||0} episodios creados`;q("success",u),window.debugLog&&(window.debugLog(`\u2705 Import completed successfully: ${d.name}`,"success"),window.debugLog(`\ud83d\udcca Stats: ${JSON.stringify(o)}`,"info")),setTimeout(()=>{f(null),C(null),R(""),D([])},3e3)}catch(e){console.error("Import error:",e),q("danger",`\u274c Error durante la importaci\xf3n: ${e.message}`),window.debugLog&&window.debugLog(`\u274c Import failed: ${e.message}`,"error")}finally{m(!1)}}else q("warning","\u26a0\ufe0f Por favor analiza el archivo antes de importar.");else q("warning","\u26a0\ufe0f Por favor completa todos los campos requeridos (archivo y servidor).")},disabled:!d||!N||!L||h||T,className:"w-100",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status"}),"Importando... ",p,"%"]}):d?N?L?"\ud83d\ude80 Iniciar Importaci\xf3n":"\u2699\ufe0f Selecciona un servidor":"\ufffd Analiza el archivo primero":"\ud83d\udcc1 Selecciona un archivo M3U"})]})})]})}),(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-info text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\u2139\ufe0f Series Import Guidelines"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsx)("h6",{children:"\ud83d\udccb Supported Content Types:"}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\udcfa Live Channels:"})," TV channels with EPG support"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfac VOD (Movies):"})," On-demand movie content"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\udcda Series:"})," TV series with episode management"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfb5 Radio:"})," Audio streaming channels"]})]}),(0,r.jsx)("h6",{children:"\u2699\ufe0f Processing Features:"}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfaf TMDB Integration:"})," Auto-fetch metadata"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udff7\ufe0f Category Assignment:"})," Smart categorization"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\uddbc\ufe0f Poster Download:"})," High-quality artwork"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\udcdd Description Parsing:"})," Extract show info"]})]}),(0,r.jsx)("h6",{children:"\u26a1 Performance Tips:"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Files under 10MB import faster"}),(0,r.jsx)("li",{children:"Use UTF-8 encoding for special characters"}),(0,r.jsx)("li",{children:"Clean duplicate entries before import"}),(0,r.jsx)("li",{children:"Ensure stable internet for metadata fetching"})]})]})]})})]}),(0,r.jsx)(Me,{children:(0,r.jsx)(Fe,{children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-secondary text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcca Recent Series Import Queue"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(pn,{striped:!0,hover:!0,children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"\ud83d\udcc4 File"}),(0,r.jsx)("th",{children:"\ud83d\udcc5 Queued"}),(0,r.jsx)("th",{children:"\ud83d\udcca Status"}),(0,r.jsx)("th",{children:"\ud83c\udfaf Target"}),(0,r.jsx)("th",{children:"\u26a1 Actions"})]})}),(0,r.jsxs)("tbody",{children:[(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"premium_list.m3u"})}),(0,r.jsx)("td",{children:"2025-07-15 15:30"}),(0,r.jsx)("td",{children:(0,r.jsx)("span",{className:"badge bg-warning",children:"\u23f3 Queued"})}),(0,r.jsx)("td",{children:"Main Server"}),(0,r.jsxs)("td",{children:[(0,r.jsx)(w,{size:"sm",variant:"outline-primary",className:"me-1",children:"\u25b6\ufe0f"}),(0,r.jsx)(w,{size:"sm",variant:"outline-danger",children:"\u274c"})]})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"sports_channels.m3u"})}),(0,r.jsx)("td",{children:"2025-07-15 15:25"}),(0,r.jsx)("td",{children:(0,r.jsx)("span",{className:"badge bg-success",children:"\u2705 Processing"})}),(0,r.jsx)("td",{children:"Cloud Server"}),(0,r.jsx)("td",{children:(0,r.jsx)(w,{size:"sm",variant:"outline-info",children:"\ud83d\udcca"})})]})]})]})})]})})})]})},kr=()=>{var n,t,a,s,l,i,o,c,u;const[d,f]=(0,e.useState)(null),[h,m]=(0,e.useState)(!1),[p,g]=(0,e.useState)(0),[v,b]=(0,e.useState)(!1),[y,x]=(0,e.useState)(""),[j,S]=(0,e.useState)("info"),[k,E]=(0,e.useState)("checking"),[N,C]=(0,e.useState)(null),[T,P]=(0,e.useState)(!1),_="movie",[L,R]=(0,e.useState)(""),[O,z]=(0,e.useState)({directSource:!0,directProxy:!1,loadBalancing:!1}),[A,D]=(0,e.useState)([]),[I,M]=(0,e.useState)([]),[$,F]=(0,e.useState)([]),U=async()=>{try{const e=await xr();E(e.success?"connected":"error"),e.success||q("warning","Backend no disponible. Funcionando en modo offline.")}catch(e){E("error"),q("danger","No se puede conectar al backend")}},B=async()=>{try{console.log("\ud83d\udd04 Iniciando carga de servidores reales...");const e=await fetch("http://localhost:5001/api/database/streaming-servers");console.log("\ud83d\udce1 Respuesta del servidor:",e.status,e.statusText);const n=await e.json();if(console.log("\ud83d\udcca Datos recibidos:",n),!n.success||!n.data)throw console.error("\u274c Respuesta no exitosa:",n),new Error(n.error||"No se pudieron cargar servidores");{const e=n.data.map(e=>({id:e.server_id,name:e.server_name||`Server ${e.server_id}`,ip:e.server_ip||"Unknown IP",load:`${e.total_streams||0} streams`,total_streams:e.total_streams||0,status:1===e.server_status?"Active":"Inactive"}));console.log("\ud83d\udda5\ufe0f Servidores mapeados:",e),M(e),console.log("\u2705 Estado actualizado con",e.length,"servidores"),window.debugLog&&window.debugLog("success",`\u2705 Cargados ${e.length} servidores reales desde BD`)}}catch(e){throw console.error("\u274c Error cargando servidores:",e),window.debugLog&&window.debugLog("error",`\u274c Error cargando servidores: ${e.message}`),e}},H=async()=>{try{const e=await xn.getCategories();if(!e.success||!e.data)throw new Error("No se pudieron cargar categor\xedas");{const n=e.data.map(e=>({id:e.category_id,name:e.category_name,type:V(e.category_name),parent_id:e.parent_id}));F(n),window.debugLog&&window.debugLog("success",`\u2705 Cargadas ${n.length} categor\xedas reales desde BD`)}}catch(e){throw console.error("Error cargando categor\xedas:",e),window.debugLog&&window.debugLog("error",`\u274c Error cargando categor\xedas: ${e.message}`),e}},V=e=>{const n=e.toLowerCase();return n.includes("movie")||n.includes("film")||n.includes("cinema")?"movie":n.includes("series")||n.includes("show")||n.includes("drama")?"series":n.includes("live")||n.includes("tv")||n.includes("channel")||n.includes("news")||n.includes("sport")?"live":n.includes("radio")||n.includes("music")||n.includes("fm")?"radio":"movie"},W=()=>{M([{id:1,name:"Main Server US",ip:"*************",load:"45%"},{id:2,name:"EU Server",ip:"*************",load:"32%"},{id:3,name:"Asia Server",ip:"*************",load:"67%"},{id:4,name:"Backup Server",ip:"*************",load:"12%"}]),F([{id:1,name:"Action Movies",type:"movie"},{id:2,name:"Comedy Movies",type:"movie"},{id:3,name:"Drama Movies",type:"movie"},{id:4,name:"Horror Movies",type:"movie"},{id:5,name:"Sci-Fi Movies",type:"movie"},{id:6,name:"Documentary",type:"movie"},{id:7,name:"Animation",type:"movie"},{id:8,name:"Thriller",type:"movie"},{id:9,name:"Romance",type:"movie"}]),window.debugLog&&window.debugLog("warning","\u26a0\ufe0f Usando datos mock - backend no disponible")},q=(e,n)=>{!function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";x(e),S(n),b(!0),setTimeout(()=>b(!1),5e3)}(n,e)};(0,e.useEffect)(()=>{U()},[]),(0,e.useEffect)(()=>{"checking"!==k&&(async()=>{try{console.log(`\ud83d\udd04 Cargando datos iniciales. Backend status: ${k}`),"connected"===k?(console.log("\u2705 Backend conectado, cargando datos reales..."),await B(),await H()):(console.log("\u26a0\ufe0f Backend no conectado, usando datos mock..."),W())}catch(e){console.error("Error cargando datos iniciales:",e),window.debugLog&&window.debugLog("error",`Error cargando datos iniciales: ${e.message}`),W()}})()},[k]);const K=async e=>{const n=e.target.files[0];f(n),C(null),n&&(q("info",`Archivo seleccionado: ${n.name} (${(n.size/1024/1024).toFixed(2)} MB)`),window.debugLog&&(window.debugLog(`\ud83d\udcc1 File selected: ${n.name}`,"info"),window.debugLog(`\ud83d\udcca File size: ${(n.size/1024/1024).toFixed(2)} MB`,"info")))};return(0,r.jsxs)("div",{style:{width:"100%",maxWidth:"none"},children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{className:"text-primary",children:"\ud83c\udfac Import VOD M3U Files"}),(0,r.jsx)(wr,{status:k,onRetry:U})]}),v&&(0,r.jsxs)(De,{variant:j,dismissible:!0,onClose:()=>b(!1),children:[(0,r.jsxs)(De.Heading,{children:["success"===j&&"\u2705 Success!","danger"===j&&"\u274c Error!","warning"===j&&"\u26a0\ufe0f Warning!","info"===j&&"\u2139\ufe0f Information"]}),(0,r.jsx)("p",{children:y})]}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsxs)(dn.Header,{className:"bg-primary text-white d-flex justify-content-between align-items-center",children:[(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcc2 File Upload"}),"connected"===k&&(0,r.jsxs)(hn,{bg:"success",children:[(0,r.jsx)("i",{className:"bi bi-cloud-check"})," Backend Ready"]})]}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(gt,{children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"Select M3U File"}),(0,r.jsx)(gt.Control,{type:"file",accept:".m3u,.m3u8",onChange:K,disabled:h}),(0,r.jsx)(gt.Text,{className:"text-muted",children:"Supported formats: .m3u, .m3u8 (Max size: 50MB)"})]}),d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("strong",{children:"Selected File:"})," ",d.name,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Size:"})," ",(d.size/1024/1024).toFixed(2)," MB"]}),T&&(0,r.jsx)(De,{variant:"secondary",children:(0,r.jsxs)("div",{className:"d-flex align-items-center",children:[(0,r.jsx)("div",{className:"spinner-border spinner-border-sm me-2",role:"status"}),"Analizando archivo..."]})}),N&&!T&&(0,r.jsxs)(De,{variant:"success",children:[(0,r.jsx)("h6",{children:"\ud83d\udcca An\xe1lisis del Archivo"}),(0,r.jsxs)(Me,{children:[(0,r.jsxs)(Fe,{md:6,children:[(0,r.jsx)("strong",{children:"Total Lines:"})," ",(null===(n=N.basic_analysis)||void 0===n?void 0:n.total_lines)||0,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"EXTINF Entries:"})," ",(null===(t=N.basic_analysis)||void 0===t?void 0:t.extinf_lines)||0,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"URL Entries:"})," ",(null===(a=N.basic_analysis)||void 0===a?void 0:a.url_lines)||0]}),(0,r.jsxs)(Fe,{md:6,children:[(0,r.jsx)("strong",{children:"Estimated Entries:"})," ",(null===(s=N.basic_analysis)||void 0===s?void 0:s.estimated_entries)||0,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Valid M3U:"})," ",null!==(l=N.basic_analysis)&&void 0!==l&&l.has_valid_m3u_header?"\u2705 Yes":"\u274c No",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"File Size:"})," ",(null===(i=N.file_info)||void 0===i?void 0:i.size_mb)||0," MB"]})]}),(null===(o=N.parse_results)||void 0===o||null===(c=o.movies)||void 0===c?void 0:c.success)&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("strong",{children:"Movies Detected:"})," ",(null===(u=N.parse_results.movies.data)||void 0===u?void 0:u.length)||0]})]})]}),(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)(w,{onClick:()=>{const e=document.createElement("input");e.type="file",e.accept=".m3u,.m3u8",e.onchange=e=>{const n=e.target.files[0];n&&K({target:{files:[n]}})},e.click()},variant:"primary",disabled:T,children:"Seleccionar Archivo M3U"}),d&&!N&&(0,r.jsx)(w,{onClick:async()=>{if(d){P(!0),C(null);try{q("info","Analizando archivo M3U...");const n=await jn.m3uAPI.analyzeFile(d);if(!n.success)throw new Error(n.error||"Error analizando archivo");var e;C(n.data),q("success",`\u2705 Archivo analizado correctamente. Se detectaron ${(null===(e=n.data.basic_analysis)||void 0===e?void 0:e.estimated_entries)||0} entradas. Configurado para importar como pel\xedculas/VOD.`)}catch(n){console.error("Error analyzing file:",n),q("danger",`\u274c Error analizando archivo: ${n.message}`)}finally{P(!1)}}},variant:"info",className:"ms-2",disabled:T,children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status"}),"Analizando..."]}):"Analizar Archivo"}),N&&(0,r.jsx)(w,{onClick:()=>{C(null),f(null),q("info","An\xe1lisis limpiado. Selecciona un nuevo archivo.")},variant:"outline-secondary",className:"ms-2",disabled:T,children:"Limpiar An\xe1lisis"})]}),(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsxs)("div",{className:"alert alert-info",children:[(0,r.jsx)("strong",{children:"\ud83c\udfaf Tipo de Contenido:"})," \ud83c\udfac Pel\xedculas/VOD (fijo)",(0,r.jsx)("br",{}),(0,r.jsx)("small",{children:"Este importador est\xe1 configurado espec\xedficamente para pel\xedculas y contenido VOD."})]})}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-2",children:[(0,r.jsx)(gt.Label,{className:"mb-0",children:"\ud83d\udda5\ufe0f Target Streams Server"}),(0,r.jsx)(w,{variant:"outline-secondary",size:"sm",onClick:B,disabled:h||"connected"!==k,children:"\ud83d\udd04 Refresh"})]}),(0,r.jsxs)(gt.Select,{value:L,onChange:e=>R(e.target.value),disabled:h,children:[(0,r.jsx)("option",{value:"",children:"Select streams server..."}),I.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.name," (",e.ip,") - ",e.load]},e.id))]}),(0,r.jsxs)(gt.Text,{className:"text-muted",children:["Server where VOD content will be hosted and served from.",I.length>0&&(0,r.jsxs)("span",{className:"text-success",children:[" \u2705 ",I.length," servers loaded"]})]})]}),L&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udd17 Source Configuration"}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\u2705 Direct Source (recommended for better performance)",checked:O.directSource,onChange:e=>z(n=>({...n,directSource:e.target.checked})),disabled:h}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udd04 Direct Proxy (for geo-restricted content)",checked:O.directProxy,onChange:e=>z(n=>({...n,directProxy:e.target.checked})),disabled:h}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\u2696\ufe0f Load Balancing (distribute across servers)",checked:O.loadBalancing,onChange:e=>z(n=>({...n,loadBalancing:e.target.checked})),disabled:h})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83c\udff7\ufe0f Categories Assignment"}),(0,r.jsx)("div",{style:{maxHeight:"120px",overflowY:"auto",border:"1px solid #ddd",padding:"8px",borderRadius:"4px"},children:$.filter(e=>"movie"===e.type||"live"===e.type).map(e=>(0,r.jsx)(gt.Check,{type:"checkbox",label:`${e.name} (${e.type})`,checked:A.includes(e.id),onChange:n=>{n.target.checked?D(n=>[...n,e.id]):D(n=>n.filter(n=>n!==e.id))},disabled:h},e.id))}),(0,r.jsx)(gt.Text,{className:"text-muted",children:"Select existing movie categories or new ones will be created automatically"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\u2699\ufe0f Import Settings"}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udd04 Auto-rename with TMDB data",defaultChecked:!0}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83d\udcc2 Auto-assign categories",defaultChecked:!0}),(0,r.jsx)(gt.Check,{type:"checkbox",label:"\ud83c\udfac Process movie metadata",defaultChecked:!0})]}),h&&(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"Import Progress"}),(0,r.jsx)(xt,{now:p,label:`${p}%`,variant:100===p?"success":"primary",animated:p<100})]}),(0,r.jsx)(w,{variant:"success",size:"lg",onClick:async()=>{if(d&&L)if(N){window.debugLog&&(window.debugLog(`\ud83d\udce5 Starting VOD import of ${d.name}`,"info"),window.debugLog(`\ud83d\udcca File size: ${(d.size/1024/1024).toFixed(2)} MB`,"info"),window.debugLog(`\ud83c\udfaf Content type: ${_}`,"info"),window.debugLog(`\ud83d\udda5\ufe0f Target server: ${L}`,"info")),m(!0),g(0);try{const e={contentType:_,streamsServer:L,sourceConfig:O,categories:A,tmdbEnabled:!0,autoAssignCategories:!0};q("info","\ud83d\udd0d Iniciando proceso de importaci\xf3n VOD..."),g(10);const n=new FormData;n.append("file",d),n.append("config",JSON.stringify(e)),window.debugLog&&window.debugLog("\ud83d\udce4 Uploading VOD file to backend...","info");const t=await jn.m3uAPI.analyzeFile(d);if(g(30),!t.success)throw new Error(t.error||"Error analyzing file");q("info","\ud83c\udfaf Archivo subido, procesando contenido VOD...");const r=await new Promise((e,n)=>{const t=new FileReader;t.onload=n=>e(n.target.result),t.onerror=()=>n(new Error("Error reading file")),t.readAsText(d)});g(40);const a=await fetch("http://localhost:5001/api/import/parse-movies",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({m3uContent:r})}),s=await a.json();if(!s.success)throw new Error(s.error||"Error parsing VOD content");g(60);const l={movies:s.data.movies,server_id:parseInt(L),category_id:A.length>0?parseInt(A[0]):null,tmdb_search:O.tmdbEnrichment||!0},i=await fetch("http://localhost:5001/api/import/movies",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)}),o=await i.json();if(console.log("\ud83d\udd0d VOD Import Response:",o),!o.success)throw new Error(o.error||"Error importing VOD content");g(100);const c=o.data||o,u=`\u2705 Importaci\xf3n VOD completada exitosamente!\n\ud83d\udcca Estad\xedsticas:\n\u2022 ${c.imported||0} elementos importados\n\u2022 ${c.errors||0} errores\n\u2022 ${c.movies_created||0} pel\xedculas creadas\n\u2022 ${c.metadata_enriched||0} con metadata TMDB`;q("success",u),window.debugLog&&(window.debugLog(`\u2705 VOD Import completed successfully: ${d.name}`,"success"),window.debugLog(`\ud83d\udcca Stats: ${JSON.stringify(o)}`,"info")),setTimeout(()=>{f(null),C(null),R(""),D([])},3e3)}catch(e){console.error("VOD Import error:",e),q("danger",`\u274c Error durante la importaci\xf3n VOD: ${e.message}`),window.debugLog&&window.debugLog(`\u274c VOD Import failed: ${e.message}`,"error")}finally{m(!1)}}else q("warning","\u26a0\ufe0f Por favor analiza el archivo antes de importar.");else q("warning","\u26a0\ufe0f Por favor completa todos los campos requeridos (archivo y servidor).")},disabled:!d||!N||!L||h||T,className:"w-100",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status"}),"Importando... ",p,"%"]}):d?N?L?"\ud83d\ude80 Iniciar Importaci\xf3n VOD":"\u2699\ufe0f Selecciona un servidor":"\ud83d\udd0d Analiza el archivo primero":"\ud83d\udcc1 Selecciona un archivo M3U"})]})})]})}),(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-info text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\u2139\ufe0f VOD Import Guidelines"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsx)("h6",{children:"\ud83d\udccb Supported VOD Content:"}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfac Movies:"})," Feature films and documentaries"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfad Short Films:"})," Independent and festival content"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\udcfa Specials:"})," TV movies and special events"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfaa Stand-up:"})," Comedy specials and performances"]})]}),(0,r.jsx)("h6",{children:"\u2699\ufe0f Processing Features:"}),(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udfaf TMDB Integration:"})," Auto-fetch movie metadata"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83c\udff7\ufe0f Category Assignment:"})," Smart movie categorization"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\uddbc\ufe0f Poster Download:"})," High-quality movie artwork"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"\ud83d\udcdd Description Parsing:"})," Extract movie info"]})]}),(0,r.jsx)("h6",{children:"\u26a1 Performance Tips:"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Files under 10MB import faster"}),(0,r.jsx)("li",{children:"Use UTF-8 encoding for special characters"}),(0,r.jsx)("li",{children:"Clean duplicate entries before import"}),(0,r.jsx)("li",{children:"Ensure stable internet for TMDB metadata"})]})]})]})})]}),(0,r.jsx)(Me,{children:(0,r.jsx)(Fe,{children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-secondary text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcca Recent VOD Import Queue"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(pn,{striped:!0,hover:!0,children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"\ud83d\udcc4 File"}),(0,r.jsx)("th",{children:"\ud83d\udcc5 Queued"}),(0,r.jsx)("th",{children:"\ud83d\udcca Status"}),(0,r.jsx)("th",{children:"\ud83c\udfaf Target"}),(0,r.jsx)("th",{children:"\u26a1 Actions"})]})}),(0,r.jsxs)("tbody",{children:[(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"movies_collection.m3u"})}),(0,r.jsx)("td",{children:"2025-07-15 16:30"}),(0,r.jsx)("td",{children:(0,r.jsx)("span",{className:"badge bg-warning",children:"\u23f3 Queued"})}),(0,r.jsx)("td",{children:"Main Server"}),(0,r.jsxs)("td",{children:[(0,r.jsx)(w,{size:"sm",variant:"outline-primary",className:"me-1",children:"\u25b6\ufe0f"}),(0,r.jsx)(w,{size:"sm",variant:"outline-danger",children:"\u274c"})]})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"action_movies.m3u"})}),(0,r.jsx)("td",{children:"2025-07-15 16:25"}),(0,r.jsx)("td",{children:(0,r.jsx)("span",{className:"badge bg-success",children:"\u2705 Processing"})}),(0,r.jsx)("td",{children:"Cloud Server"}),(0,r.jsx)("td",{children:(0,r.jsx)(w,{size:"sm",variant:"outline-info",children:"\ud83d\udcca"})})]})]})]})})]})})})]})},Er=e=>{let{showCard:n=!0,showControls:t=!0}=e;const{state:a,cancelDataReading:s,resetDataReading:l,startDataReading:i}=In(),{dataReading:o}=a,c=(()=>{var e,n,t,r;if(!o.result)return null;const{contentAnalysis:a,streamTypes:s,totalStreams:l}=o.result;return{total:l||0,live:(null===a||void 0===a||null===(e=a.live)||void 0===e?void 0:e.count)||0,movies:(null===a||void 0===a||null===(n=a.movies)||void 0===n?void 0:n.count)||0,series:(null===a||void 0===a||null===(t=a.series)||void 0===t?void 0:t.count)||0,other:(null===a||void 0===a||null===(r=a.other)||void 0===r?void 0:r.count)||0,types:Object.keys(s||{}).length}})(),u=(e=>{switch(e){case"starting":case"initializing":return"info";case"stream_types":case"stream_types_complete":return"primary";case"streams":case"streams_page":case"streams_complete":case"complete":return"success";case"series_structure":return"warning";case"error":return"danger";default:return"secondary"}})(o.currentStep),d=(e=>{switch(e){case"starting":case"initializing":return"\ud83d\ude80";case"stream_types":case"stream_types_complete":return"\ud83c\udfef";case"streams":case"streams_page":case"streams_complete":return"\ud83d\udfe2";case"series_structure":return"\ud83d\udd34";case"complete":return"\ud83c\udf89";case"error":return"\u274c";default:return"\u23f3"}})(o.currentStep),f=(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"d-flex align-items-center mb-3",children:[(0,r.jsx)("span",{className:"me-2",style:{fontSize:"1.5rem"},children:d}),(0,r.jsxs)("div",{className:"flex-grow-1",children:[(0,r.jsx)("h6",{className:"mb-1",children:o.currentMessage||"Esperando..."}),(0,r.jsxs)("small",{className:"text-muted",children:["Paso: ",o.currentStep||"none"]})]}),o.progress>0&&(0,r.jsxs)(hn,{bg:u,className:"ms-2",children:[Math.round(o.progress),"%"]})]}),(0,r.jsx)(xt,{now:o.progress,variant:u,className:"mb-3",style:{height:"8px"},animated:o.isReading}),o.error&&(0,r.jsxs)(De,{variant:"danger",className:"mb-3",children:[(0,r.jsx)("strong",{children:"\u274c Error:"})," ",o.error]}),c&&(0,r.jsxs)(Me,{className:"text-center",children:[(0,r.jsx)(Fe,{xs:6,md:2,children:(0,r.jsxs)("div",{className:"border rounded p-2 mb-2",children:[(0,r.jsx)("div",{className:"h5 mb-1 text-primary",children:c.total.toLocaleString()}),(0,r.jsx)("small",{className:"text-muted",children:"Total Streams"})]})}),(0,r.jsx)(Fe,{xs:6,md:2,children:(0,r.jsxs)("div",{className:"border rounded p-2 mb-2",children:[(0,r.jsx)("div",{className:"h5 mb-1 text-info",children:c.live.toLocaleString()}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83d\udcfa Canales"})]})}),(0,r.jsx)(Fe,{xs:6,md:2,children:(0,r.jsxs)("div",{className:"border rounded p-2 mb-2",children:[(0,r.jsx)("div",{className:"h5 mb-1 text-warning",children:c.movies.toLocaleString()}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83c\udfac Pel\xedculas"})]})}),(0,r.jsx)(Fe,{xs:6,md:2,children:(0,r.jsxs)("div",{className:"border rounded p-2 mb-2",children:[(0,r.jsx)("div",{className:"h5 mb-1 text-success",children:c.series.toLocaleString()}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83d\udcda Series"})]})}),(0,r.jsx)(Fe,{xs:6,md:2,children:(0,r.jsxs)("div",{className:"border rounded p-2 mb-2",children:[(0,r.jsx)("div",{className:"h5 mb-1 text-secondary",children:c.other.toLocaleString()}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83d\udce6 Otros"})]})}),(0,r.jsx)(Fe,{xs:6,md:2,children:(0,r.jsxs)("div",{className:"border rounded p-2 mb-2",children:[(0,r.jsx)("div",{className:"h5 mb-1 text-dark",children:c.types}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83c\udff7\ufe0f Tipos"})]})})]}),t&&(0,r.jsxs)("div",{className:"d-flex gap-2 mt-3",children:[o.isReading?(0,r.jsx)(w,{variant:"warning",size:"sm",onClick:s,children:"\u23f9\ufe0f Cancelar"}):(0,r.jsx)(w,{variant:"primary",size:"sm",onClick:i,disabled:!a.databaseConnection.isConnected,children:"\ud83d\udd04 Leer Datos"}),(0,r.jsx)(w,{variant:"outline-secondary",size:"sm",onClick:l,disabled:o.isReading,children:"\ud83e\uddf9 Limpiar"})]})]});return n?(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:`bg-${u} text-white`,children:(0,r.jsxs)("div",{className:"d-flex align-items-center",children:[(0,r.jsx)("span",{className:"me-2",children:d}),(0,r.jsx)("h6",{className:"mb-0",children:"\ud83d\udcca Lectura de Base de Datos"}),o.isReading&&(0,r.jsx)("div",{className:"ms-auto",children:(0,r.jsx)("div",{className:"spinner-border spinner-border-sm",role:"status",children:(0,r.jsx)("span",{className:"visually-hidden",children:"Cargando..."})})})]})}),(0,r.jsx)(dn.Body,{children:f})]}):(0,r.jsx)("div",{className:"data-reading-progress",children:f})};function Nr(n,t,r){const a=(0,e.useRef)(void 0!==n),[s,l]=(0,e.useState)(t),i=void 0!==n,o=a.current;return a.current=i,!i&&o&&s!==t&&l(t),[i?n:s,(0,e.useCallback)(function(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];const[a,...s]=n;let i=null==r?void 0:r(a,...s);return l(a),i},[r])]}const Cr={prefix:String(Math.round(1e10*Math.random())),current:0},Tr=e.createContext(Cr),Pr=e.createContext(!1);Boolean("undefined"!==typeof window&&window.document&&window.document.createElement);let _r=new WeakMap;function Lr(){let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=(0,e.useContext)(Tr),r=(0,e.useRef)(null);if(null===r.current&&!n){var a,s;let n=null===(s=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===s||null===(a=s.ReactCurrentOwner)||void 0===a?void 0:a.current;if(n){let e=_r.get(n);null==e?_r.set(n,{id:t.current,state:n.memoizedState}):n.memoizedState!==e.state&&(t.current=e.id,_r.delete(n))}r.current=++t.current}return r.current}const Rr="function"===typeof e.useId?function(n){let t=e.useId(),[r]=(0,e.useState)("function"===typeof e.useSyncExternalStore?e.useSyncExternalStore(Ar,Or,zr):(0,e.useContext)(Pr));return n||`${r?"react-aria":`react-aria${Cr.prefix}`}-${t}`}:function(n){let t=(0,e.useContext)(Tr),r=Lr(!!n),a=`react-aria${t.prefix}`;return n||`${a}-${r}`};function Or(){return!1}function zr(){return!0}function Ar(e){return()=>{}}const Dr=e.createContext(null),Ir=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return null!=e?String(e):n||null},Mr=e.createContext(null),$r=["active","eventKey","mountOnEnter","transition","unmountOnExit","role","onEnter","onEntering","onEntered","onExit","onExiting","onExited"],Fr=["activeKey","getControlledId","getControllerId"],Ur=["as"];function Br(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}function Hr(n){let{active:t,eventKey:r,mountOnEnter:a,transition:s,unmountOnExit:l,role:i="tabpanel",onEnter:o,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:h}=n,m=Br(n,$r);const p=(0,e.useContext)(Dr);if(!p)return[Object.assign({},m,{role:i}),{eventKey:r,isActive:t,mountOnEnter:a,transition:s,unmountOnExit:l,onEnter:o,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:h}];const{activeKey:g,getControlledId:v,getControllerId:b}=p,y=Br(p,Fr),x=Ir(r);return[Object.assign({},m,{role:i,id:v(r),"aria-labelledby":b(r)}),{eventKey:r,isActive:null==t&&null!=x?Ir(g)===x:t,transition:s||y.transition,mountOnEnter:null!=a?a:y.mountOnEnter,unmountOnExit:null!=l?l:y.unmountOnExit,onEnter:o,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:h}]}const Vr=e.forwardRef((e,n)=>{let{as:t="div"}=e,a=Br(e,Ur);const[s,{isActive:l,onEnter:i,onEntering:o,onEntered:c,onExit:u,onExiting:d,onExited:f,mountOnEnter:h,unmountOnExit:m,transition:p=At}]=Hr(a);return(0,r.jsx)(Dr.Provider,{value:null,children:(0,r.jsx)(Mr.Provider,{value:null,children:(0,r.jsx)(p,{in:l,onEnter:i,onEntering:o,onEntered:c,onExit:u,onExiting:d,onExited:f,mountOnEnter:h,unmountOnExit:m,children:(0,r.jsx)(t,Object.assign({},s,{ref:n,hidden:!l,"aria-hidden":!l}))})})})});Vr.displayName="TabPanel";const Wr=n=>{const{id:t,generateChildId:a,onSelect:s,activeKey:l,defaultActiveKey:i,transition:o,mountOnEnter:c,unmountOnExit:u,children:d}=n,[f,h]=Nr(l,i,s),m=Rr(t),p=(0,e.useMemo)(()=>a||((e,n)=>m?`${m}-${n}-${e}`:null),[m,a]),g=(0,e.useMemo)(()=>({onSelect:h,activeKey:f,transition:o,mountOnEnter:c||!1,unmountOnExit:u||!1,getControlledId:e=>p(e,"tabpane"),getControllerId:e=>p(e,"tab")}),[h,f,o,c,u,p]);return(0,r.jsx)(Dr.Provider,{value:g,children:(0,r.jsx)(Mr.Provider,{value:h||null,children:d})})};Wr.Panel=Vr;const qr=Wr;const Kr=e.createContext(null);Kr.displayName="NavContext";const Qr=Kr,Gr=["as","active","eventKey"];function Xr(n){let{key:t,onClick:r,active:a,id:s,role:l,disabled:i}=n;const o=(0,e.useContext)(Mr),c=(0,e.useContext)(Qr),u=(0,e.useContext)(Dr);let d=a;const f={role:l};if(c){l||"tablist"!==c.role||(f.role="tab");const e=c.getControllerId(null!=t?t:null),n=c.getControlledId(null!=t?t:null);f[Ct("event-key")]=t,f.id=e||s,d=null==a&&null!=t?c.activeKey===t:a,!d&&(null!=u&&u.unmountOnExit||null!=u&&u.mountOnEnter)||(f["aria-controls"]=n)}return"tab"===f.role&&(f["aria-selected"]=d,d||(f.tabIndex=-1),i&&(f.tabIndex=-1,f["aria-disabled"]=!0)),f.onClick=M(e=>{i||(null==r||r(e),null!=t&&o&&!e.isPropagationStopped()&&o(t,e))}),[f,{isActive:d}]}const Yr=e.forwardRef((e,n)=>{let{as:t=x,active:a,eventKey:s}=e,l=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,Gr);const[i,o]=Xr(Object.assign({key:Ir(s,l.href),active:a},l));return i[Ct("active")]=o.isActive,(0,r.jsx)(t,Object.assign({},l,i,{ref:n}))});Yr.displayName="NavItem";const Jr=Yr,Zr=["as","onSelect","activeKey","role","onKeyDown"];const ea=()=>{},na=Ct("event-key"),ta=e.forwardRef((n,t)=>{let{as:a="div",onSelect:s,activeKey:l,role:i,onKeyDown:o}=n,c=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(n,Zr);const u=function(){const[,n]=(0,e.useReducer)(e=>e+1,0);return n}(),d=(0,e.useRef)(!1),f=(0,e.useContext)(Mr),h=(0,e.useContext)(Dr);let m,p;h&&(i=i||"tablist",l=h.activeKey,m=h.getControlledId,p=h.getControllerId);const g=(0,e.useRef)(null),v=e=>{const n=g.current;if(!n)return null;const t=Kt(n,`[${na}]:not([aria-disabled=true])`),r=n.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;const a=t.indexOf(r);if(-1===a)return null;let s=a+e;return s>=t.length&&(s=0),s<0&&(s=t.length-1),t[s]},b=(e,n)=>{null!=e&&(null==s||s(e,n),null==f||f(e,n))};(0,e.useEffect)(()=>{if(g.current&&d.current){const e=g.current.querySelector(`[${na}][aria-selected=true]`);null==e||e.focus()}d.current=!1});const y=zt(t,g);return(0,r.jsx)(Mr.Provider,{value:b,children:(0,r.jsx)(Qr.Provider,{value:{role:i,activeKey:Ir(l),getControlledId:m||ea,getControllerId:p||ea},children:(0,r.jsx)(a,Object.assign({},c,{onKeyDown:e=>{if(null==o||o(e),!h)return;let n;switch(e.key){case"ArrowLeft":case"ArrowUp":n=v(-1);break;case"ArrowRight":case"ArrowDown":n=v(1);break;default:return}var t;n&&(e.preventDefault(),b(n.dataset[(t="EventKey",`rrUi${t}`)]||null,e),d.current=!0,u())},ref:y,role:i}))})})});ta.displayName="Nav";const ra=Object.assign(ta,{Item:Jr}),aa=e.createContext(null);aa.displayName="NavbarContext";const sa=aa,la=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"nav-item"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});la.displayName="NavItem";const ia=la,oa=e.forwardRef((e,n)=>{let{bsPrefix:t,className:a,as:s=H,active:i,eventKey:o,disabled:c=!1,...u}=e;t=f(t,"nav-link");const[d,h]=Xr({key:Ir(o,u.href),active:i,disabled:c,...u});return(0,r.jsx)(s,{...u,...d,ref:n,disabled:c,className:l()(a,t,c&&"disabled",h.isActive&&"active")})});oa.displayName="NavLink";const ca=oa,ua=e.forwardRef((n,t)=>{const{as:a="div",bsPrefix:s,variant:i,fill:o=!1,justify:c=!1,navbar:u,navbarScroll:d,className:h,activeKey:m,...p}=C(n,{activeKey:"onSelect"}),g=f(s,"nav");let v,b,y=!1;const x=(0,e.useContext)(sa),j=(0,e.useContext)(qe);return x?(v=x.bsPrefix,y=null==u||u):j&&({cardHeaderBsPrefix:b}=j),(0,r.jsx)(ra,{as:a,ref:t,activeKey:m,className:l()(h,{[g]:!y,[`${v}-nav`]:y,[`${v}-nav-scroll`]:y&&d,[`${b}-${i}`]:!!b,[`${g}-${i}`]:!!i,[`${g}-fill`]:o,[`${g}-justified`]:c}),...p})});ua.displayName="Nav";const da=Object.assign(ua,{Item:ia,Link:ca}),fa=e.forwardRef((e,n)=>{let{className:t,bsPrefix:a,as:s="div",...i}=e;return a=f(a,"tab-content"),(0,r.jsx)(s,{ref:n,className:l()(t,a),...i})});fa.displayName="TabContent";const ha=fa;function ma(e){return"boolean"===typeof e?e?Pe:At:e}const pa=e.forwardRef((e,n)=>{let{bsPrefix:t,transition:a,...s}=e;const[{className:i,as:o="div",...c},{isActive:u,onEnter:d,onEntering:h,onEntered:m,onExit:p,onExiting:g,onExited:v,mountOnEnter:b,unmountOnExit:y,transition:x=Pe}]=Hr({...s,transition:ma(a)}),j=f(t,"tab-pane");return(0,r.jsx)(Dr.Provider,{value:null,children:(0,r.jsx)(Mr.Provider,{value:null,children:(0,r.jsx)(x,{in:u,onEnter:d,onEntering:h,onEntered:m,onExit:p,onExiting:g,onExited:v,mountOnEnter:b,unmountOnExit:y,children:(0,r.jsx)(o,{...c,ref:n,className:l()(i,j,u&&"active")})})})})});pa.displayName="TabPane";const ga=pa;function va(n){let t;return function(n,t){let r=0;e.Children.forEach(n,n=>{e.isValidElement(n)&&t(n,r++)})}(n,e=>{null==t&&(t=e.props.eventKey)}),t}function ba(e){const{title:n,eventKey:t,disabled:a,tabClassName:s,tabAttrs:l,id:i}=e.props;return null==n?null:(0,r.jsx)(ia,{as:"li",role:"presentation",children:(0,r.jsx)(ca,{as:"button",type:"button",eventKey:t,disabled:a,id:i,className:s,...l,children:n})})}const ya=e=>{const{id:n,onSelect:t,transition:a,mountOnEnter:s=!1,unmountOnExit:l=!1,variant:i="tabs",children:o,activeKey:c=va(o),...u}=C(e,{activeKey:"onSelect"});return(0,r.jsxs)(qr,{id:n,activeKey:c,onSelect:t,transition:ma(a),mountOnEnter:s,unmountOnExit:l,children:[(0,r.jsx)(da,{id:n,...u,role:"tablist",as:"ul",variant:i,children:Kn(o,ba)}),(0,r.jsx)(ha,{children:Kn(o,e=>{const n={...e.props};return delete n.title,delete n.disabled,delete n.tabClassName,delete n.tabAttrs,(0,r.jsx)(ga,{...n})})})]})};ya.displayName="Tabs";const xa=ya,ja=e=>{let{transition:n,...t}=e;return(0,r.jsx)(qr,{...t,transition:ma(n)})};ja.displayName="TabContainer";const wa=ja,Sa={eventKey:Le().oneOfType([Le().string,Le().number]),title:Le().node.isRequired,disabled:Le().bool,tabClassName:Le().string,tabAttrs:Le().object},ka=()=>{throw new Error("ReactBootstrap: The `Tab` component is not meant to be rendered! It's an abstract component that is only valid as a direct Child of the `Tabs` Component. For custom tabs components use TabPane and TabsContainer directly")};ka.propTypes=Sa;const Ea=Object.assign(ka,{Container:wa,Content:ha,Pane:ga}),Na=n=>{let{show:t,onHide:a}=n;const{state:s,cancelDataReading:l,resetDataReading:i}=In(),{dataReading:o}=s,[c,u]=(0,e.useState)("progress");(0,e.useEffect)(()=>{o.progress>=100&&o.result&&u("results")},[o.progress,o.result]);return(0,r.jsxs)(vr,{show:t,onHide:a,size:"lg",centered:!0,children:[(0,r.jsx)(vr.Header,{closeButton:!0,children:(0,r.jsxs)(vr.Title,{children:["\ud83d\udcca Progreso de Lectura de Base de Datos",o.isReading&&(0,r.jsx)("div",{className:"spinner-border spinner-border-sm ms-2",role:"status",children:(0,r.jsx)("span",{className:"visually-hidden",children:"Cargando..."})})]})}),(0,r.jsx)(vr.Body,{children:(0,r.jsxs)(xa,{activeKey:c,onSelect:u,className:"mb-3",children:[(0,r.jsx)(Ea,{eventKey:"progress",title:"\ud83d\udd04 Progreso",children:(()=>{var e,n;return(0,r.jsxs)("div",{children:[(0,r.jsxs)(De,{variant:"info",className:"d-flex align-items-center",children:[(0,r.jsx)("div",{className:"me-3",style:{fontSize:"2rem"},children:o.isReading?"\u23f3":o.progress>=100?"\u2705":"\u23f8\ufe0f"}),(0,r.jsxs)("div",{className:"flex-grow-1",children:[(0,r.jsx)("h6",{className:"mb-1",children:o.currentMessage||"Esperando..."}),(0,r.jsxs)("small",{className:"text-muted",children:["Paso actual: ",(0,r.jsx)("code",{children:o.currentStep||"none"})]})]}),(0,r.jsxs)(hn,{bg:"primary",style:{fontSize:"1rem"},children:[Math.round(o.progress),"%"]})]}),(0,r.jsx)(xt,{now:o.progress,variant:o.error?"danger":"primary",className:"mb-4",style:{height:"12px"},animated:o.isReading}),(0,r.jsx)("h6",{children:"\ud83d\udd04 Pasos del Proceso:"}),(0,r.jsxs)(pn,{striped:!0,size:"sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Paso"}),(0,r.jsx)("th",{children:"Descripci\xf3n"}),(0,r.jsx)("th",{children:"Estado"})]})}),(0,r.jsxs)("tbody",{children:[(0,r.jsxs)("tr",{className:"stream_types"===o.currentStep?"table-primary":"",children:[(0,r.jsx)("td",{children:"\ud83c\udfef Paso 1"}),(0,r.jsx)("td",{children:"Leer tipos de stream (streams_types)"}),(0,r.jsx)("td",{children:"stream_types"===o.currentStep?(0,r.jsx)(hn,{bg:"primary",children:"En progreso"}):o.progress>20?(0,r.jsx)(hn,{bg:"success",children:"\u2705 Completado"}):(0,r.jsx)(hn,{bg:"secondary",children:"\u23f3 Pendiente"})})]}),(0,r.jsxs)("tr",{className:null!==(e=o.currentStep)&&void 0!==e&&e.includes("streams")?"table-primary":"",children:[(0,r.jsx)("td",{children:"\ud83d\udfe2 Paso 2"}),(0,r.jsx)("td",{children:"Leer todos los streams con paginaci\xf3n"}),(0,r.jsx)("td",{children:null!==(n=o.currentStep)&&void 0!==n&&n.includes("streams")?(0,r.jsx)(hn,{bg:"primary",children:"En progreso"}):o.progress>75?(0,r.jsx)(hn,{bg:"success",children:"\u2705 Completado"}):(0,r.jsx)(hn,{bg:"secondary",children:"\u23f3 Pendiente"})})]}),(0,r.jsxs)("tr",{className:"series_structure"===o.currentStep?"table-primary":"",children:[(0,r.jsx)("td",{children:"\ud83d\udd34 Paso 4"}),(0,r.jsx)("td",{children:"Analizar estructura de series"}),(0,r.jsx)("td",{children:"series_structure"===o.currentStep?(0,r.jsx)(hn,{bg:"primary",children:"En progreso"}):o.progress>=100?(0,r.jsx)(hn,{bg:"success",children:"\u2705 Completado"}):(0,r.jsx)(hn,{bg:"secondary",children:"\u23f3 Pendiente"})})]})]})]}),o.error&&(0,r.jsxs)(De,{variant:"danger",children:[(0,r.jsx)("h6",{children:"\u274c Error durante la lectura:"}),(0,r.jsx)("code",{children:o.error})]})]})})()}),(0,r.jsx)(Ea,{eventKey:"results",title:"\ud83d\udcca Resultados",children:(()=>{var e,n,t,a,s,l,i,c,u;if(!o.result)return(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("h6",{children:"\u2139\ufe0f Sin resultados"}),(0,r.jsx)("p",{children:"Los resultados aparecer\xe1n aqu\xed cuando se complete la lectura."})]});const{contentAnalysis:d,streamTypes:f,seriesStructure:h,totalStreams:m,readAt:p}=o.result;return(0,r.jsxs)("div",{children:[(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{md:3,children:(0,r.jsxs)("div",{className:"text-center border rounded p-3",children:[(0,r.jsx)("h3",{className:"text-primary",children:(null===m||void 0===m?void 0:m.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"Total Streams"})]})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsxs)("div",{className:"text-center border rounded p-3",children:[(0,r.jsx)("h3",{className:"text-info",children:(null===d||void 0===d||null===(e=d.live)||void 0===e||null===(n=e.count)||void 0===n?void 0:n.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83d\udcfa Canales Live"})]})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsxs)("div",{className:"text-center border rounded p-3",children:[(0,r.jsx)("h3",{className:"text-warning",children:(null===d||void 0===d||null===(t=d.movies)||void 0===t||null===(a=t.count)||void 0===a?void 0:a.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83c\udfac Pel\xedculas"})]})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsxs)("div",{className:"text-center border rounded p-3",children:[(0,r.jsx)("h3",{className:"text-success",children:(null===d||void 0===d||null===(s=d.series)||void 0===s||null===(l=s.count)||void 0===l?void 0:l.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"\ud83d\udcda Series"})]})})]}),(0,r.jsx)("h6",{children:"\ud83c\udff7\ufe0f Tipos de Stream:"}),(0,r.jsxs)(pn,{striped:!0,size:"sm",className:"mb-4",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"ID"}),(0,r.jsx)("th",{children:"Nombre"}),(0,r.jsx)("th",{children:"Cantidad"})]})}),(0,r.jsx)("tbody",{children:Object.entries(f||{}).map(e=>{var n;let[t,a]=e;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)(hn,{bg:"secondary",children:t})}),(0,r.jsx)("td",{children:a.name}),(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:(null===(n=a.count)||void 0===n?void 0:n.toLocaleString())||0})})]},t)})})]}),h&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h6",{children:"\ud83d\udcda Estructura de Series:"}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{md:4,children:(0,r.jsxs)("div",{className:"text-center border rounded p-2",children:[(0,r.jsx)("h5",{className:"text-success",children:(null===(i=h.totalSeries)||void 0===i?void 0:i.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"Series Totales"})]})}),(0,r.jsx)(Fe,{md:4,children:(0,r.jsxs)("div",{className:"text-center border rounded p-2",children:[(0,r.jsx)("h5",{className:"text-warning",children:(null===(c=h.totalEpisodes)||void 0===c?void 0:c.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"Episodios Totales"})]})}),(0,r.jsx)(Fe,{md:4,children:(0,r.jsxs)("div",{className:"text-center border rounded p-2",children:[(0,r.jsx)("h5",{className:"text-info",children:(null===(u=h.totalSeasons)||void 0===u?void 0:u.toLocaleString())||0}),(0,r.jsx)("small",{className:"text-muted",children:"Temporadas Totales"})]})})]})]}),(0,r.jsx)(De,{variant:"success",children:(0,r.jsxs)("small",{children:[(0,r.jsx)("strong",{children:"\u2705 Lectura completada:"})," ",new Date(p).toLocaleString()]})})]})})()})]})}),(0,r.jsx)(vr.Footer,{children:(0,r.jsxs)("div",{className:"d-flex w-100 justify-content-between align-items-center",children:[(0,r.jsx)("div",{children:o.isReading&&(0,r.jsx)(w,{variant:"warning",onClick:l,children:"\u23f9\ufe0f Cancelar Lectura"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{variant:"outline-secondary",onClick:i,className:"me-2",children:"\ud83e\uddf9 Limpiar"}),(0,r.jsx)(w,{variant:"secondary",onClick:a,children:"Cerrar"})]})]})})]})},Ca=()=>{const[n,t]=(0,e.useState)(!1),[a,s]=(0,e.useState)(null),[l,i]=(0,e.useState)(null),[o,c]=(0,e.useState)(!1),[u,d]=(0,e.useState)(null),f=async()=>{try{t(!0),d(null);const e=await jn.databaseAPI.optimizeIndexes();e.success?s(e.data):d(e.message||"Error optimizando \xedndices")}catch(u){d(`Error optimizando \xedndices: ${u.message}`)}finally{t(!1)}},h=async()=>{try{c(!0),d(null);const e=await jn.databaseAPI.getQueryPerformance();e.success?i(e.data):d(e.message||"Error cargando datos de rendimiento")}catch(u){d(`Error cargando rendimiento: ${u.message}`)}finally{c(!1)}},m=e=>{if(0===e)return"0 Bytes";const n=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,n)).toFixed(2))+" "+["Bytes","KB","MB","GB"][n]};return(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-warning text-dark",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\ude80 Optimizaci\xf3n de Base de Datos"})}),(0,r.jsxs)(dn.Body,{children:[u&&(0,r.jsxs)(De,{variant:"danger",dismissible:!0,onClose:()=>d(null),children:[(0,r.jsx)("strong",{children:"\u274c Error:"})," ",u]}),(0,r.jsxs)(xa,{defaultActiveKey:"optimization",className:"mb-3",children:[(0,r.jsx)(Ea,{eventKey:"optimization",title:"\ud83d\ude80 Optimizaci\xf3n",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("h6",{children:"\ud83d\ude80 Optimizaci\xf3n de \xcdndices"}),(0,r.jsx)("p",{children:"Esta herramienta crea \xedndices optimizados en las tablas principales para mejorar el rendimiento de las consultas de lectura de datos."})]}),(0,r.jsx)("div",{className:"d-flex gap-2 mb-3",children:(0,r.jsx)(w,{variant:"primary",onClick:f,disabled:n,children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{animation:"border",size:"sm",className:"me-2"}),"Optimizando..."]}):"\ud83d\ude80 Optimizar \xcdndices"})}),a&&(0,r.jsxs)(De,{variant:"success",children:[(0,r.jsx)("h6",{children:"\u2705 Optimizaci\xf3n Completada"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:a.created_successfully})," de"," ",(0,r.jsx)("strong",{children:a.total_indexes})," \xedndices creados exitosamente."]}),a.details&&(0,r.jsxs)(pn,{size:"sm",className:"mt-3",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"\xcdndice"}),(0,r.jsx)("th",{children:"Estado"})]})}),(0,r.jsx)("tbody",{children:a.details.map((e,n)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("code",{children:e.query})}),(0,r.jsx)("td",{children:e.success?(0,r.jsx)(hn,{bg:"success",children:"\u2705 Creado"}):(0,r.jsx)(hn,{bg:"danger",children:"\u274c Error"})})]},`detail-${n}-${e.query.substring(0,20)}`))})]})]})]})}),(0,r.jsx)(Ea,{eventKey:"performance",title:"\ud83d\udcca Rendimiento",children:(()=>{var e,n;return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"d-flex gap-2 mb-3",children:(0,r.jsx)(w,{variant:"info",onClick:h,disabled:o,children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{animation:"border",size:"sm",className:"me-2"}),"Cargando..."]}):"\ud83d\udcca Analizar Rendimiento"})}),l&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h6",{children:"\ud83d\udcca Estad\xedsticas de Tablas"}),(0,r.jsxs)(pn,{striped:!0,size:"sm",className:"mb-4",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Tabla"}),(0,r.jsx)("th",{children:"Filas"}),(0,r.jsx)("th",{children:"Tama\xf1o Datos"}),(0,r.jsx)("th",{children:"Tama\xf1o \xcdndices"}),(0,r.jsx)("th",{children:"Tama\xf1o Total"})]})}),(0,r.jsx)("tbody",{children:null===(e=l.table_statistics)||void 0===e?void 0:e.map((e,n)=>{var t;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:e.TABLE_NAME})}),(0,r.jsx)("td",{children:(null===(t=e.TABLE_ROWS)||void 0===t?void 0:t.toLocaleString())||0}),(0,r.jsx)("td",{children:m(e.DATA_LENGTH||0)}),(0,r.jsx)("td",{children:m(e.INDEX_LENGTH||0)}),(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:m(e.TOTAL_SIZE||0)})})]},`table-${e.TABLE_NAME}-${n}`)})})]}),(0,r.jsx)("h6",{children:"\ud83c\udff7\ufe0f \xcdndices Existentes"}),(0,r.jsxs)(pn,{striped:!0,size:"sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Tabla"}),(0,r.jsx)("th",{children:"\xcdndice"}),(0,r.jsx)("th",{children:"Columna"}),(0,r.jsx)("th",{children:"Cardinalidad"}),(0,r.jsx)("th",{children:"Tipo"})]})}),(0,r.jsx)("tbody",{children:null===(n=l.indexes)||void 0===n?void 0:n.map((e,n)=>{var t;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:e.TABLE_NAME}),(0,r.jsx)("td",{children:(0,r.jsx)(hn,{bg:"PRIMARY"===e.INDEX_NAME?"primary":"secondary",children:e.INDEX_NAME})}),(0,r.jsx)("td",{children:(0,r.jsx)("code",{children:e.COLUMN_NAME})}),(0,r.jsx)("td",{children:(null===(t=e.CARDINALITY)||void 0===t?void 0:t.toLocaleString())||0}),(0,r.jsx)("td",{children:e.INDEX_TYPE})]},`index-${e.TABLE_NAME}-${e.INDEX_NAME}-${e.COLUMN_NAME}-${n}`)})})]})]})]})})()})]})]})]})},Ta=async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3;console.log("\ud83e\uddea Probando conectividad con URLs:",e);const t=e.map(e=>async function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3;try{const t=new AbortController,r=setTimeout(()=>t.abort(),n),a=await fetch(`${e}/health`,{method:"GET",signal:t.signal,headers:{"Content-Type":"application/json"}});return clearTimeout(r),{url:e,success:a.ok,status:a.status,statusText:a.statusText,responseTime:Date.now()}}catch(t){return{url:e,success:!1,error:t.message,errorType:t.name}}}(e,n));return(await Promise.allSettled(t)).map((n,t)=>{var r;return{url:e[t],..."fulfilled"===n.status?n.value:{success:!1,error:null===(r=n.reason)||void 0===r?void 0:r.message}}})},Pa=async()=>{const e=vn(),n=(()=>{const e=window.location,n=e.protocol,t=e.hostname,r=e.port,a=[];return r&&"80"!==r&&"443"!==r?a.push(`${n}//${t}:${r}/api`):a.push(`${n}//${t}/api`),"localhost"!==t&&"127.0.0.1"!==t||(a.push("http://localhost:3000/api"),a.push("http://localhost:5000/api"),a.push("http://127.0.0.1:3000/api"),a.push("http://127.0.0.1:5000/api")),[...new Set(a)]})(),t=await Ta(n),r=t.filter(e=>e.success),a=t.filter(e=>!e.success);return{configInfo:e,possibleUrls:n,testResults:t,workingUrls:r,failedUrls:a,hasWorkingConnection:r.length>0,recommendedUrl:r.length>0?r[0].url:null,timestamp:(new Date).toISOString()}},_a=e=>{const n=[];return e.hasWorkingConnection||n.push({type:"error",title:"Sin conectividad",message:"No se pudo conectar a ninguna URL del backend",actions:["Verificar que el servidor backend est\xe9 ejecut\xe1ndose","Verificar configuraci\xf3n de firewall","Probar con http://127.0.0.1 en lugar de localhost"]}),e.workingUrls.length>1&&n.push({type:"info",title:"M\xfaltiples URLs funcionando",message:"Se encontraron varias URLs que funcionan",actions:[`Usar URL recomendada: ${e.recommendedUrl}`,"Configurar REACT_APP_API_URL para consistencia"]}),"development"!==e.configInfo.environment||e.workingUrls.some(e=>e.url.includes(":5000"))||n.push({type:"warning",title:"Backend de desarrollo no encontrado",message:"En desarrollo, el backend deber\xeda estar en puerto 5000",actions:["Ejecutar: cd backend && npm start","Verificar que el puerto 5000 est\xe9 libre"]}),n},La=async()=>{console.log("\ud83e\uddea === REPORTE DE CONECTIVIDAD ===");const e=await Pa();console.log("\ud83d\udcca Configuraci\xf3n:",e.configInfo),console.log("\ud83d\udd0d URLs detectadas:",e.possibleUrls),console.log("\u2705 URLs funcionando:",e.workingUrls.map(e=>e.url)),console.log("\u274c URLs fallidas:",e.failedUrls.map(e=>`${e.url} (${e.error})`)),e.recommendedUrl&&console.log("\ud83d\udca1 URL recomendada:",e.recommendedUrl);const n=_a(e);return n.length>0&&(console.log("\ud83d\udca1 Sugerencias:"),n.forEach(e=>{console.log(`  ${e.type.toUpperCase()}: ${e.title}`),console.log(`    ${e.message}`),e.actions.forEach(e=>{console.log(`    - ${e}`)})})),console.log("\ud83e\uddea === FIN REPORTE ==="),e};"undefined"!==typeof window&&(window.testConnectivity=La);const Ra=()=>{const[n,t]=(0,e.useState)(null),[a,s]=(0,e.useState)(null),[l,i]=(0,e.useState)(!1),[o,c]=(0,e.useState)(null);(0,e.useEffect)(()=>{u()},[]);const u=()=>{try{const e=vn();t(e)}catch(e){console.error("Error cargando configuraci\xf3n:",e)}};return(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-info text-white",children:(0,r.jsx)("h6",{className:"mb-0",children:"\ud83d\udd27 Diagn\xf3stico de Conectividad"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsx)("h6",{children:"\ud83d\udcca Configuraci\xf3n Actual"}),n?(0,r.jsx)(pn,{size:"sm",striped:!0,children:(0,r.jsxs)("tbody",{children:[(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"Entorno"})}),(0,r.jsx)("td",{children:(0,r.jsx)(hn,{bg:"development"===n.environment?"warning":"success",children:n.environment})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"URL del API"})}),(0,r.jsx)("td",{children:(0,r.jsx)("code",{children:n.apiBaseUrl})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"Protocolo"})}),(0,r.jsx)("td",{children:n.currentLocation.protocol})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"Host"})}),(0,r.jsx)("td",{children:n.currentLocation.hostname})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"Puerto"})}),(0,r.jsx)("td",{children:n.currentLocation.port||"Por defecto"})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"Variable API_URL"})}),(0,r.jsx)("td",{children:n.environmentVariables.REACT_APP_API_URL?(0,r.jsx)("code",{children:n.environmentVariables.REACT_APP_API_URL}):(0,r.jsx)(hn,{bg:"secondary",children:"No definida"})})]})]})}):null,(0,r.jsxs)("div",{className:"d-flex gap-2 mb-3",children:[(0,r.jsx)(w,{variant:"primary",size:"sm",onClick:async()=>{i(!0),s(null);try{const e=await Pa(),n=_a(e);s({success:e.hasWorkingConnection,report:e,suggestions:n,message:e.hasWorkingConnection?`\u2705 Conectividad exitosa con: ${e.recommendedUrl}`:"\u274c No se pudo conectar a ninguna URL del backend"})}catch(e){s({success:!1,error:e.message,message:`\u274c Error probando conectividad: ${e.message}`})}finally{i(!1)}},disabled:l,children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{animation:"border",size:"sm",className:"me-2"}),"Probando..."]}):"\ud83e\uddea Probar Conectividad"}),(0,r.jsx)(w,{variant:"success",size:"sm",onClick:async()=>{try{const e=await jn.utilsAPI.healthCheck();c({success:!0,data:e,message:"\u2705 Backend respondiendo correctamente"})}catch(e){c({success:!1,error:e.message,message:`\u274c Error en health check: ${e.message}`})}},children:"\ud83c\udfe5 Health Check"})]}),a&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(De,{variant:a.success?"success":"danger",children:[(0,r.jsx)("h6",{children:"\ud83e\uddea Resultado de Conectividad"}),(0,r.jsx)("p",{children:a.message}),a.report&&(0,r.jsxs)(r.Fragment,{children:[a.report.workingUrls.length>0&&(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("strong",{children:"\u2705 URLs funcionando:"}),(0,r.jsx)("ul",{className:"mb-0 mt-1",children:a.report.workingUrls.map((e,n)=>(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:e.url}),(0,r.jsx)(hn,{bg:"success",className:"ms-2",children:e.status})]},`working-${e.url}-${n}`))})]}),a.report.failedUrls.length>0&&(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("strong",{children:"\u274c URLs fallidas:"}),(0,r.jsx)("ul",{className:"mb-0 mt-1",children:a.report.failedUrls.map((e,n)=>(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:e.url}),(0,r.jsxs)("small",{className:"text-muted ms-2",children:["(",e.error||e.errorType,")"]})]},`failed-${e.url}-${n}`))})]})]})]}),a.suggestions&&a.suggestions.length>0&&(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("h6",{children:"\ud83d\udca1 Sugerencias de Soluci\xf3n"}),a.suggestions.map((e,n)=>(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("strong",{children:[e.title,":"]}),(0,r.jsx)("p",{className:"mb-1",children:e.message}),(0,r.jsx)("ul",{className:"mb-0",children:e.actions.map((e,n)=>(0,r.jsx)("li",{children:e},`action-${n}-${e.substring(0,10)}`))})]},`suggestion-${e.title}-${n}`))]})]}),o&&(0,r.jsxs)(De,{variant:o.success?"success":"danger",children:[(0,r.jsx)("h6",{children:"\ud83c\udfe5 Estado del Backend"}),(0,r.jsx)("p",{children:o.message}),o.data&&(0,r.jsxs)("small",{children:["Respuesta: ",(0,r.jsx)("code",{children:JSON.stringify(o.data,null,2)})]})]}),(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("h6",{children:"\ud83d\udca1 Consejos de Soluci\xf3n"}),(0,r.jsxs)("ul",{className:"mb-0",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Desarrollo:"})," Aseg\xfarese de que el backend est\xe9 ejecut\xe1ndose en puerto 5000"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Ejecutable:"})," El frontend y backend deben estar en el mismo puerto"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Variable de entorno:"})," Use ",(0,r.jsx)("code",{children:"REACT_APP_API_URL"})," para URL personalizada"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Firewall:"})," Verifique que no haya bloqueos de puerto"]})]})]})]})]})},Oa=()=>{var n,t,a;const{state:s,connectToDatabase:l,disconnectFromDatabase:i,setError:o}=In(),[c,u]=(0,e.useState)(!1),[d,f]=(0,e.useState)(!1),[h,m]=(0,e.useState)(!1),[p,v]=(0,e.useState)(null),[b,y]=(0,e.useState)([]),[x,j]=(0,e.useState)({name:"",host:"",port:"3306",database:"xui",username:"",password:""});(0,e.useEffect)(()=>{S()},[]),(0,e.useEffect)(()=>{E()},[s.databaseConnection]),(0,e.useEffect)(()=>{var e;null!==(e=s.dataReading)&&void 0!==e&&e.isReading&&!d&&f(!0)},[null===(n=s.dataReading)||void 0===n?void 0:n.isReading,d]);const S=()=>{try{const e=localStorage.getItem("xuiConnections");if(e){const n=JSON.parse(e);y(n)}else y([{id:1,name:"Local XUI Server",host:"localhost",port:"3306",database:"xtream_codes",username:"root",status:"disconnected",lastTest:null,version:null,isActive:!1}])}catch(e){console.error("Error cargando conexiones:",e),y([])}},k=e=>{localStorage.setItem("xuiConnections",JSON.stringify(e))},E=()=>{s.databaseConnection.isConnected?y(e=>e.map(e=>{var n;return{...e,status:e.host===s.databaseConnection.host&&e.database===s.databaseConnection.database?"connected":"disconnected",isActive:e.host===s.databaseConnection.host&&e.database===s.databaseConnection.database,lastTest:e.host===s.databaseConnection.host&&e.database===s.databaseConnection.database?(new Date).toLocaleString():e.lastTest,version:e.host===s.databaseConnection.host&&e.database===s.databaseConnection.database?null===(n=s.databaseConnection.serverInfo)||void 0===n?void 0:n.version:e.version}})):y(e=>e.map(e=>({...e,status:"disconnected",isActive:!1})))},N=(e,n)=>{j(t=>({...t,[e]:n}))},C=async()=>{try{await i(),E()}catch(e){o(`Error desconectando: ${e.message}`)}},T=()=>{u(!1),j({name:"",host:"",port:"3306",database:"xtream_codes",username:"",password:""}),v(null)};return(0,r.jsxs)("div",{style:{width:"100%",maxWidth:"none"},children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{className:"text-primary",children:"\ud83d\udd17 XUI Server Connections"}),(0,r.jsxs)("div",{children:[s.databaseConnection.isConnected&&(0,r.jsxs)(hn,{bg:"success",className:"me-2",children:["\u2705 Connected to ",s.databaseConnection.host]}),(0,r.jsx)(w,{variant:"danger",onClick:()=>{console.log("\ud83e\uddf9 Limpiando todas las conexiones guardadas..."),localStorage.removeItem("xuiConnections"),y([]),console.log("\u2705 Conexiones limpiadas")},className:"me-2",size:"sm",children:"\ud83e\uddf9 Clear All"}),(0,r.jsx)(w,{variant:"success",onClick:()=>u(!0),children:"\u2795 Add New Connection"})]})]}),s.error&&(0,r.jsx)(De,{variant:"danger",dismissible:!0,onClose:()=>o(null),children:s.error}),((null===(t=s.dataReading)||void 0===t?void 0:t.isReading)||(null===(a=s.dataReading)||void 0===a?void 0:a.result))&&(0,r.jsx)(Me,{className:"mb-4",children:(0,r.jsx)(Fe,{children:(0,r.jsx)(Er,{showCard:!0,showControls:!0})})}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{lg:8,children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsxs)(dn.Header,{className:"bg-primary text-white d-flex justify-content-between align-items-center",children:[(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udda5\ufe0f Active XUI Servers"}),s.isLoading&&(0,r.jsx)(g,{animation:"border",size:"sm",variant:"light"})]}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(pn,{striped:!0,hover:!0,children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"\ud83d\udcdb Server Name"}),(0,r.jsx)("th",{children:"\ud83c\udf10 Host:Port"}),(0,r.jsx)("th",{children:"\ud83d\uddc4\ufe0f Database"}),(0,r.jsx)("th",{children:"\ud83d\udcca Status"}),(0,r.jsx)("th",{children:"\ud83d\udd27 Version"}),(0,r.jsx)("th",{children:"\u26a1 Actions"})]})}),(0,r.jsxs)("tbody",{children:[b.map(e=>(0,r.jsxs)("tr",{className:e.isActive?"table-success":"",children:[(0,r.jsxs)("td",{children:[(0,r.jsx)("strong",{children:e.name}),e.isActive&&(0,r.jsx)(hn,{bg:"primary",className:"ms-2",children:"Active"})]}),(0,r.jsxs)("td",{children:[e.host,":",e.port]}),(0,r.jsx)("td",{children:e.database}),(0,r.jsx)("td",{children:(0,r.jsx)(hn,{bg:"connected"===e.status?"success":"danger",children:"connected"===e.status?"\u2705 Connected":"\u274c Disconnected"})}),(0,r.jsx)("td",{children:e.version||"Unknown"}),(0,r.jsxs)("td",{children:["connected"===e.status?(0,r.jsx)(w,{size:"sm",variant:"outline-danger",className:"me-1",onClick:C,disabled:h,children:"\ud83d\udd0c Disconnect"}):(0,r.jsxs)(w,{size:"sm",variant:"outline-success",className:"me-1",onClick:()=>(async e=>{const n=prompt(`Ingrese la contrase\xf1a para ${e.name} (${e.username}@${e.host}):`);if(n){m(!0);try{const t={host:e.host,port:e.port,database:e.database,username:e.username,password:n},r=await l(t);r.success?o(null):o(`Error conectando a ${e.name}: ${r.error}`)}catch(t){o(`Error: ${t.message}`)}finally{m(!1)}}else o("Se requiere contrase\xf1a para conectar")})(e),disabled:h,children:[h?"\u23f3":"\ud83d\udd0c"," Connect"]}),(0,r.jsx)(w,{size:"sm",variant:"outline-danger",onClick:()=>(e=>{const n=b.filter(n=>n.id!==e);y(n),k(n)})(e.id),children:"\ud83d\uddd1\ufe0f"})]})]},e.id)),0===b.length&&(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:"6",className:"text-center text-muted",children:"No connections configured. Add a new connection to get started."})})]})]})})]})}),(0,r.jsx)(Fe,{lg:4,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-info text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udca1 XUI Database Info"})}),(0,r.jsxs)(dn.Body,{children:[s.databaseConnection.isConnected&&s.databaseConnection.serverInfo&&(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("h6",{children:"\ud83d\udcca Current Connection:"}),(0,r.jsxs)("ul",{className:"list-unstyled",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Host:"})," ",s.databaseConnection.host]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Database:"})," ",s.databaseConnection.database]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"User:"})," ",s.databaseConnection.username]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Connected:"})," ",new Date(s.databaseConnection.lastConnected).toLocaleString()]})]}),(0,r.jsx)("hr",{})]}),(0,r.jsx)("h6",{children:"\ud83c\udfaf Target Tables:"}),(0,r.jsxs)("ul",{className:"list-unstyled",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:"\ud83d\udcfa streams"})," - Live channels"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:"\ud83c\udfac stream_categories"})," - Categories"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:"\ud83d\udcda series"})," - TV Series"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:"\ud83c\udf9e\ufe0f series_episodes"})," - Episodes"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:"\ud83c\udfaa bouquets"})," - Channel groups"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("code",{children:"\ud83d\udc65 users"})," - User accounts"]})]}),(0,r.jsx)("h6",{children:"\u2699\ufe0f Required Permissions:"}),(0,r.jsxs)("ul",{className:"list-unstyled",children:[(0,r.jsxs)("li",{children:["\u2705 ",(0,r.jsx)("code",{children:"SELECT"})," - Read existing data"]}),(0,r.jsxs)("li",{children:["\u2705 ",(0,r.jsx)("code",{children:"INSERT"})," - Add new content"]}),(0,r.jsxs)("li",{children:["\u2705 ",(0,r.jsx)("code",{children:"UPDATE"})," - Modify metadata"]}),(0,r.jsxs)("li",{children:["\u2705 ",(0,r.jsx)("code",{children:"DELETE"})," - Remove duplicates"]})]}),(0,r.jsx)(De,{variant:"warning",className:"mt-3",children:(0,r.jsxs)("small",{children:[(0,r.jsx)("strong",{children:"\u26a0\ufe0f Important:"})," Always backup your XUI database before importing large M3U files."]})})]})]})})]}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{lg:6,children:(0,r.jsx)(Ra,{})}),(0,r.jsx)(Fe,{lg:6,children:s.databaseConnection.isConnected&&(0,r.jsx)(Ca,{})})]}),(0,r.jsxs)(vr,{show:c,onHide:T,size:"lg",children:[(0,r.jsx)(vr.Header,{closeButton:!0,children:(0,r.jsx)(vr.Title,{children:"\ud83d\udd17 Add New XUI Server Connection"})}),(0,r.jsxs)(vr.Body,{children:[p&&(0,r.jsx)(De,{variant:p.type,dismissible:!0,onClose:()=>v(null),children:p.message}),(0,r.jsxs)(gt,{children:[(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{md:6,children:(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udcdb Connection Name"}),(0,r.jsx)(gt.Control,{type:"text",placeholder:"e.g., Main Production Server",value:x.name,onChange:e=>N("name",e.target.value)})]})}),(0,r.jsx)(Fe,{md:6,children:(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\uddc4\ufe0f Database Name"}),(0,r.jsx)(gt.Control,{type:"text",value:x.database,onChange:e=>N("database",e.target.value)})]})})]}),(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{md:8,children:(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83c\udf10 Host/IP Address"}),(0,r.jsx)(gt.Control,{type:"text",placeholder:"************* or xuiserver.com",value:x.host,onChange:e=>N("host",e.target.value)})]})}),(0,r.jsx)(Fe,{md:4,children:(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udd0c Port"}),(0,r.jsx)(gt.Control,{type:"text",value:x.port,onChange:e=>N("port",e.target.value)})]})})]}),(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{md:6,children:(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udc64 Username"}),(0,r.jsx)(gt.Control,{type:"text",placeholder:"Database username",value:x.username,onChange:e=>N("username",e.target.value)})]})}),(0,r.jsx)(Fe,{md:6,children:(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udd10 Password"}),(0,r.jsx)(gt.Control,{type:"password",placeholder:"Database password",value:x.password,onChange:e=>N("password",e.target.value)})]})})]}),(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("strong",{children:"\ud83d\udca1 Tip:"})," Make sure your XUI server allows remote MySQL connections and the user has the necessary permissions."]})]})]}),(0,r.jsxs)(vr.Footer,{children:[(0,r.jsx)(w,{variant:"secondary",onClick:T,children:"Cancel"}),(0,r.jsx)(w,{variant:"warning",onClick:async()=>{m(!0),v(null);try{window.debugLog&&window.debugLog(`\ud83d\udd0d Testing connection to ${x.host}`,"info");const n=await l(x);if(n.success){var e;v({type:"success",message:"\u2705 Conexi\xf3n exitosa! Base de datos conectada."});const n={id:`conn_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,name:x.name||`${x.host}:${x.port}`,host:x.host,port:x.port,database:x.database,username:x.username,status:"connected",lastTest:(new Date).toLocaleString(),version:(null===(e=s.databaseConnection.serverInfo)||void 0===e?void 0:e.version)||"Unknown",isActive:!0},t=[...b.filter(e=>!(e.host===x.host&&e.database===x.database)),n];y(t),k(t),j({name:"",host:"",port:"3306",database:"xtream_codes",username:"",password:""}),setTimeout(()=>{u(!1)},2e3)}else v({type:"danger",message:`\u274c Error de conexi\xf3n: ${n.error}`})}catch(n){console.error("Error testing connection:",n),v({type:"danger",message:`\u274c Error: ${n.message}`})}finally{m(!1)}},disabled:h||!x.host||!x.username,children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{animation:"border",size:"sm",className:"me-2"}),"Testing..."]}):"\ud83e\uddea Test Connection"})]})]}),(0,r.jsx)(Na,{show:d,onHide:()=>f(!1)})]})},za=()=>{const[n,t]=(0,e.useState)(!1),[a,s]=(0,e.useState)(null),[l,i]=(0,e.useState)("all"),[o,c]=(0,e.useState)("all"),u=[{id:1,fileName:"premium_channels.m3u",date:"2025-07-15 14:30:25",status:"success",type:"channels",server:"Main XUI Server",duration:"2m 15s",itemsTotal:1250,itemsSuccess:1245,itemsFailed:5,categories:25,tmdbMatches:1180,logs:["\u2705 Connected to XUI database (xtream_codes)","\ud83d\udcca Found 1250 entries in M3U file","\ud83c\udff7\ufe0f Created 3 new categories","\ud83c\udfaf TMDB matched 1180/1250 items (94.4%)","\ud83d\udcfa Inserted 1245 channels into streams table","\u274c Failed to process 5 items (invalid URLs)","\u2705 Import completed successfully"]},{id:2,fileName:"netflix_series.m3u",date:"2025-07-15 13:45:12",status:"processing",type:"series",server:"Cloud Server",duration:"5m 32s",itemsTotal:850,itemsSuccess:720,itemsFailed:0,categories:12,tmdbMatches:805,logs:["\u2705 Connected to XUI database (xtream_codes)","\ud83d\udcca Found 850 series in M3U file","\ud83c\udfac Processing episodes metadata...","\ud83c\udfaf TMDB matched 805/850 series (94.7%)","\u23f3 Inserting series data...","\u23f3 Processing in progress..."]},{id:3,fileName:"vod_movies.m3u",date:"2025-07-15 12:20:45",status:"success",type:"vod",server:"Main XUI Server",duration:"3m 45s",itemsTotal:3450,itemsSuccess:3420,itemsFailed:30,categories:18,tmdbMatches:3200,logs:["\u2705 Connected to XUI database (xtream_codes)","\ud83d\udcca Found 3450 VOD items in M3U file","\ud83c\udff7\ufe0f Created 5 new movie categories","\ud83c\udfaf TMDB matched 3200/3450 items (92.8%)","\ud83c\udfac Downloaded 3200 movie posters","\ud83d\udcdd Updated movie descriptions and ratings","\ud83d\udcfa Inserted 3420 movies into streams table","\u274c Failed to process 30 items (duplicate entries)","\u2705 Import completed successfully"]},{id:4,fileName:"sports_live.m3u",date:"2025-07-15 11:10:30",status:"failed",type:"channels",server:"Backup Server",duration:"0m 30s",itemsTotal:0,itemsSuccess:0,itemsFailed:0,categories:0,tmdbMatches:0,logs:["\u274c Failed to connect to XUI database","\ud83d\udd0d Error: Access denied for user 'admin'@'************'","\u26a0\ufe0f Please check database credentials","\u274c Import aborted"]}],d=u.filter(e=>("all"===l||e.status===l)&&("all"===o||e.type===o)),f=e=>{const n={success:{bg:"success",icon:"\u2705",text:"Success"},processing:{bg:"warning",icon:"\u23f3",text:"Processing"},failed:{bg:"danger",icon:"\u274c",text:"Failed"}}[e];return(0,r.jsxs)(hn,{bg:n.bg,children:[n.icon," ",n.text]})},h=e=>({channels:"\ud83d\udcfa",series:"\ud83d\udcda",vod:"\ud83c\udfac"}[e]||"\ud83d\udcc4");return(0,r.jsxs)("div",{style:{width:"100%",maxWidth:"none"},children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{className:"text-primary",children:"\ud83d\udcdc Import History & Logs"}),(0,r.jsx)(w,{variant:"outline-primary",children:"\ud83d\udcca Export Report"})]}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{md:6,children:(0,r.jsxs)(gt.Group,{children:[(0,r.jsx)(gt.Label,{children:"Filter by Status"}),(0,r.jsxs)(gt.Select,{value:l,onChange:e=>i(e.target.value),children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"success",children:"\u2705 Success"}),(0,r.jsx)("option",{value:"processing",children:"\u23f3 Processing"}),(0,r.jsx)("option",{value:"failed",children:"\u274c Failed"})]})]})}),(0,r.jsx)(Fe,{md:6,children:(0,r.jsxs)(gt.Group,{children:[(0,r.jsx)(gt.Label,{children:"Filter by Type"}),(0,r.jsxs)(gt.Select,{value:o,onChange:e=>c(e.target.value),children:[(0,r.jsx)("option",{value:"all",children:"All Types"}),(0,r.jsx)("option",{value:"channels",children:"\ud83d\udcfa Live Channels"}),(0,r.jsx)("option",{value:"series",children:"\ud83d\udcda TV Series"}),(0,r.jsx)("option",{value:"vod",children:"\ud83c\udfac VOD Movies"})]})]})})]}),(0,r.jsxs)(Me,{className:"mb-4",children:[(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"info",text:"white",className:"shadow-sm",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\ud83d\udcca Total Imports"}),(0,r.jsx)("h3",{children:u.length})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"success",text:"white",className:"shadow-sm",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\u2705 Successful"}),(0,r.jsx)("h3",{children:u.filter(e=>"success"===e.status).length})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"warning",text:"white",className:"shadow-sm",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\u23f3 Processing"}),(0,r.jsx)("h3",{children:u.filter(e=>"processing"===e.status).length})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"danger",text:"white",className:"shadow-sm",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h5",{children:"\u274c Failed"}),(0,r.jsx)("h3",{children:u.filter(e=>"failed"===e.status).length})]})})})]}),(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-dark text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udccb Import Records"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(pn,{striped:!0,hover:!0,responsive:!0,children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"\ud83d\udcc4 File"}),(0,r.jsx)("th",{children:"\ud83d\udcc5 Date"}),(0,r.jsx)("th",{children:"\ud83d\udcca Status"}),(0,r.jsx)("th",{children:"\ud83c\udfaf Type"}),(0,r.jsx)("th",{children:"\ud83d\udda5\ufe0f Server"}),(0,r.jsx)("th",{children:"\u23f1\ufe0f Duration"}),(0,r.jsx)("th",{children:"\ud83d\udcc8 Results"}),(0,r.jsx)("th",{children:"\ud83c\udfaf TMDB Match"}),(0,r.jsx)("th",{children:"\u26a1 Actions"})]})}),(0,r.jsx)("tbody",{children:d.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:e.fileName})}),(0,r.jsx)("td",{children:e.date}),(0,r.jsx)("td",{children:f(e.status)}),(0,r.jsxs)("td",{children:[h(e.type)," ",e.type]}),(0,r.jsx)("td",{children:e.server}),(0,r.jsx)("td",{children:e.duration}),(0,r.jsx)("td",{children:(0,r.jsxs)("small",{children:["\u2705 ",e.itemsSuccess,"/",e.itemsTotal,(0,r.jsx)("br",{}),e.itemsFailed>0&&(0,r.jsxs)("span",{className:"text-danger",children:["\u274c ",e.itemsFailed]})]})}),(0,r.jsx)("td",{children:(0,r.jsxs)("small",{children:["\ud83c\udfaf ",e.tmdbMatches,"/",e.itemsTotal,(0,r.jsx)("br",{}),(0,r.jsxs)("span",{className:"text-muted",children:["(",e.itemsTotal>0?(e.tmdbMatches/e.itemsTotal*100).toFixed(1):0,"%)"]})]})}),(0,r.jsx)("td",{children:(0,r.jsx)(w,{size:"sm",variant:"outline-info",onClick:()=>(s(e),void t(!0)),children:"\ud83d\udccb Details"})})]},e.id))})]})})]}),(0,r.jsxs)(vr,{show:n,onHide:()=>t(!1),size:"lg",children:[(0,r.jsx)(vr.Header,{closeButton:!0,children:(0,r.jsxs)(vr.Title,{children:["\ud83d\udccb Import Details: ",null===a||void 0===a?void 0:a.fileName]})}),(0,r.jsx)(vr.Body,{children:a&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(Me,{className:"mb-3",children:[(0,r.jsxs)(Fe,{md:6,children:[(0,r.jsx)("strong",{children:"\ud83d\udcc5 Date:"})," ",a.date,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"\u23f1\ufe0f Duration:"})," ",a.duration,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"\ud83d\udda5\ufe0f Server:"})," ",a.server]}),(0,r.jsxs)(Fe,{md:6,children:[(0,r.jsx)("strong",{children:"\ud83d\udcca Status:"})," ",f(a.status),(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"\ud83c\udfaf Type:"})," ",h(a.type)," ",a.type,(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"\ud83c\udff7\ufe0f Categories:"})," ",a.categories]})]}),(0,r.jsxs)(Me,{className:"mb-3",children:[(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"primary",text:"white",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h6",{children:"Total Items"}),(0,r.jsx)("h4",{children:a.itemsTotal})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"success",text:"white",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h6",{children:"Successful"}),(0,r.jsx)("h4",{children:a.itemsSuccess})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"danger",text:"white",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h6",{children:"Failed"}),(0,r.jsx)("h4",{children:a.itemsFailed})]})})}),(0,r.jsx)(Fe,{md:3,children:(0,r.jsx)(dn,{bg:"info",text:"white",children:(0,r.jsxs)(dn.Body,{className:"text-center",children:[(0,r.jsx)("h6",{children:"TMDB Matches"}),(0,r.jsx)("h4",{children:a.tmdbMatches})]})})})]}),(0,r.jsx)("h6",{children:"\ud83d\udcdd Import Logs:"}),(0,r.jsx)("div",{className:"bg-dark text-light p-3 rounded",style:{maxHeight:"300px",overflowY:"auto"},children:a.logs.map((e,n)=>(0,r.jsx)("div",{className:"mb-1",children:(0,r.jsxs)("small",{children:["[",a.date.split(" ")[1],"] ",e]})},`log-${n}-${e.substring(0,20)}`))})]})}),(0,r.jsxs)(vr.Footer,{children:[(0,r.jsx)(w,{variant:"secondary",onClick:()=>t(!1),children:"Close"}),(0,r.jsx)(w,{variant:"primary",children:"\ud83d\udce5 Download Full Log"})]})]})]})},Aa=()=>{const[n,t]=(0,e.useState)(!1),[a,s]=(0,e.useState)("general"),[l,i]=(0,e.useState)({userName:"Admin User",email:"<EMAIL>",timezone:"America/New_York",language:"en",tmdbApiKey:"sk-1234567890abcdef",tmdbEnabled:!0,tmdbAutoMatch:!0,tmdbLanguage:"en-US",tmdbPosterQuality:"w500",defaultServer:"main",batchSize:100,autoBackup:!0,duplicateHandling:"skip",categoryMapping:!0,emailNotifications:!0,successNotifications:!0,errorNotifications:!0,webhookUrl:""}),o=(e,n)=>{i(t=>({...t,[e]:n}))};return(0,r.jsxs)("div",{style:{width:"100%",maxWidth:"none"},children:[(0,r.jsxs)("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[(0,r.jsx)("h1",{className:"text-primary",children:"\ud83d\udc64 Profile & Settings"}),(0,r.jsx)(w,{variant:"success",onClick:()=>{t(!0),setTimeout(()=>t(!1),3e3)},children:"\ud83d\udcbe Save All Settings"})]}),n&&(0,r.jsx)(De,{variant:"success",dismissible:!0,onClose:()=>t(!1),children:"\u2705 Settings saved successfully!"}),(0,r.jsxs)(xa,{activeKey:a,onSelect:s,className:"mb-4",children:[(0,r.jsx)(Ea,{eventKey:"general",title:"\ud83d\udc64 General",children:(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-primary text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83c\udff7\ufe0f User Information"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(gt,{children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udc64 Full Name"}),(0,r.jsx)(gt.Control,{type:"text",value:l.userName,onChange:e=>o("userName",e.target.value)})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udce7 Email Address"}),(0,r.jsx)(gt.Control,{type:"email",value:l.email,onChange:e=>o("email",e.target.value)})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83c\udf0d Timezone"}),(0,r.jsxs)(gt.Select,{value:l.timezone,onChange:e=>o("timezone",e.target.value),children:[(0,r.jsx)("option",{value:"America/New_York",children:"\ud83c\uddfa\ud83c\uddf8 Eastern Time (ET)"}),(0,r.jsx)("option",{value:"America/Chicago",children:"\ud83c\uddfa\ud83c\uddf8 Central Time (CT)"}),(0,r.jsx)("option",{value:"America/Denver",children:"\ud83c\uddfa\ud83c\uddf8 Mountain Time (MT)"}),(0,r.jsx)("option",{value:"America/Los_Angeles",children:"\ud83c\uddfa\ud83c\uddf8 Pacific Time (PT)"}),(0,r.jsx)("option",{value:"Europe/London",children:"\ud83c\uddec\ud83c\udde7 London (GMT)"}),(0,r.jsx)("option",{value:"Europe/Madrid",children:"\ud83c\uddea\ud83c\uddf8 Madrid (CET)"}),(0,r.jsx)("option",{value:"Asia/Tokyo",children:"\ud83c\uddef\ud83c\uddf5 Tokyo (JST)"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83c\udf10 Language"}),(0,r.jsxs)(gt.Select,{value:l.language,onChange:e=>o("language",e.target.value),children:[(0,r.jsx)("option",{value:"en",children:"\ud83c\uddfa\ud83c\uddf8 English"}),(0,r.jsx)("option",{value:"es",children:"\ud83c\uddea\ud83c\uddf8 Spanish"}),(0,r.jsx)("option",{value:"fr",children:"\ud83c\uddeb\ud83c\uddf7 French"}),(0,r.jsx)("option",{value:"de",children:"\ud83c\udde9\ud83c\uddea German"}),(0,r.jsx)("option",{value:"pt",children:"\ud83c\udde7\ud83c\uddf7 Portuguese"})]})]})]})})]})}),(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-info text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udcca Usage Statistics"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsxs)(Me,{className:"text-center",children:[(0,r.jsxs)(Fe,{md:6,className:"mb-3",children:[(0,r.jsx)("h3",{className:"text-primary",children:"47"}),(0,r.jsx)("small",{children:"Total Imports"})]}),(0,r.jsxs)(Fe,{md:6,className:"mb-3",children:[(0,r.jsx)("h3",{className:"text-success",children:"45,680"}),(0,r.jsx)("small",{children:"Items Processed"})]}),(0,r.jsxs)(Fe,{md:6,className:"mb-3",children:[(0,r.jsx)("h3",{className:"text-warning",children:"98.2%"}),(0,r.jsx)("small",{children:"Success Rate"})]}),(0,r.jsxs)(Fe,{md:6,className:"mb-3",children:[(0,r.jsx)("h3",{className:"text-info",children:"156h"}),(0,r.jsx)("small",{children:"Total Time Saved"})]})]}),(0,r.jsx)("hr",{}),(0,r.jsx)("h6",{children:"\ud83c\udfc6 Achievements"}),(0,r.jsxs)("div",{className:"d-flex flex-wrap gap-2",children:[(0,r.jsx)("span",{className:"badge bg-success",children:"\ud83e\udd47 First Import"}),(0,r.jsx)("span",{className:"badge bg-primary",children:"\ud83d\udcfa 1K Channels"}),(0,r.jsx)("span",{className:"badge bg-warning",children:"\ud83c\udfac 10K Movies"}),(0,r.jsx)("span",{className:"badge bg-info",children:"\ud83c\udfaf TMDB Master"})]})]})]})})]})}),(0,r.jsx)(Ea,{eventKey:"tmdb",title:"\ud83c\udfac TMDB",children:(0,r.jsxs)(dn,{className:"shadow-sm",children:[(0,r.jsx)(dn.Header,{className:"bg-warning text-dark",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83c\udfaf The Movie Database (TMDB) Settings"})}),(0,r.jsx)(dn.Body,{children:(0,r.jsxs)(Me,{children:[(0,r.jsxs)(Fe,{lg:6,children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udd11 TMDB API Key"}),(0,r.jsx)(gt.Control,{type:"password",value:l.tmdbApiKey,onChange:e=>o("tmdbApiKey",e.target.value),placeholder:"Enter your TMDB API key"}),(0,r.jsxs)(gt.Text,{className:"text-muted",children:["Get your free API key from ",(0,r.jsx)("a",{href:"https://www.themoviedb.org/settings/api",target:"_blank",rel:"noopener noreferrer",children:"TMDB"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83c\udf10 TMDB Language"}),(0,r.jsxs)(gt.Select,{value:l.tmdbLanguage,onChange:e=>o("tmdbLanguage",e.target.value),children:[(0,r.jsx)("option",{value:"en-US",children:"\ud83c\uddfa\ud83c\uddf8 English (US)"}),(0,r.jsx)("option",{value:"es-ES",children:"\ud83c\uddea\ud83c\uddf8 Spanish (Spain)"}),(0,r.jsx)("option",{value:"es-MX",children:"\ud83c\uddf2\ud83c\uddfd Spanish (Mexico)"}),(0,r.jsx)("option",{value:"fr-FR",children:"\ud83c\uddeb\ud83c\uddf7 French"}),(0,r.jsx)("option",{value:"de-DE",children:"\ud83c\udde9\ud83c\uddea German"}),(0,r.jsx)("option",{value:"pt-BR",children:"\ud83c\udde7\ud83c\uddf7 Portuguese (Brazil)"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\uddbc\ufe0f Poster Quality"}),(0,r.jsxs)(gt.Select,{value:l.tmdbPosterQuality,onChange:e=>o("tmdbPosterQuality",e.target.value),children:[(0,r.jsx)("option",{value:"w185",children:"Small (185px) - Faster"}),(0,r.jsx)("option",{value:"w342",children:"Medium (342px) - Balanced"}),(0,r.jsx)("option",{value:"w500",children:"Large (500px) - Recommended"}),(0,r.jsx)("option",{value:"w780",children:"Extra Large (780px) - Best Quality"})]})]})]}),(0,r.jsxs)(Fe,{lg:6,children:[(0,r.jsx)("h6",{children:"\u2699\ufe0f TMDB Features"}),(0,r.jsx)(gt.Check,{type:"switch",id:"tmdb-enabled",label:"\ud83c\udfaf Enable TMDB Integration",checked:l.tmdbEnabled,onChange:e=>o("tmdbEnabled",e.target.checked),className:"mb-3"}),(0,r.jsx)(gt.Check,{type:"switch",id:"tmdb-auto",label:"\ud83e\udd16 Auto-match during import",checked:l.tmdbAutoMatch,onChange:e=>o("tmdbAutoMatch",e.target.checked),className:"mb-3",disabled:!l.tmdbEnabled}),(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("strong",{children:"\ud83c\udfac TMDB Features:"}),(0,r.jsxs)("ul",{className:"mb-0 mt-2",children:[(0,r.jsx)("li",{children:"\ud83d\udcdd Auto-fill movie/series metadata"}),(0,r.jsx)("li",{children:"\ud83d\uddbc\ufe0f Download high-quality posters"}),(0,r.jsx)("li",{children:"\u2b50 Import ratings and reviews"}),(0,r.jsx)("li",{children:"\ud83c\udfad Cast and crew information"}),(0,r.jsx)("li",{children:"\ud83c\udff7\ufe0f Genre and category mapping"})]})]})]})]})})]})}),(0,r.jsx)(Ea,{eventKey:"import",title:"\ud83d\udce5 Import",children:(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-success text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\u2699\ufe0f Import Configuration"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udda5\ufe0f Default XUI Server"}),(0,r.jsxs)(gt.Select,{value:l.defaultServer,onChange:e=>o("defaultServer",e.target.value),children:[(0,r.jsx)("option",{value:"main",children:"\ud83d\udda5\ufe0f Main XUI Server"}),(0,r.jsx)("option",{value:"backup",children:"\u2601\ufe0f Backup Server"}),(0,r.jsx)("option",{value:"local",children:"\ud83c\udfe0 Local Server"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udce6 Batch Size (items per batch)"}),(0,r.jsxs)(gt.Select,{value:l.batchSize,onChange:e=>o("batchSize",parseInt(e.target.value)),children:[(0,r.jsx)("option",{value:"50",children:"50 - Slower but safer"}),(0,r.jsx)("option",{value:"100",children:"100 - Recommended"}),(0,r.jsx)("option",{value:"250",children:"250 - Faster"}),(0,r.jsx)("option",{value:"500",children:"500 - Maximum speed"})]})]}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"\ud83d\udd04 Duplicate Handling"}),(0,r.jsxs)(gt.Select,{value:l.duplicateHandling,onChange:e=>o("duplicateHandling",e.target.value),children:[(0,r.jsx)("option",{value:"skip",children:"Skip duplicates"}),(0,r.jsx)("option",{value:"update",children:"Update existing"}),(0,r.jsx)("option",{value:"append",children:"Create new entry"})]})]}),(0,r.jsx)(gt.Check,{type:"switch",id:"auto-backup",label:"\ud83d\udcbe Auto-backup before import",checked:l.autoBackup,onChange:e=>o("autoBackup",e.target.checked),className:"mb-3"}),(0,r.jsx)(gt.Check,{type:"switch",id:"category-mapping",label:"\ud83c\udff7\ufe0f Smart category mapping",checked:l.categoryMapping,onChange:e=>o("categoryMapping",e.target.checked)})]})]})}),(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-secondary text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udd14 Notifications"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsx)("h6",{children:"\ud83d\udce7 Email Notifications"}),(0,r.jsx)(gt.Check,{type:"switch",id:"email-notifications",label:"\ud83d\udce7 Enable email notifications",checked:l.emailNotifications,onChange:e=>o("emailNotifications",e.target.checked),className:"mb-2"}),(0,r.jsx)(gt.Check,{type:"switch",id:"success-notifications",label:"\u2705 Successful imports",checked:l.successNotifications,onChange:e=>o("successNotifications",e.target.checked),className:"mb-2",disabled:!l.emailNotifications}),(0,r.jsx)(gt.Check,{type:"switch",id:"error-notifications",label:"\u274c Failed imports",checked:l.errorNotifications,onChange:e=>o("errorNotifications",e.target.checked),className:"mb-3",disabled:!l.emailNotifications}),(0,r.jsx)("h6",{children:"\ud83d\udd17 Webhook Integration"}),(0,r.jsxs)(gt.Group,{className:"mb-3",children:[(0,r.jsx)(gt.Label,{children:"Webhook URL (optional)"}),(0,r.jsx)(gt.Control,{type:"url",value:l.webhookUrl,onChange:e=>o("webhookUrl",e.target.value),placeholder:"https://your-webhook-url.com/notify"}),(0,r.jsx)(gt.Text,{className:"text-muted",children:"Receive real-time notifications via webhook"})]}),(0,r.jsxs)(De,{variant:"info",children:[(0,r.jsx)("strong",{children:"\ud83d\udd14 Notification Events:"}),(0,r.jsxs)("ul",{className:"mb-0 mt-2",children:[(0,r.jsx)("li",{children:"Import started/completed"}),(0,r.jsx)("li",{children:"TMDB matching progress"}),(0,r.jsx)("li",{children:"Database connection errors"}),(0,r.jsx)("li",{children:"Daily/weekly reports"})]})]})]})]})})]})}),(0,r.jsx)(Ea,{eventKey:"system",title:"\u2699\ufe0f System",children:(0,r.jsxs)(Me,{children:[(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-dark text-white",children:(0,r.jsx)("h5",{className:"mb-0",children:"\ud83d\udee0\ufe0f System Information"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsx)("table",{className:"table table-sm",children:(0,r.jsxs)("tbody",{children:[(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"\ud83d\udce6 Version:"})}),(0,r.jsx)("td",{children:"RGS XUI Importer v2.1.0"})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"\ud83d\uddd3\ufe0f Build Date:"})}),(0,r.jsx)("td",{children:"2025-07-15"})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"\ud83d\udd27 Node.js:"})}),(0,r.jsx)("td",{children:"v18.17.0"})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"\u269b\ufe0f React:"})}),(0,r.jsx)("td",{children:"v18.2.0"})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"\ud83d\udcbe Database:"})}),(0,r.jsx)("td",{children:"MySQL 8.0.35"})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:(0,r.jsx)("strong",{children:"\ud83c\udf10 API Status:"})}),(0,r.jsx)("td",{children:(0,r.jsx)("span",{className:"badge bg-success",children:"\u2705 Online"})})]})]})}),(0,r.jsx)("hr",{}),(0,r.jsxs)("div",{className:"d-grid gap-2",children:[(0,r.jsx)(w,{variant:"outline-warning",children:"\ud83d\udd04 Check for Updates"}),(0,r.jsx)(w,{variant:"outline-danger",children:"\ud83d\udcbe Export Settings"}),(0,r.jsx)(w,{variant:"outline-info",children:"\ud83d\udccb Download Logs"})]})]})]})}),(0,r.jsx)(Fe,{lg:6,children:(0,r.jsxs)(dn,{className:"shadow-sm h-100",children:[(0,r.jsx)(dn.Header,{className:"bg-warning text-dark",children:(0,r.jsx)("h5",{className:"mb-0",children:"\u26a0\ufe0f Maintenance"})}),(0,r.jsxs)(dn.Body,{children:[(0,r.jsxs)(De,{variant:"warning",children:[(0,r.jsx)("strong",{children:"\u26a0\ufe0f Important:"})," These actions affect your XUI database directly. Always backup before performing maintenance operations."]}),(0,r.jsxs)("div",{className:"d-grid gap-2",children:[(0,r.jsx)(w,{variant:"outline-primary",children:"\ud83e\uddf9 Clean Duplicate Entries"}),(0,r.jsx)(w,{variant:"outline-info",children:"\ud83d\udd04 Rebuild Categories"}),(0,r.jsx)(w,{variant:"outline-success",children:"\ud83c\udfaf Refresh TMDB Data"}),(0,r.jsx)(w,{variant:"outline-secondary",children:"\ud83d\udcca Optimize Database"}),(0,r.jsx)("hr",{}),(0,r.jsx)(w,{variant:"danger",children:"\ud83d\uddd1\ufe0f Reset All Settings"})]}),(0,r.jsxs)(De,{variant:"info",className:"mt-3",children:[(0,r.jsx)("strong",{children:"\ud83d\udca1 Pro Tips:"}),(0,r.jsxs)("ul",{className:"mb-0 mt-2",children:[(0,r.jsx)("li",{children:"Run cleanup monthly for best performance"}),(0,r.jsx)("li",{children:"Backup before major operations"}),(0,r.jsx)("li",{children:"Monitor disk space regularly"}),(0,r.jsx)("li",{children:"Update TMDB data weekly"})]})]})]})]})})]})})]})]})},Da=()=>{const[n,t]=(0,e.useState)(!1),[a,s]=(0,e.useState)([]),[l,i]=(0,e.useState)(!0),o=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";const t=(new Date).toLocaleTimeString(),r={id:Date.now(),timestamp:t,message:e,type:n};s(e=>{const n=[...e,r];return n.length>100?n.slice(-100):n})};(0,e.useEffect)(()=>(window.debugLog=o,o("\ud83d\ude80 XUI Importer Debug Panel initialized","success"),o("\ud83d\udccb Ready to log import operations","info"),()=>{delete window.debugLog}),[]),(0,e.useEffect)(()=>{const e=document.querySelector(".debug-content");e&&(e.scrollTop=e.scrollHeight)},[a]);const c=()=>{i(!l)};return l?(0,r.jsxs)("div",{className:"debug-panel "+(n?"minimized":""),children:[(0,r.jsxs)("div",{className:"debug-header",onClick:()=>{t(!n)},children:[(0,r.jsxs)("span",{children:["\ud83d\udc1b Debug Console (",a.length,")"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("button",{style:{background:"none",border:"none",color:"#888",cursor:"pointer",marginRight:"8px",fontSize:"12px"},onClick:e=>{e.stopPropagation(),s([]),o("\ud83e\uddf9 Debug logs cleared","info")},children:"\ud83e\uddf9"}),(0,r.jsx)("button",{style:{background:"none",border:"none",color:"#888",cursor:"pointer",marginRight:"8px",fontSize:"12px"},onClick:e=>{e.stopPropagation(),c()},children:"\u274c"}),(0,r.jsx)("span",{style:{color:"#888",fontSize:"12px"},children:n?"\u25b2":"\u25bc"})]})]}),!n&&(0,r.jsx)("div",{className:"debug-content",children:a.map(e=>(0,r.jsxs)("div",{className:`debug-log ${e.type}`,children:[(0,r.jsxs)("span",{style:{color:"#888"},children:["[",e.timestamp,"]"]})," ",e.message]},e.id))})]}):(0,r.jsx)("div",{style:{position:"fixed",bottom:"10px",right:"10px",zIndex:1002,background:"#333",color:"white",padding:"8px 12px",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},onClick:c,children:"\ud83d\udc1b Show Debug"})};const Ia=function(){const[n,t]=(0,e.useState)("dashboard");return(0,e.useEffect)(()=>{(()=>{try{console.log("\ud83e\uddf9 Iniciando limpieza completa de configuraciones...");let e=0;["xuiImporterState","xuiConnections","apiConfig","backendUrl","connectionConfig","databaseConnection","serverConfig"].forEach(n=>{const t=localStorage.getItem(n);t&&(t.includes("5555")||t.includes("5000"))&&(console.log(`\ud83e\uddf9 Limpiando configuraci\xf3n antigua: ${n}`),localStorage.removeItem(n),e++)});for(let n=0;n<localStorage.length;n++){const t=localStorage.key(n);if(t){const r=localStorage.getItem(t);r&&(r.includes("5555")||r.includes(":5000"))&&(console.log(`\ud83e\uddf9 Limpiando key con puerto antiguo: ${t}`),localStorage.removeItem(t),e++,n--)}}console.log(`\ud83e\uddf9 Limpieza completada. ${e} configuraciones eliminadas.`),console.log("\ud83d\udd04 Forzando recarga de configuraci\xf3n...")}catch(e){console.warn("\u26a0\ufe0f Error limpiando configuraciones:",e)}})()},[]),(0,e.useEffect)(()=>{window.debugLog&&window.debugLog(`\ud83d\udcc4 Navigation: Switched to ${n}`,"info")},[n]),(0,r.jsx)(Dn,{children:(0,r.jsxs)("div",{className:"d-flex",children:[(0,r.jsx)(a,{setCurrentPage:t,currentPage:n}),(0,r.jsx)("div",{className:"main-content",children:(0,r.jsx)("div",{className:"content-wrapper",children:(()=>{switch(n){case"dashboard":default:return(0,r.jsx)(Mn,{});case"connections":return(0,r.jsx)(Oa,{});case"import-series":return(0,r.jsx)(Sr,{});case"import-vod":return(0,r.jsx)(kr,{});case"history":return(0,r.jsx)(za,{});case"profile":return(0,r.jsx)(Aa,{})}})()})}),(0,r.jsx)(Da,{})]})})},Ma=e=>{e&&e instanceof Function&&t.e(206).then(t.bind(t,206)).then(n=>{let{getCLS:t,getFID:r,getFCP:a,getLCP:s,getTTFB:l}=n;t(e),r(e),a(e),s(e),l(e)})};n.createRoot(document.getElementById("root")).render((0,r.jsx)(e.StrictMode,{children:(0,r.jsx)(Ia,{})})),Ma()})()})();