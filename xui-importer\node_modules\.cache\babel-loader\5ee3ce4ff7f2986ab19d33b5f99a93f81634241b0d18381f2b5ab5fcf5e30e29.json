{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Row,Col,Card,Form,Button,Alert,Table,Badge,Modal}from'react-bootstrap';import{parseM3UForSeries,validateSeriesStructure}from'../utils/seriesLogic';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SeriesImportHandler=_ref=>{let{selectedFile,isImporting,onSeriesDetected}=_ref;const[detectedSeries,setDetectedSeries]=useState([]);const[selectedSeries,setSelectedSeries]=useState(new Set());const[showPreview,setShowPreview]=useState(false);const[previewSeries,setPreviewSeries]=useState(null);const[tmdbMatching,setTmdbMatching]=useState({});const[seriesSettings,setSeriesSettings]=useState({});// Analizar archivo M3U cuando se selecciona\nuseEffect(()=>{if(selectedFile&&selectedFile.type==='application/x-mpegurl'){analyzeM3UFile();}},[selectedFile]);// analyzeM3UFile se define dentro del componente, no necesita estar en dependencias\nconst analyzeM3UFile=async()=>{window.debugLog('info','🔍 Analizando archivo M3U para series...');try{const fileContent=await selectedFile.text();const series=parseM3UForSeries(fileContent);setDetectedSeries(series);// Inicializar configuraciones por defecto\nconst defaultSettings={};series.forEach(s=>{defaultSettings[s.title]={category_id:[],tmdb_id:null,auto_fetch_metadata:true,merge_similar:false};});setSeriesSettings(defaultSettings);window.debugLog('success',`✅ Detectadas ${series.length} series en el archivo`);// Notificar al componente padre\nif(onSeriesDetected){onSeriesDetected(series);}}catch(error){window.debugLog('error',`❌ Error analizando M3U: ${error.message}`);}};const handleSeriesSelection=(seriesTitle,isSelected)=>{const newSelection=new Set(selectedSeries);if(isSelected){newSelection.add(seriesTitle);}else{newSelection.delete(seriesTitle);}setSelectedSeries(newSelection);};const handleSelectAll=()=>{if(selectedSeries.size===detectedSeries.length){setSelectedSeries(new Set());}else{setSelectedSeries(new Set(detectedSeries.map(s=>s.title)));}};const showSeriesPreview=series=>{setPreviewSeries(series);setShowPreview(true);};const updateSeriesSettings=(seriesTitle,setting,value)=>{setSeriesSettings(prev=>({...prev,[seriesTitle]:{...prev[seriesTitle],[setting]:value}}));};const searchTMDB=async seriesTitle=>{window.debugLog('info',`🔍 Buscando \"${seriesTitle}\" en TMDB...`);// Mock de búsqueda TMDB - aquí iría la integración real\nsetTimeout(()=>{setTmdbMatching(prev=>({...prev,[seriesTitle]:{found:true,tmdb_id:Math.floor(Math.random()*100000),title:seriesTitle,overview:`Serie detectada: ${seriesTitle}`,poster_path:'/mock-poster.jpg',first_air_date:'2023-01-01',vote_average:8.5}}));window.debugLog('success',`✅ Metadata encontrada para \"${seriesTitle}\"`);},1000);};// Función validateAndPrepareImport eliminada - no se usaba\nif(!selectedFile||detectedSeries.length===0){return/*#__PURE__*/_jsx(Alert,{variant:\"info\",children:\"\\uD83D\\uDCFA Selecciona un archivo M3U para detectar series autom\\xE1ticamente\"});}return/*#__PURE__*/_jsxs(Card,{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-0\",children:[\"\\uD83D\\uDCFA Series Detectadas (\",detectedSeries.length,\")\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",size:\"sm\",onClick:handleSelectAll,className:\"me-2\",children:selectedSeries.size===detectedSeries.length?'Deseleccionar Todo':'Seleccionar Todo'}),/*#__PURE__*/_jsxs(Badge,{bg:\"info\",children:[selectedSeries.size,\" seleccionadas\"]})]})]}),/*#__PURE__*/_jsx(Card.Body,{style:{maxHeight:'400px',overflowY:'auto'},children:detectedSeries.map((series,index)=>{var _seriesSettings$serie,_seriesSettings$serie2,_tmdbMatching$series$;return/*#__PURE__*/_jsx(Card,{className:\"mb-3 border\",children:/*#__PURE__*/_jsxs(Card.Body,{className:\"py-2\",children:[/*#__PURE__*/_jsxs(Row,{className:\"align-items-center\",children:[/*#__PURE__*/_jsx(Col,{md:1,children:/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",checked:selectedSeries.has(series.title),onChange:e=>handleSeriesSelection(series.title,e.target.checked),disabled:isImporting})}),/*#__PURE__*/_jsxs(Col,{md:4,children:[/*#__PURE__*/_jsx(\"strong\",{children:series.title}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[series.episodes.length,\" episodios\"]})]}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsx(\"div\",{className:\"d-flex flex-wrap gap-1\",children:[...new Set(series.episodes.map(ep=>`S${ep.season_num}`))].map(season=>/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",style:{fontSize:'0.7em'},children:season},season))})}),/*#__PURE__*/_jsx(Col,{md:2,children:tmdbMatching[series.title]?/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:\"\\u2705 TMDB\"}):/*#__PURE__*/_jsx(Button,{variant:\"outline-info\",size:\"sm\",onClick:()=>searchTMDB(series.title),disabled:isImporting,children:\"\\uD83D\\uDD0D TMDB\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-1\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",size:\"sm\",onClick:()=>showSeriesPreview(series),children:\"\\uD83D\\uDC41\\uFE0F Ver\"}),/*#__PURE__*/_jsxs(Form.Select,{size:\"sm\",value:((_seriesSettings$serie=seriesSettings[series.title])===null||_seriesSettings$serie===void 0?void 0:(_seriesSettings$serie2=_seriesSettings$serie.category_id)===null||_seriesSettings$serie2===void 0?void 0:_seriesSettings$serie2[0])||'',onChange:e=>updateSeriesSettings(series.title,'category_id',[parseInt(e.target.value)]),disabled:isImporting,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Categor\\xEDa...\"}),/*#__PURE__*/_jsx(\"option\",{value:\"1\",children:\"Drama Series\"}),/*#__PURE__*/_jsx(\"option\",{value:\"2\",children:\"Comedy Series\"}),/*#__PURE__*/_jsx(\"option\",{value:\"3\",children:\"Action Series\"}),/*#__PURE__*/_jsx(\"option\",{value:\"4\",children:\"Sci-Fi Series\"})]})]})})]}),tmdbMatching[series.title]&&/*#__PURE__*/_jsx(Row,{className:\"mt-2\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Alert,{variant:\"success\",className:\"py-1 mb-0\",children:/*#__PURE__*/_jsxs(\"small\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"TMDB:\"}),\" \",tmdbMatching[series.title].title,\"(\",(_tmdbMatching$series$=tmdbMatching[series.title].first_air_date)===null||_tmdbMatching$series$===void 0?void 0:_tmdbMatching$series$.split('-')[0],\") - \\u2B50 \",tmdbMatching[series.title].vote_average]})})})})]})},`series-${series.title}-${index}`);})}),selectedSeries.size>0&&/*#__PURE__*/_jsxs(Card.Footer,{children:[/*#__PURE__*/_jsxs(Alert,{variant:\"success\",className:\"mb-2\",children:[\"\\u2705 \",selectedSeries.size,\" series seleccionadas para importar\"]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-2\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"\\u2699\\uFE0F Configuraci\\xF3n Global para Series Seleccionadas\"}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83D\\uDD0D Auto-buscar metadata en TMDB\",defaultChecked:true})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83D\\uDD17 Merge episodios similares\",defaultChecked:false})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"\\uD83D\\uDCDD Generar descripciones autom\\xE1ticas\",defaultChecked:true})})]})]})]}),/*#__PURE__*/_jsxs(Modal,{show:showPreview,onHide:()=>setShowPreview(false),size:\"lg\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsxs(Modal.Title,{children:[\"\\uD83D\\uDCFA Preview: \",previewSeries===null||previewSeries===void 0?void 0:previewSeries.title]})}),/*#__PURE__*/_jsx(Modal.Body,{children:previewSeries&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total de Episodios:\"}),\" \",previewSeries.episodes.length,/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"Temporadas:\"}),\" \",[...new Set(previewSeries.episodes.map(ep=>ep.season_num))].join(', ')]})}),/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:'300px',overflowY:'auto'},children:/*#__PURE__*/_jsxs(Table,{striped:true,size:\"sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Episodio\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Nombre\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Duraci\\xF3n\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:previewSeries.episodes.sort((a,b)=>a.season_num-b.season_num||a.episode_num-b.episode_num).map((episode,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",children:[\"S\",String(episode.season_num).padStart(2,'0'),\"E\",String(episode.episode_num).padStart(2,'0')]})}),/*#__PURE__*/_jsx(\"td\",{children:episode.episode_title||'Sin título'}),/*#__PURE__*/_jsx(\"td\",{children:episode.duration})]},`episode-S${episode.season_num}E${episode.episode_num}-${index}`))})]})})]})}),/*#__PURE__*/_jsx(Modal.Footer,{children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>setShowPreview(false),children:\"Cerrar\"})})]})]});};export default SeriesImportHandler;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}