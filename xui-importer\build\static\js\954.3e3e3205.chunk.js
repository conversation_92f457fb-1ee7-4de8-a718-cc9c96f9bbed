(()=>{const e=(()=>{const e="http://localhost:5001/api";return console.log("\ud83d\udd27 Worker API URL FORZADA:",e),e})();console.log("\ud83d\udd27 Worker inicializado con API_BASE_URL:",e);const s=async function(s){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a=`${e}${s}`,r={...{headers:{"Content-Type":"application/json",...t.headers}},...t};try{const e=await fetch(a,r),s=await e.json();if(!e.ok)throw new Error(s.message||`HTTP ${e.status}`);return s}catch(o){throw o}},t=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;try{return await s(`/database/streams?page=${e}&limit=${t}`)}catch(a){throw new Error(`<PERSON>rror leyendo streams p\xe1gina ${e}: ${a.message}`)}},a=async()=>{try{var e;self.postMessage({type:"clear_data",data:{}}),self.postMessage({type:"progress",data:{step:"starting",message:"\ud83d\ude80 Iniciando lectura de base de datos...",progress:0}}),self.postMessage({type:"progress",data:{step:"optimizing",message:"\ud83d\ude80 Optimizando \xedndices de base de datos...",progress:5}});const n=await(async()=>{try{return await s("/database/optimize-indexes",{method:"POST"})}catch(e){return console.warn("No se pudieron optimizar los \xedndices:",e.message),{success:!1,error:e.message}}})();var a;if(n.success)self.postMessage({type:"progress",data:{step:"optimized",message:`\u2705 \xcdndices optimizados: ${(null===(a=n.data)||void 0===a?void 0:a.created_successfully)||0} creados`,progress:10}});else self.postMessage({type:"progress",data:{step:"optimization_skipped",message:"\u26a0\ufe0f Optimizaci\xf3n de \xedndices omitida, continuando...",progress:10}});self.postMessage({type:"progress",data:{step:"stream_types",message:"\ud83c\udfef Paso 1: Leyendo tipos de stream...",progress:15}});const i=(null===(e=(await(async()=>{try{return await s("/database/stream-types")}catch(e){throw new Error(`Error leyendo tipos de stream: ${e.message}`)}})()).data)||void 0===e?void 0:e.types)||[],p={};i.forEach(e=>{p[e.type_id]={name:e.type_name,key:e.type_key,count:0}}),self.postMessage({type:"progress",data:{step:"stream_types_complete",message:`\u2705 Encontrados ${i.length} tipos de stream`,progress:25,result:{streamTypes:i,typeMap:p}}}),self.postMessage({type:"progress",data:{step:"streams",message:"\ud83d\udfe2 Paso 2: Leyendo streams...",progress:30}});let d=[],c=1,l=!0;const g=1e3;for(;l;){var r;const e=(null===(r=(await t(c,g)).data)||void 0===r?void 0:r.streams)||[];if(0===e.length)l=!1;else{d=d.concat(e);const s=Math.min(30+5*c,75);self.postMessage({type:"progress",data:{step:"streams_page",message:`\ud83d\udcc4 P\xe1gina ${c}: ${e.length} streams le\xeddos (Total: ${d.length})`,progress:s}}),c++,c>50&&(l=!1)}}self.postMessage({type:"progress",data:{step:"streams_complete",message:`\u2705 Total de ${d.length} streams le\xeddos`,progress:80}});const m={live:{count:0,samples:[]},movies:{count:0,samples:[]},series:{count:0,samples:[]},other:{count:0,samples:[]}};for(const e of d){const s=e.type;p[s]&&p[s].count++,1===s?(m.live.count++,m.live.samples.length<10&&m.live.samples.push({id:e.id,name:e.stream_display_name,has_epg:!!e.epg_id,category_id:e.category_id})):2===s?(m.movies.count++,m.movies.samples.length<10&&m.movies.samples.push({id:e.id,name:e.stream_display_name,tmdb_id:e.tmdb_id,has_source:!!e.stream_source,category_id:e.category_id})):5===s?(m.series.count++,m.series.samples.length<10&&m.series.samples.push({id:e.id,name:e.stream_display_name,series_no:e.series_no,category_id:e.category_id})):m.other.count++}let y={totalSeries:0,totalEpisodes:0,sampleSeries:[]};if(m.series.count>0){self.postMessage({type:"progress",data:{step:"series_structure",message:"\ud83d\udd34 Analizando estructura de series...",progress:90}});try{const e=await(async()=>{try{return await s("/database/series-structure-analysis")}catch(e){throw new Error(`Error leyendo estructura de series: ${e.message}`)}})();e.success&&(y={...y,...e.data})}catch(o){console.warn("Error analizando estructura de series:",o.message)}}const u={streamTypes:p,contentAnalysis:m,seriesStructure:y,totalStreams:d.length,readAt:(new Date).toISOString()};return self.postMessage({type:"complete",data:{step:"complete",message:"\ud83c\udf89 Lectura completa de base de datos finalizada",progress:100,result:u}}),u}catch(o){throw self.postMessage({type:"error",data:{step:"error",message:`\u274c Error durante la lectura: ${o.message}`,error:o.message}}),o}};self.addEventListener("message",async e=>{const{type:s}=e.data;if("READ_ALL_DATA"===s)try{await a()}catch(t){self.postMessage({type:"error",data:{error:t.message}})}else self.postMessage({type:"error",data:{error:`Tipo de mensaje desconocido: ${s}`}})})})();