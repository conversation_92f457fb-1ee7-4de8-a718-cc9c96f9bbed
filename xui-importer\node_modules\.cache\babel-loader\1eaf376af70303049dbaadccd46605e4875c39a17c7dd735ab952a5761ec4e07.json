{"ast": null, "code": "import'./App.css';import Sidebar from'./components/Sidebar';import Dashboard from'./components/Dashboard';import ImportM3U from'./components/ImportM3U';import ImportVODM3U from'./components/ImportVODM3U';import Connections from'./components/Connections';import History from'./components/History';import Profile from'./components/Profile';import DebugPanel from'./components/DebugPanel';import{AppProvider}from'./context/AppContext';import{clearOldConfigurations}from'./config/apiConfig';import{useState,useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[currentPage,setCurrentPage]=useState('dashboard');// Limpiar configuraciones antiguas al cargar\nuseEffect(()=>{clearOldConfigurations();},[]);// Log de cambios de página\nuseEffect(()=>{if(window.debugLog){window.debugLog(`📄 Navigation: Switched to ${currentPage}`,'info');}},[currentPage]);const renderPage=()=>{switch(currentPage){case'dashboard':return/*#__PURE__*/_jsx(Dashboard,{});case'connections':return/*#__PURE__*/_jsx(Connections,{});case'import-series':return/*#__PURE__*/_jsx(ImportM3U,{});case'import-vod':return/*#__PURE__*/_jsx(ImportVODM3U,{});case'history':return/*#__PURE__*/_jsx(History,{});case'profile':return/*#__PURE__*/_jsx(Profile,{});default:return/*#__PURE__*/_jsx(Dashboard,{});}};return/*#__PURE__*/_jsx(AppProvider,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex\",children:[/*#__PURE__*/_jsx(Sidebar,{setCurrentPage:setCurrentPage,currentPage:currentPage}),/*#__PURE__*/_jsx(\"div\",{className:\"main-content\",children:/*#__PURE__*/_jsx(\"div\",{className:\"content-wrapper\",children:renderPage()})}),/*#__PURE__*/_jsx(DebugPanel,{})]})});}export default App;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}